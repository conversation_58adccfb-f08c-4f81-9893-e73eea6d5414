import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/playlist_category_model.dart';

/// 歌单API服务
class PlaylistApiService {
  static final PlaylistApiService _instance = PlaylistApiService._internal();
  factory PlaylistApiService() => _instance;
  PlaylistApiService._internal();

  late final Dio _dio;

  void initialize() {
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'HiMusic/1.0.0',
      },
    ));

    // 添加日志拦截器（仅在调试模式下）
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        request: true,
        error: true,
        requestHeader: false,
        responseBody: false,
      ));
    }
  }

  /// 获取歌单分类数据
  Future<PlaylistCategoryResponse> getPlaylistCategories() async {
    try {
      if (kDebugMode) {
        print('PlaylistApiService: Fetching playlist categories...');
      }

      const url = 'https://api.xiaodaokg.com/kw/kuwo_fast.php';
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        final data = response.data;
        
        if (kDebugMode) {
          print('PlaylistApiService: Successfully fetched playlist categories');
        }

        return PlaylistCategoryResponse.fromJson(data);
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.statusMessage}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('PlaylistApiService: Error fetching playlist categories: $e');
      }
      
      // 返回空结果而不是抛出异常，保持应用稳定
      return PlaylistCategoryResponse(
        code: 500,
        success: false,
        message: 'Failed to fetch data',
        data: PlaylistCategoryData(
          total: 0,
          categories: {},
        ),
      );
    }
  }
}
