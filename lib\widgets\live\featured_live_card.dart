import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../data/models/live_model.dart';

class FeaturedLiveCard extends StatelessWidget {
  final Live live;
  final VoidCallback? onTap;
  
  const FeaturedLiveCard({
    super.key,
    required this.live,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 200,
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: AppColors.cardBackground,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          children: [
            // 背景图片
            Positioned.fill(
              child: CachedNetworkImage(
                imageUrl: live.coverUrl ?? 'https://picsum.photos/800/400',
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.cardBackground,
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.cardBackground,
                  child: const Center(
                    child: Icon(Icons.broken_image, size: 50),
                  ),
                ),
              ),
            ),
            
            // 渐变遮罩
            Positioned.fill(
              child: DecoratedBox(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
              ),
            ),
            
            // 状态标签
            Positioned(
              top: 16,
              left: 16,
              child: _buildStatusBadge(),
            ),
            
            // 观看人数
            if (live.status == LiveStatus.live)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.visibility,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        live.formattedViewerCount,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            // 直播信息
            Positioned(
              left: 16,
              right: 16,
              bottom: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    live.title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 14,
                        backgroundImage: live.hostAvatar != null
                            ? CachedNetworkImageProvider(live.hostAvatar!)
                            : null,
                        backgroundColor: AppColors.cardBackground,
                        child: live.hostAvatar == null
                            ? const Icon(Icons.person, size: 16)
                            : null,
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            live.hostName,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (live.status == LiveStatus.upcoming)
                            Text(
                              _formatUpcomingTime(live.scheduledStartTime),
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.primaryColor,
                              ),
                            ),
                        ],
                      ),
                      const Spacer(),
                      _buildActionButton(),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStatusBadge() {
    Color badgeColor;
    String badgeText;
    
    switch (live.status) {
      case LiveStatus.live:
        badgeColor = Colors.red;
        badgeText = 'LIVE';
        break;
      case LiveStatus.upcoming:
        badgeColor = AppColors.primaryColor;
        badgeText = 'UPCOMING';
        break;
      case LiveStatus.ended:
        badgeColor = AppColors.grey1;
        badgeText = 'ENDED';
        break;
      case LiveStatus.canceled:
        badgeColor = AppColors.error;
        badgeText = 'CANCELED';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        badgeText,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  Widget _buildActionButton() {
    if (live.status == LiveStatus.live) {
      return ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: const Text('Watch Now'),
      );
    } else if (live.status == LiveStatus.upcoming) {
      return ElevatedButton.icon(
        onPressed: onTap,
        icon: Icon(
          live.isReminded ? Icons.notifications_active : Icons.notifications_none,
          size: 16,
        ),
        label: Text(
          live.isReminded ? 'Reminded' : 'Remind Me',
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: AppColors.grey3,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }
  
  String _formatUpcomingTime(DateTime scheduledTime) {
    final now = DateTime.now();
    final difference = scheduledTime.difference(now);
    
    if (difference.inDays > 0) {
      return 'Starts in ${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return 'Starts in ${difference.inHours} hours';
    } else if (difference.inMinutes > 0) {
      return 'Starts in ${difference.inMinutes} minutes';
    } else {
      return 'Starting soon';
    }
  }
} 