import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../core/theme/app_colors.dart';
import '../core/theme/app_text_styles.dart';
import '../data/providers/mock_data_provider.dart';
import '../data/models/models.dart';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/common/custom_app_bar.dart';
import 'search_page.dart';
import 'player_page.dart';
import 'playlist_detail_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late MockDataProvider _dataProvider;
  late List<Playlist> _recommendedPlaylists;
  late List<Playlist> _dailyRecommendations;
  late List<Song> _popularSongs;
  
  final _searchController = TextEditingController();
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _dataProvider = Provider.of<MockDataProvider>(context, listen: false);
    _loadData();
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  void _loadData() {
    _recommendedPlaylists = _dataProvider.getRecommendedPlaylists();
    _dailyRecommendations = _dataProvider.getDailyRecommendedPlaylists();
    _popularSongs = _dataProvider.getRandomSongs(10);
  }
  
  @override
  Widget build(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // 自定义应用栏
          Container(
            padding: EdgeInsets.only(
              top: statusBarHeight,
              left: 16,
              right: 16,
              bottom: 8,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'HiMusic',
                  style: AppTextStyles.headingMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(
                        Icons.search,
                        color: AppColors.textPrimary,
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SearchPage(),
                          ),
                        );
                      },
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.notifications_none,
                        color: AppColors.textPrimary,
                      ),
                      onPressed: () {
                        // TODO: 导航到通知页面
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // 主内容
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Banner轮播图
                  BannerSection(playlists: _recommendedPlaylists.take(3).toList()),
                  
                  // 分类标签
                  CategoryTabs(
                    onCategorySelected: (category) {
                      // TODO: 处理分类选择
                    },
                  ),
                  
                  // 推荐歌单
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: SectionHeader(
                      title: 'Recommended For You',
                      onMoreTap: () {
                        // TODO: 查看更多推荐歌单
                      },
                    ),
                  ),
                  SizedBox(
                    height: 220,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      itemCount: _recommendedPlaylists.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 12,
                          ),
                          child: PlaylistCard(
                            playlist: _recommendedPlaylists[index],
                            onTap: () {
                              // 导航到歌单详情页
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PlaylistDetailPage(
                                    playlist: _recommendedPlaylists[index],
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                  
                  // 每日推荐
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: SectionHeader(
                      title: 'Daily Recommendations',
                      onMoreTap: () {
                        // TODO: 查看更多每日推荐
                      },
                    ),
                  ),
                  SizedBox(
                    height: 220,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      itemCount: _dailyRecommendations.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 12,
                          ),
                          child: PlaylistCard(
                            playlist: _dailyRecommendations[index],
                            onTap: () {
                              // 导航到歌单详情页
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PlaylistDetailPage(
                                    playlist: _dailyRecommendations[index],
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                  
                  // 热门歌曲
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: SectionHeader(
                      title: 'Popular Songs',
                      onMoreTap: () {
                        // TODO: 查看更多热门歌曲
                      },
                    ),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _popularSongs.length > 5 ? 5 : _popularSongs.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: SongItem(
                          song: _popularSongs[index],
                          onTap: () {
                            // 播放歌曲
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PlayerPage(
                                  song: _popularSongs[index],
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                  
                  if (_popularSongs.length > 5)
                    Padding(
                      padding: const EdgeInsets.only(top: 8, bottom: 24, left: 16, right: 16),
                      child: OutlinedButton(
                        onPressed: () {
                          // TODO: 导航到完整歌曲列表页面
                        },
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: AppColors.primaryColor),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          minimumSize: const Size(double.infinity, 44),
                        ),
                        child: Text(
                          'View All Songs',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ),
                    )
                  else
                    const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 