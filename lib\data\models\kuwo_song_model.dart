import 'package:equatable/equatable.dart';

class KuwoSearchResponse extends Equatable {
  final String hitMode;
  final int hit;
  final int hitButOffline;
  final int mshow;
  final int isNew;
  final int pn;
  final int rn;
  final int show;
  final int total;
  final String uk;
  final List<KuwoSong> songList;

  const KuwoSearchResponse({
    this.hitMode = 'song',
    this.hit = 0,
    this.hitButOffline = 0,
    this.mshow = 0,
    this.isNew = 0,
    required this.pn,
    required this.rn,
    this.show = 0,
    required this.total,
    this.uk = '',
    required this.songList,
  });

  factory KuwoSearchResponse.fromJson(Map<String, dynamic> json) {
    return KuwoSearchResponse(
      hitMode: json['HITMODE'] as String? ?? '',
      hit: int.parse(json['HIT'] as String? ?? '0'),
      hitButOffline: int.parse(json['HIT_BUT_OFFLINE'] as String? ?? '0'),
      mshow: int.parse(json['MSHOW'] as String? ?? '0'),
      isNew: int.parse(json['NEW'] as String? ?? '0'),
      pn: int.parse(json['PN'] as String? ?? '0'),
      rn: int.parse(json['RN'] as String? ?? '0'),
      show: int.parse(json['SHOW'] as String? ?? '0'),
      total: int.parse(json['TOTAL'] as String? ?? '0'),
      uk: json['UK'] as String? ?? '',
      songList: (json['abslist'] as List<dynamic>?)
              ?.map((item) => KuwoSong.fromJson(item as Map<String, dynamic>))
              .toList() ?? [],
    );
  }

  @override
  List<Object?> get props => [
        hitMode,
        hit,
        hitButOffline,
        mshow,
        isNew,
        pn,
        rn,
        show,
        total,
        uk,
        songList,
      ];
}

class KuwoSong extends Equatable {
  final String songId;       // MUSICRID 例如: MUSIC_324244
  final String songName;     // SONGNAME
  final String artistId;     // ARTISTID
  final String artistName;   // ARTIST
  final String albumId;      // ALBUMID
  final String albumName;    // ALBUM
  final int duration;        // DURATION 单位：秒
  final String? coverUrl;    // 可以从 web_albumpic_short 获取
  final String? mvId;        // MKVRID 例如: MV_2077744
  final int playCount;       // PLAYCNT
  final int score;           // SCORE100
  final String formats;      // FORMATS
  final bool hasLyrics;      // HASECHO
  final bool isOriginal;     // originalsongtype
  final bool isPay;          // 根据 PAY 判断

  const KuwoSong({
    required this.songId,
    required this.songName,
    required this.artistId,
    required this.artistName,
    required this.albumId,
    required this.albumName,
    required this.duration,
    this.coverUrl,
    this.mvId,
    required this.playCount,
    required this.score,
    required this.formats,
    required this.hasLyrics,
    required this.isOriginal,
    required this.isPay,
  });

  factory KuwoSong.fromJson(Map<String, dynamic> json) {
    // 提取原始MUSICRID中的ID部分
    final musicRid = json['MUSICRID'] as String? ?? '';
    final songIdMatch = RegExp(r'MUSIC_(\d+)').firstMatch(musicRid);
    final songId = songIdMatch != null ? songIdMatch.group(1) ?? '' : '';

    // 提取原始MKVRID中的ID部分
    final mkvRid = json['MKVRID'] as String? ?? '';
    final mvIdMatch = RegExp(r'MV_(\d+)').firstMatch(mkvRid);
    final mvId = mvIdMatch != null ? mvIdMatch.group(1) : null;

    // 处理歌曲名称，去除HTML实体编码
    String songName = json['SONGNAME'] as String? ?? '';
    songName = songName.replaceAll('&nbsp;', ' ').trim();

    // 专辑封面URL
    String? coverUrl;
    if (json['web_albumpic_short'] != null && json['web_albumpic_short'].toString().isNotEmpty) {
      coverUrl = 'https://img4.kuwo.cn/star/albumcover/${json['web_albumpic_short']}';
    } else if (json['hts_MVPIC'] != null && json['hts_MVPIC'].toString().isNotEmpty) {
      coverUrl = json['hts_MVPIC'] as String;
    }

    return KuwoSong(
      songId: songId,
      songName: songName,
      artistId: json['ARTISTID'] as String? ?? '',
      artistName: json['ARTIST'] as String? ?? '',
      albumId: json['ALBUMID'] as String? ?? '',
      albumName: json['ALBUM'] as String? ?? '',
      duration: int.parse(json['DURATION'] as String? ?? '0'),
      coverUrl: coverUrl,
      mvId: mvId,
      playCount: int.parse(json['PLAYCNT'] as String? ?? '0'),
      score: int.parse(json['SCORE100'] as String? ?? '0'),
      formats: json['FORMATS'] as String? ?? '',
      hasLyrics: (json['HASECHO'] as String? ?? '0') == '1',
      isOriginal: (json['originalsongtype'] as String? ?? '0') == '1',
      isPay: int.parse(json['PAY'] as String? ?? '0') > 0,
    );
  }

  // 转换为应用内统一的Song模型
  Map<String, dynamic> toSongJson() {
    return {
      'id': songId,
      'title': songName,
      'artist_id': artistId,
      'artist_name': artistName,
      'album_id': albumId,
      'album_name': albumName,
      'cover_url': coverUrl,
      'audio_url': null, // 音频URL需要通过另一个API获取
      'duration': duration,
      'release_date': DateTime.now().toIso8601String(), // 酷我API没有直接提供发布日期
      'play_count': playCount,
      'like_count': 0, // 酷我API没有直接提供点赞数
      'comment_count': 0, // 酷我API没有直接提供评论数
      'lyrics': null, // 歌词需要通过另一个API获取
      'tags': <String>[],
      'genres': <String>[],
      'is_liked': false,
      'quality': formats.contains('ALFLAC') ? 2 : (formats.contains('MP3H') ? 1 : 0),
      'is_featured': score > 80,
      'is_explicit': false,
    };
  }

  @override
  List<Object?> get props => [
        songId,
        songName,
        artistId,
        artistName,
        albumId,
        albumName,
        duration,
        coverUrl,
        mvId,
        playCount,
        score,
        formats,
        hasLyrics,
        isOriginal,
        isPay,
      ];
} 