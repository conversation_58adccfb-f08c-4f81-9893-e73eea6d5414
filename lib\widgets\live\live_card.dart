import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../data/models/live_model.dart';

class LiveCard extends StatelessWidget {
  final Live live;
  final VoidCallback? onTap;
  final bool isHorizontal;
  
  const LiveCard({
    super.key,
    required this.live,
    this.onTap,
    this.isHorizontal = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        clipBehavior: Clip.antiAlias,
        child: isHorizontal
            ? _buildHorizontalLayout()
            : _buildVerticalLayout(),
      ),
    );
  }
  
  Widget _buildVerticalLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 封面图片
        Stack(
          children: [
            AspectRatio(
              aspectRatio: 16 / 9,
              child: CachedNetworkImage(
                imageUrl: live.coverUrl ?? 'https://picsum.photos/300/200',
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.cardBackground,
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.cardBackground,
                  child: const Icon(Icons.live_tv, size: 40),
                ),
              ),
            ),
            
            // 状态标签
            Positioned(
              top: 8,
              left: 8,
              child: _buildStatusBadge(),
            ),
            
            // 观看人数
            if (live.status == LiveStatus.live)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.visibility,
                        color: Colors.white,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        live.formattedViewerCount,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        
        // 直播信息
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                live.title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  CircleAvatar(
                    radius: 10,
                    backgroundImage: live.hostAvatar != null
                        ? CachedNetworkImageProvider(live.hostAvatar!)
                        : null,
                    backgroundColor: AppColors.cardBackground,
                    child: live.hostAvatar == null
                        ? const Icon(Icons.person, size: 12)
                        : null,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      live.hostName,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildHorizontalLayout() {
    return Row(
      children: [
        // 封面图片
        SizedBox(
          width: 120,
          height: 70,
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                imageUrl: live.coverUrl ?? 'https://picsum.photos/300/200',
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.cardBackground,
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.cardBackground,
                  child: const Icon(Icons.live_tv, size: 24),
                ),
              ),
              
              // 状态标签
              Positioned(
                top: 4,
                left: 4,
                child: _buildStatusBadge(small: true),
              ),
            ],
          ),
        ),
        
        // 直播信息
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  live.title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    CircleAvatar(
                      radius: 8,
                      backgroundImage: live.hostAvatar != null
                          ? CachedNetworkImageProvider(live.hostAvatar!)
                          : null,
                      backgroundColor: AppColors.cardBackground,
                      child: live.hostAvatar == null
                          ? const Icon(Icons.person, size: 8)
                          : null,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        live.hostName,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                if (live.status == LiveStatus.upcoming)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      _formatUpcomingTime(live.scheduledStartTime),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
        
        // 操作按钮
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: live.status == LiveStatus.upcoming
              ? _buildRemindButton()
              : _buildViewerCount(),
        ),
      ],
    );
  }
  
  Widget _buildStatusBadge({bool small = false}) {
    Color badgeColor;
    String badgeText;
    
    switch (live.status) {
      case LiveStatus.live:
        badgeColor = Colors.red;
        badgeText = 'LIVE';
        break;
      case LiveStatus.upcoming:
        badgeColor = AppColors.primaryColor;
        badgeText = 'UPCOMING';
        break;
      case LiveStatus.ended:
        badgeColor = AppColors.grey1;
        badgeText = 'ENDED';
        break;
      case LiveStatus.canceled:
        badgeColor = AppColors.error;
        badgeText = 'CANCELED';
        break;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: small ? 4 : 8,
        vertical: small ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        badgeText,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontSize: small ? 8 : 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  Widget _buildRemindButton() {
    return IconButton(
      onPressed: onTap,
      icon: Icon(
        live.isReminded ? Icons.notifications_active : Icons.notifications_none,
        color: live.isReminded ? AppColors.primaryColor : AppColors.textSecondary,
        size: 20,
      ),
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(),
    );
  }
  
  Widget _buildViewerCount() {
    return Row(
      children: [
        const Icon(
          Icons.visibility,
          color: AppColors.textSecondary,
          size: 16,
        ),
        const SizedBox(width: 4),
        Text(
          live.formattedViewerCount,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
  
  String _formatUpcomingTime(DateTime scheduledTime) {
    final now = DateTime.now();
    final difference = scheduledTime.difference(now);
    
    if (difference.inDays > 0) {
      return 'In ${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return 'In ${difference.inHours} hours';
    } else if (difference.inMinutes > 0) {
      return 'In ${difference.inMinutes} minutes';
    } else {
      return 'Starting soon';
    }
  }
} 