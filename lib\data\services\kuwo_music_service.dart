import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/kuwo_song_model.dart';
import '../models/song_model.dart';
import 'unified_cache_manager.dart';

class KuwoMusicService {
  final Dio _dio;
  final UnifiedCacheManager _cacheManager = UnifiedCacheManager();

  // 基础URL
  static const String _baseUrl = 'http://search.kuwo.cn/r.s';

  // 单例模式
  static final KuwoMusicService _instance = KuwoMusicService._internal();

  factory KuwoMusicService() {
    return _instance;
  }

  KuwoMusicService._internal() : _dio = Dio() {
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
    
    // 移除详细的HTTP日志，减少控制台噪音
    // 只在需要调试网络问题时才启用
    if (kDebugMode && false) { // 设置为false禁用详细日志
      _dio.interceptors.add(LogInterceptor(
        request: true,
        requestHeader: false, // 减少头部信息
        requestBody: false,   // 减少请求体信息
        responseHeader: false, // 减少响应头信息
        responseBody: false,   // 减少响应体信息
        error: true,          // 保留错误信息
      ));
    }
  }
  
  /// 搜索歌曲
  /// [keyword] 搜索关键词，可以是歌曲名、歌手名或专辑名
  /// [page] 页码，从0开始
  /// [pageSize] 每页返回的结果数量
  Future<KuwoSearchResponse> searchSongs(String keyword, {int page = 0, int pageSize = 10}) async {
    try {
      final response = await _dio.get(
        _baseUrl,
        queryParameters: {
          'all': keyword,
          'ft': 'music',
          'newsearch': 1,
          'alflac': 1,
          'itemset': 'web_2013',
          'client': 'kt',
          'cluster': 0,
          'pn': page,
          'rn': pageSize,
          'vermerge': 1,
          'rformat': 'json',
          'encoding': 'utf8',
          'show_copyright_off': 1,
          'pcmp4': 1,
          'ver': 'mbox',
          'vipver': 'MUSIC_8.7.6.0.BCS31',
          'plat': 'pc',
          'devid': 0,
        },
      );
      
      if (response.statusCode == 200) {
        // 处理API返回的数据，酷我API有时会返回类似JSON但不完全符合标准的数据
        // 尝试将单引号替换为双引号，并处理其他可能的JSON格式问题
        String responseData = response.data.toString();
        responseData = responseData.replaceAll("'", '"');
        
        try {
          final Map<String, dynamic> data = json.decode(responseData);
          return KuwoSearchResponse.fromJson(data);
        } catch (jsonError) {
          if (kDebugMode) {
            print('JSON解析错误: $jsonError');
            print('原始响应: $responseData');
          }
          
          // 如果正常解析失败，返回一个空结果
          return KuwoSearchResponse(
            total: 0,
            pn: 0,
            rn: 0,
            songList: [],
          );
        }
      } else {
        throw Exception('Failed to load search results: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error searching songs: $e');
      }
      
      // 返回空结果而不是抛出异常，让应用能继续运行
      return KuwoSearchResponse(
        total: 0,
        pn: 0,
        rn: 0,
        songList: [],
      );
    }
  }
  
  /// 将酷我音乐API的搜索结果转换为应用内统一的Song模型
  List<Song> convertToSongs(List<KuwoSong> kuwoSongs) {
    return kuwoSongs.map((kuwoSong) {
      final songJson = kuwoSong.toSongJson();
      return Song.fromJson(songJson);
    }).toList();
  }
  
  /// 获取歌词（带缓存）
  /// [musicId] 歌曲ID，例如：324244
  Future<String?> getLyrics(String musicId) async {
    return await _cacheManager.getLyrics(musicId, () async {
      try {
        // 获取歌词

        final response = await _dio.get(
          'http://m.kuwo.cn/newh5/singles/songinfoandlrc',
          queryParameters: {
            'musicId': musicId,
            'httpsStatus': 1,
          },
        );

        if (response.statusCode == 200) {
          final Map<String, dynamic> data = json.decode(response.data.toString());
          if (data['status'] == 200 && data['data'] != null) {
            final lrcList = data['data']['lrclist'] as List<dynamic>?;
            if (lrcList != null && lrcList.isNotEmpty) {
              // 合并所有歌词行
              final lyrics = lrcList.map<String>((line) =>
                '[${line['time']}] ${line['lineLyric']}'
              ).join('\n');

              // 歌词获取成功
              return lyrics;
            }
          }
        }

        if (kDebugMode) {
          print('KuwoMusicService: No lyrics found for: $musicId');
        }
        return null;
      } catch (e) {
        if (kDebugMode) {
          print('KuwoMusicService: Error getting lyrics for $musicId: $e');
        }
        return null;
      }
    });
  }
  
  /// 获取歌曲播放文件（带缓存和下载）
  /// [musicId] 歌曲ID，例如：324244
  /// [quality] 音质类型，默认为标准音质(mq)
  Future<String?> getSongPlayUrl(String musicId, {String quality = 'mq'}) async {
    return await _cacheManager.getMusicFile(musicId, quality, () async {
      try {
        if (kDebugMode) {
          print('KuwoMusicService: Fetching song URL from API for: $musicId ($quality)');
        }

        // 构建API URL，支持不同音质
        String apiUrl = 'http://api.xiaodaokg.com/kuwo.php?ID=$musicId';

        // 根据音质添加参数
        switch (quality) {
          case 'hq':
            apiUrl += '&type=hq';
            break;
          case 'sq':
            apiUrl += '&type=sq';
            break;
          case 'hires':
            apiUrl += '&type=hires';
            break;
          case 'mq':
          default:
            // 标准音质不需要额外参数
            break;
        }

        final response = await _dio.get(apiUrl);

        if (response.statusCode == 200) {
          final url = response.data.toString().trim();
          if (url.isNotEmpty && url.startsWith('http')) {
            // 简化音频URL获取日志
            if (kDebugMode) {
              print('KuwoMusicService: Audio URL fetched for: $musicId ($quality)');
            }
            return url;
          }
        }

        if (kDebugMode) {
          print('KuwoMusicService: Invalid song URL response for: $musicId ($quality)');
        }
        return null;
      } catch (e) {
        if (kDebugMode) {
          print('KuwoMusicService: Error getting song play URL for $musicId ($quality): $e');
        }
        return null;
      }
    });
  }

  /// 获取歌曲播放URL（兼容旧接口）
  /// 默认使用标准音质
  Future<String?> getSongPlayUrlLegacy(String musicId) async {
    return await getSongPlayUrl(musicId, quality: 'mq');
  }

  // 防止同一首歌的并发请求
  static final Map<String, Future<String?>> _pendingRequests = {};

  /// 获取高清专辑封面（带URL缓存和防重复）
  /// [musicId] 歌曲ID，例如：441426083
  Future<String?> getHighQualityAlbumCover(String musicId) async {
    // 检查URL缓存
    final cachedUrl = _cacheManager.getCachedImageUrl(musicId);
    if (cachedUrl != null) {
      if (kDebugMode) {
        print('KuwoMusicService: URL cache HIT for $musicId: $cachedUrl');
      }
      return cachedUrl;
    }

    // 检查是否已有正在进行的请求
    if (_pendingRequests.containsKey(musicId)) {
      if (kDebugMode) {
        print('KuwoMusicService: Request already in progress for $musicId, waiting...');
      }
      return await _pendingRequests[musicId];
    }

    if (kDebugMode) {
      print('KuwoMusicService: URL cache MISS for $musicId, fetching from API...');
    }

    // 创建新的请求
    final requestFuture = _fetchHighQualityAlbumCover(musicId);
    _pendingRequests[musicId] = requestFuture;

    try {
      final result = await requestFuture;
      return result;
    } finally {
      // 请求完成后清理
      _pendingRequests.remove(musicId);
    }
  }

  /// 实际的API请求方法
  Future<String?> _fetchHighQualityAlbumCover(String musicId) async {

    try {
      if (kDebugMode) {
        print('KuwoMusicService: Fetching high quality album cover from API for: $musicId');
      }

      final response = await _dio.get(
        'https://m.kuwo.cn/h5app/single/$musicId',
      );

      if (response.statusCode == 200) {
        final String htmlContent = response.data.toString();

        // 查找script标签中的pic120字段
        final RegExp scriptRegex = RegExp(r'<script>window\.__NUXT__=.*?</script>');
        final Match? scriptMatch = scriptRegex.firstMatch(htmlContent);

        if (scriptMatch != null) {
          final String scriptContent = scriptMatch.group(0) ?? '';

          // 查找pic120字段
          final RegExp pic120Regex = RegExp(r'pic120:"([^"]*)"');
          final Match? pic120Match = pic120Regex.firstMatch(scriptContent);

          if (pic120Match != null) {
            String pic120Url = pic120Match.group(1) ?? '';

            // USC2转ANSI解码
            pic120Url = _decodeUsc2ToAnsi(pic120Url);

            // 将/120/替换为/500/获取高清图片
            final String highQualityUrl = pic120Url.replaceAll('/120/', '/500/');

            // 缓存URL避免下次重复请求
            _cacheManager.cacheImageUrl(musicId, highQualityUrl);

            if (kDebugMode) {
              print('KuwoMusicService: Found and cached high quality album cover: $highQualityUrl');
            }

            return highQualityUrl;
          }
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting high quality album cover: $e');
      }
      return null;
    }
  }

  /// USC2转ANSI解码
  String _decodeUsc2ToAnsi(String input) {
    try {
      // 替换USC2编码的反斜杠
      String decoded = input.replaceAll(r'\u002F', '/');
      decoded = decoded.replaceAll(r'\u003A', ':');
      decoded = decoded.replaceAll(r'\u003D', '=');
      decoded = decoded.replaceAll(r'\u0026', '&');
      decoded = decoded.replaceAll(r'\u003F', '?');

      return decoded;
    } catch (e) {
      if (kDebugMode) {
        print('Error decoding USC2 to ANSI: $e');
      }
      return input;
    }
  }

  /// 智能转换普通封面URL为高质量版本
  /// 避免不必要的网络请求
  String? _convertToHighQualityUrl(String originalUrl) {
    try {
      // 酷我音乐URL模式分析
      // 普通: https://img4.kuwo.cn/star/albumcover/120/s3s36/75/400243393.jpg
      // 高质量: https://img3.kuwo.cn/star/albumcover/500/s3s36/75/400243393.jpg

      if (originalUrl.contains('kuwo.cn/star/albumcover/')) {
        // 替换尺寸：120 -> 500, 240 -> 500 等
        String highQualityUrl = originalUrl
            .replaceAll('/120/', '/500/')
            .replaceAll('/240/', '/500/')
            .replaceAll('/300/', '/500/');

        // 如果URL发生了变化，说明转换成功
        if (highQualityUrl != originalUrl) {
          if (kDebugMode) {
            print('KuwoMusicService: URL conversion successful');
            print('Original: $originalUrl');
            print('High Quality: $highQualityUrl');
          }
          return highQualityUrl;
        }
      }

      // 如果无法转换，返回null
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('KuwoMusicService: Error converting URL: $e');
      }
      return null;
    }
  }
}