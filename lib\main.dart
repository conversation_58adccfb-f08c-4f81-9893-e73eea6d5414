import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import 'core/constants/app_constants.dart';
import 'data/providers/mock_data_provider.dart';
import 'data/providers/mini_player_provider.dart';
import 'data/providers/enhanced_mini_player_provider.dart';
import 'data/providers/playback_state_provider.dart';
import 'data/providers/player_page_provider.dart';
import 'data/services/audio_player_service.dart';
import 'data/services/permission_service.dart';
import 'data/services/player_state_sync_service.dart';
import 'data/services/unified_cache_manager.dart';
import 'data/services/enhanced_playlist_manager.dart';
import 'services/music_service_manager.dart';

import 'pages/main_page_v3.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置首选方向
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 初始化缓存管理器
  await UnifiedCacheManager().initialize();

  // 初始化音频播放服务（这会自动初始化通知服务）
  AudioPlayerService();

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  bool _permissionsChecked = false;
  bool _serviceInitialized = false; // 添加服务初始化标志
  final PlayerStateSyncService _syncService = PlayerStateSyncService();
  final MusicServiceManager _serviceManager = MusicServiceManager();

  @override
  void initState() {
    super.initState();
    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // 检查权限
      await _checkPermissions();

      // 简单的初始化延迟
      await Future.delayed(const Duration(milliseconds: 800));

      // 设置为已初始化
      if (mounted) {
        setState(() {
          _permissionsChecked = true;
        });
      }

      // 权限检查完成后再初始化音乐服务
      await _initializeMusicServiceDelayed();

    } catch (e) {
      if (kDebugMode) {
        print('App initialization error: $e');
      }
      // 即使出错也要让应用启动
      if (mounted) {
        setState(() {
          _permissionsChecked = true;
        });
      }
    }
  }

  /// 检查应用启动时的权限
  Future<void> _checkPermissions() async {
    try {
      final permissionService = PermissionService();

      // 检查通知权限
      final hasNotificationPermission = await permissionService.checkNotificationPermission();

      if (kDebugMode) {
        print('Main: Notification permission at startup: $hasNotificationPermission');
      }

      // 如果没有通知权限，可以在这里记录或者设置标志
      // 实际的权限请求会在用户开始播放音乐时进行
      if (!hasNotificationPermission) {
        if (kDebugMode) {
          print('Main: Notification permission not granted, will request when needed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Main: Error checking permissions at startup: $e');
      }
    }
  }

  /// 延迟初始化音乐服务
  Future<void> _initializeMusicServiceDelayed() async {
    if (_serviceInitialized) {
      if (kDebugMode) {
        print('Main: Music service already initialized, skipping');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('Main: Starting delayed music service initialization...');
      }
      
      // 更长的延迟，确保UI完全初始化
      await Future.delayed(const Duration(milliseconds: 2000));
      
      // 异步启动音乐前台服务，避免阻塞主线程
      unawaited(_startMusicServiceAsync());
      
      _serviceInitialized = true;
      
    } catch (e) {
      if (kDebugMode) {
        print('Main: Error initializing delayed music service: $e');
      }
    }
  }
  
  /// 异步启动音乐服务
  Future<void> _startMusicServiceAsync() async {
    try {
      if (kDebugMode) {
        print('Main: Starting async music service...');
      }
      
      // 启动音乐前台服务
      final started = await _serviceManager.startMusicService();
      
      if (kDebugMode) {
        print('Main: Music service started: $started');
      }

      if (started) {
        // 服务启动成功后，再次延迟获取状态
        await Future.delayed(const Duration(milliseconds: 2000));
        
        // 获取服务状态（带重试机制）
        await _getServiceStatusWithRetry();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Main: Error in async music service start: $e');
      }
    }
  }

  /// 带重试机制的服务状态获取
  Future<void> _getServiceStatusWithRetry() async {
    for (int i = 0; i < 3; i++) {
      try {
        await Future.delayed(Duration(milliseconds: 1000 * (i + 1)));
        final status = await _serviceManager.getServiceStatus();
        if (kDebugMode) {
          print('Main: Service status (attempt ${i + 1}): $status');
        }
        
        // 如果成功获取状态，退出重试循环
        if (status['isServiceRunning'] != null) {
          break;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Main: Service status check failed (attempt ${i + 1}): $e');
        }
        if (i == 2) {
          if (kDebugMode) {
            print('Main: Service status check failed after 3 attempts');
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<MockDataProvider>(
          create: (_) => MockDataProvider(),
        ),
        // 保留旧的Provider以确保兼容性
        ChangeNotifierProvider<MiniPlayerProvider>(
          create: (_) => MiniPlayerProvider(),
        ),
        ChangeNotifierProvider<EnhancedMiniPlayerProvider>(
          create: (_) => EnhancedMiniPlayerProvider(),
        ),
        // 新的统一Provider
        ChangeNotifierProvider<PlaybackStateProvider>(
          create: (_) => PlaybackStateProvider(),
        ),
        ChangeNotifierProvider<PlayerPageProvider>(
          create: (_) => PlayerPageProvider(),
        ),
        ChangeNotifierProvider<EnhancedPlaylistManager>(
          create: (_) => EnhancedPlaylistManager(),
        ),
      ],
      child: Builder(
        builder: (context) {
          // 初始化同步服务
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final miniPlayerProvider = Provider.of<MiniPlayerProvider>(context, listen: false);
            final enhancedMiniPlayerProvider = Provider.of<EnhancedMiniPlayerProvider>(context, listen: false);
            final playbackStateProvider = Provider.of<PlaybackStateProvider>(context, listen: false);
            final enhancedPlaylistManager = Provider.of<EnhancedPlaylistManager>(context, listen: false);

            _syncService.initialize(
              miniPlayerProvider: miniPlayerProvider,
              enhancedMiniPlayerProvider: enhancedMiniPlayerProvider,
              playbackStateProvider: playbackStateProvider,
            );

            // 连接 AudioPlayerService 与 EnhancedPlaylistManager
            final audioPlayerService = AudioPlayerService();
            audioPlayerService.setEnhancedPlaylistManager(enhancedPlaylistManager);
          });

          return MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primaryColor: const Color(0xFFF8BBE4),
              scaffoldBackgroundColor: Colors.white,
              useMaterial3: true,
              brightness: Brightness.light,
              colorScheme: const ColorScheme.light(
                primary: Color(0xFFF8BBE4),
                secondary: Color(0xFF675FDE),
              ),
            ),
            home: _permissionsChecked
                ? const MainPageV3()
                : const _SplashScreen(),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        // 应用回到前台
        _serviceManager.onAppComesToForeground();
        if (kDebugMode) {
          print('App: Resumed (foreground)');
        }
        break;
      case AppLifecycleState.paused:
        // 应用进入后台
        _serviceManager.onAppGoesToBackground();
        if (kDebugMode) {
          print('App: Paused (background)');
        }
        break;
      case AppLifecycleState.detached:
        // 应用被终止
        if (kDebugMode) {
          print('App: Detached');
        }
        break;
      case AppLifecycleState.inactive:
        // 应用处于非活跃状态（如接收电话）
        if (kDebugMode) {
          print('App: Inactive');
        }
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏
        if (kDebugMode) {
          print('App: Hidden');
        }
        break;
    }
  }
}

/// 启动画面
class _SplashScreen extends StatelessWidget {
  const _SplashScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8BBE4),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用图标
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.music_note,
                size: 40,
                color: Color(0xFFF8BBE4),
              ),
            ),
            const SizedBox(height: 24),
            // 应用名称
            const Text(
              AppConstants.appName,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '正在初始化...',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 32),
            // 加载指示器
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
