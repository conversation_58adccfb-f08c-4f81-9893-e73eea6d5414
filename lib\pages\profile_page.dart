import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../core/theme/app_colors.dart';
import '../core/theme/app_text_styles.dart';
import '../data/providers/mock_data_provider.dart';
import '../data/models/models.dart';
import '../widgets/profile/profile_header.dart';
import '../widgets/profile/profile_stats.dart';
import '../widgets/profile/profile_menu_item.dart';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with SingleTickerProviderStateMixin {
  late MockDataProvider _dataProvider;
  late User _currentUser;
  late List<Playlist> _userPlaylists;
  late List<Song> _recentlyPlayed;
  late TabController _tabController;
  
  final List<String> _tabs = [
    'Playlists',
    'Favorites',
    'History',
    'Downloads',
    'Following',
  ];
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _dataProvider = Provider.of<MockDataProvider>(context, listen: false);
    _loadData();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  void _loadData() {
    _currentUser = _dataProvider.currentUser;
    _userPlaylists = _dataProvider.playlists.where((playlist) => 
      playlist.creatorId == _currentUser.id).toList();
    _recentlyPlayed = _dataProvider.getRandomSongs(10);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    // 用户资料头部
                    ProfileHeader(
                      user: _currentUser,
                      onEditProfile: () {
                        // TODO: 导航到编辑个人资料页面
                      },
                    ),
                    
                    // 用户统计信息
                    ProfileStats(
                      user: _currentUser,
                      onFollowingTap: () {
                        // TODO: 查看关注列表
                      },
                      onFollowersTap: () {
                        // TODO: 查看粉丝列表
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 功能菜单
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: ProfileMenuItem(
                                  icon: Icons.music_note,
                                  title: 'My Songs',
                                  onTap: () {
                                    // TODO: 查看我的歌曲
                                  },
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: ProfileMenuItem(
                                  icon: Icons.playlist_play,
                                  title: 'My Playlists',
                                  onTap: () {
                                    // TODO: 查看我的歌单
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: ProfileMenuItem(
                                  icon: Icons.download,
                                  title: 'Downloads',
                                  onTap: () {
                                    // TODO: 查看下载内容
                                  },
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: ProfileMenuItem(
                                  icon: Icons.settings,
                                  title: 'Settings',
                                  onTap: () {
                                    // TODO: 打开设置页面
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // 标签栏
                    TabBar(
                      controller: _tabController,
                      isScrollable: true,
                      labelColor: AppColors.primaryColor,
                      unselectedLabelColor: AppColors.textSecondary,
                      indicatorColor: AppColors.primaryColor,
                      indicatorSize: TabBarIndicatorSize.label,
                      labelStyle: AppTextStyles.tabText,
                      unselectedLabelStyle: AppTextStyles.tabText,
                      tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
                    ),
                  ],
                ),
              ),
            ];
          },
          body: TabBarView(
            controller: _tabController,
            children: List.generate(_tabs.length, (index) {
              return _buildTabContent(index);
            }),
          ),
        ),
      ),
    );
  }
  
  Widget _buildTabContent(int tabIndex) {
    switch (tabIndex) {
      case 0: // Playlists
        return _buildPlaylistsTab();
      case 1: // Favorites
        return _buildFavoritesTab();
      case 2: // History
        return _buildHistoryTab();
      case 3: // Downloads
        return _buildDownloadsTab();
      case 4: // Following
        return _buildFollowingTab();
      default:
        return const Center(child: Text('Coming soon'));
    }
  }
  
  Widget _buildPlaylistsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          
          // 我的歌单
          SectionHeader(
            title: 'My Playlists',
            actionText: 'Create New',
            onMoreTap: () {
              // TODO: 创建新歌单
            },
          ),
          
          if (_userPlaylists.isEmpty)
            _buildEmptyState('No playlists yet', 'Create your first playlist')
          else
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.85,
                crossAxisSpacing: 12,
                mainAxisSpacing: 16,
              ),
              itemCount: _userPlaylists.length,
              itemBuilder: (context, index) {
                return PlaylistCard(
                  playlist: _userPlaylists[index],
                  onTap: () {
                    // TODO: 打开歌单详情
                  },
                );
              },
            ),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }
  
  Widget _buildFavoritesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          
          // 收藏的歌曲
          const SectionHeader(
            title: 'Favorite Songs',
          ),
          
          if (_recentlyPlayed.isEmpty)
            _buildEmptyState('No favorite songs yet', 'Like songs to see them here')
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _recentlyPlayed.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: SongItem(
                    song: _recentlyPlayed[index],
                    onTap: () {
                      // TODO: 播放歌曲
                    },
                  ),
                );
              },
            ),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }
  
  Widget _buildHistoryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          
          // 最近播放
          const SectionHeader(
            title: 'Recently Played',
          ),
          
          if (_recentlyPlayed.isEmpty)
            _buildEmptyState('No play history yet', 'Songs you play will appear here')
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _recentlyPlayed.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: SongItem(
                    song: _recentlyPlayed[index],
                    showPlayTime: true,
                    onTap: () {
                      // TODO: 播放歌曲
                    },
                  ),
                );
              },
            ),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }
  
  Widget _buildDownloadsTab() {
    return _buildEmptyState(
      'No downloaded songs',
      'Downloaded songs will appear here',
    );
  }
  
  Widget _buildFollowingTab() {
    return _buildEmptyState(
      'Not following anyone yet',
      'Follow artists and friends to see them here',
    );
  }
  
  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_off,
              size: 80,
              color: AppColors.textSecondary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
} 