import 'dart:math' as math;

void main() {
  print('测试颜色模式随机化：');
  
  // 模拟多次颜色提取，检查是否真正随机
  Map<int, int> modeCount = {};
  
  for (int i = 0; i < 100; i++) {
    final random = math.Random();
    final colorMode = random.nextInt(6) + 1;
    
    modeCount[colorMode] = (modeCount[colorMode] ?? 0) + 1;
  }
  
  print('100次随机测试结果：');
  for (int mode = 1; mode <= 6; mode++) {
    final count = modeCount[mode] ?? 0;
    final percentage = (count / 100 * 100).toStringAsFixed(1);
    print('模式 $mode: $count 次 ($percentage%)');
  }
  
  // 检查分布是否相对均匀（每个模式应该在10-25%之间）
  bool isWellDistributed = true;
  for (int mode = 1; mode <= 6; mode++) {
    final count = modeCount[mode] ?? 0;
    if (count < 5 || count > 35) {
      isWellDistributed = false;
      break;
    }
  }
  
  print('\n随机分布${isWellDistributed ? "正常" : "异常"}');
}
