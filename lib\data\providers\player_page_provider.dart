import 'package:flutter/material.dart';
import '../models/song_model.dart';

class PlayerPageProvider extends ChangeNotifier {
  static final PlayerPageProvider _instance = PlayerPageProvider._internal();
  factory PlayerPageProvider() => _instance;
  PlayerPageProvider._internal();

  // 播放页面状态
  Song? _currentSong;
  Color _dominantColor = const Color(0xFFB388FF); // 默认紫色
  Color _vibrantColor = const Color(0xFFE8B4FF); // 默认浅紫色
  bool _isInitialized = false;
  bool _showLyrics = false;

  // Getters
  Song? get currentSong => _currentSong;
  Color get dominantColor => _dominantColor;
  Color get vibrantColor => _vibrantColor;
  bool get isInitialized => _isInitialized;
  bool get showLyrics => _showLyrics;

  // 更新当前歌曲
  void updateCurrentSong(Song song) {
    if (_currentSong?.id != song.id) {
      _currentSong = song;
      _isInitialized = true;
      notifyListeners();
    }
  }

  // 更新颜色
  void updateColors(Color vibrant, Color dominant) {
    _vibrantColor = vibrant;
    _dominantColor = dominant;
    notifyListeners();
  }

  // 切换歌词显示
  void toggleLyrics() {
    _showLyrics = !_showLyrics;
    notifyListeners();
  }

  // 设置歌词显示状态
  void setShowLyrics(bool show) {
    _showLyrics = show;
    notifyListeners();
  }

  // 重置状态（当没有歌曲播放时）
  void reset() {
    _currentSong = null;
    _dominantColor = const Color(0xFFB388FF);
    _vibrantColor = const Color(0xFFE8B4FF);
    _isInitialized = false;
    _showLyrics = false;
    notifyListeners();
  }

  // 检查是否需要重新初始化
  bool needsReinitialization(Song? newSong) {
    if (newSong == null) return false;
    return _currentSong?.id != newSong.id || !_isInitialized;
  }
}
