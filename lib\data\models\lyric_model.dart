import 'package:equatable/equatable.dart';

class LyricLine extends Equatable {
  final String text;
  final Duration timestamp;
  
  const LyricLine({
    required this.text,
    required this.timestamp,
  });
  
  @override
  List<Object?> get props => [text, timestamp];
}

class Lyric extends Equatable {
  final List<LyricLine> lines;
  
  const Lyric({
    required this.lines,
  });
  
  // 解析歌词文本，支持两种格式:
  // 1. [00:00.00]歌词内容
  // 2. [time] 直接的数字时间戳，如[0.0]、[12.34]
  factory Lyric.parse(String lyricText) {
    final lines = <LyricLine>[];
    
    if (lyricText.isEmpty) {
      return Lyric(lines: lines);
    }
    
    final lineTexts = lyricText.split('\n');
    
    for (var line in lineTexts) {
      if (line.isEmpty) continue;
      
      // 检查是否包含时间戳
      if (!line.contains('[') || !line.contains(']')) continue;
      
      // 提取时间戳
      final timeStart = line.indexOf('[');
      final timeEnd = line.indexOf(']');
      
      if (timeStart < 0 || timeEnd < 0 || timeEnd <= timeStart) continue;
      
      final timeStr = line.substring(timeStart + 1, timeEnd);
      final content = line.substring(timeEnd + 1).trim();
      
      if (content.isEmpty) continue;
      
      try {
        Duration timestamp;
        
        // 检查时间格式
        if (timeStr.contains(':')) {
          // 格式1: 00:00.00
          final parts = timeStr.split(':');
          final minutes = int.parse(parts[0]);
          double seconds = 0;
          
          if (parts[1].contains('.')) {
            final secondParts = parts[1].split('.');
            seconds = double.parse(secondParts[0]) + double.parse('0.${secondParts[1]}');
          } else {
            seconds = double.parse(parts[1]);
          }
          
          timestamp = Duration(
            minutes: minutes,
            milliseconds: (seconds * 1000).toInt(),
          );
        } else {
          // 格式2: 直接的秒数，如0.0、12.34
          final seconds = double.parse(timeStr);
          timestamp = Duration(milliseconds: (seconds * 1000).toInt());
        }
        
        lines.add(LyricLine(
          text: content,
          timestamp: timestamp,
        ));
      } catch (e) {
        // 解析错误，跳过这一行
        continue;
      }
    }
    
    // 按时间戳排序
    lines.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    return Lyric(lines: lines);
  }
  
  // 获取当前时间对应的歌词索引
  int getCurrentLineIndex(Duration position) {
    if (lines.isEmpty) return -1;
    
    // 如果当前时间小于第一行的时间戳，返回-1
    if (position < lines.first.timestamp) return -1;
    
    // 如果当前时间大于最后一行的时间戳，返回最后一行
    if (position >= lines.last.timestamp) return lines.length - 1;
    
    // 查找当前应该显示的歌词行
    for (int i = 0; i < lines.length - 1; i++) {
      if (position >= lines[i].timestamp && position < lines[i + 1].timestamp) {
        return i;
      }
    }
    
    return lines.length - 1;
  }
  
  @override
  List<Object?> get props => [lines];
} 