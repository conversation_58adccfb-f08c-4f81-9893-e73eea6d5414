import 'package:flutter/foundation.dart';
import '../models/song_model.dart';
import 'audio_player_service.dart';
import 'player_state_sync_service.dart';

class PlaylistManager extends ChangeNotifier {
  // 单例模式
  static final PlaylistManager _instance = PlaylistManager._internal();
  
  factory PlaylistManager() {
    return _instance;
  }
  
  PlaylistManager._internal();
  
  // 播放列表
  List<Song> _playlist = [];
  int _currentIndex = -1;
  final AudioPlayerService _audioPlayerService = AudioPlayerService();
  
  // Getters
  List<Song> get playlist => _playlist;
  int get currentIndex => _currentIndex;
  Song? get currentSong => _currentIndex >= 0 && _currentIndex < _playlist.length 
      ? _playlist[_currentIndex] 
      : null;
  
  // 设置播放列表并从指定索引开始播放
  Future<void> setPlaylistAndPlay(List<Song> songs, int index) async {
    if (songs.isEmpty) return;
    
    // 设置播放列表并播放
    
    _playlist = List.from(songs);
    _currentIndex = index.clamp(0, songs.length - 1);
    
    await _playCurrent();
  }
  
  // 播放当前歌曲
  Future<void> _playCurrent() async {
    if (_currentIndex < 0 || _currentIndex >= _playlist.length) {
      if (kDebugMode) {
        print('PlaylistManager: Invalid current index: $_currentIndex');
      }
      return;
    }

    final song = _playlist[_currentIndex];
    if (song.audioUrl == null || song.audioUrl!.isEmpty) {
      if (kDebugMode) {
        print('PlaylistManager: Current song has no audio URL: ${song.title}');
      }
      // 不要递归调用playNext()，而是通知监听器当前歌曲无法播放
      notifyListeners();
      return;
    }

    if (kDebugMode) {
      print('PlaylistManager: Playing current song: ${song.title}, audioUrl: ${song.audioUrl}');
    }

    // 使用同步服务播放歌曲，确保UI正确更新
    final syncService = PlayerStateSyncService();
    await syncService.playSong(song);

    notifyListeners();
  }
  
  // 播放下一首
  Future<void> playNext() async {
    if (_playlist.isEmpty) return;
    
    int attempts = 0;
    final maxAttempts = _playlist.length; // 最多尝试播放列表中的所有歌曲
    
    do {
    if (_currentIndex < _playlist.length - 1) {
      _currentIndex++;
    } else {
      // 到达列表末尾，循环回第一首
      _currentIndex = 0;
    }
    
      attempts++;
      
      final song = _playlist[_currentIndex];
      if (song.audioUrl != null && song.audioUrl!.isNotEmpty) {
        // 找到有效的歌曲，播放它
    await _playCurrent();
        return;
      }
      
      if (kDebugMode) {
        print('PlaylistManager: Skipping song without audio URL: ${song.title}');
      }
      
    } while (attempts < maxAttempts);
    
    // 如果所有歌曲都没有audio URL，停止播放
    if (kDebugMode) {
      print('PlaylistManager: No playable songs found in playlist');
    }
    notifyListeners();
  }
  
  // 播放上一首
  Future<void> playPrevious() async {
    if (_playlist.isEmpty) return;
    
    int attempts = 0;
    final maxAttempts = _playlist.length; // 最多尝试播放列表中的所有歌曲
    
    do {
    if (_currentIndex > 0) {
      _currentIndex--;
    } else {
      // 到达列表开头，循环到最后一首
      _currentIndex = _playlist.length - 1;
    }
    
      attempts++;
      
      final song = _playlist[_currentIndex];
      if (song.audioUrl != null && song.audioUrl!.isNotEmpty) {
        // 找到有效的歌曲，播放它
    await _playCurrent();
        return;
      }
      
      if (kDebugMode) {
        print('PlaylistManager: Skipping song without audio URL: ${song.title}');
      }
      
    } while (attempts < maxAttempts);
    
    // 如果所有歌曲都没有audio URL，停止播放
    if (kDebugMode) {
      print('PlaylistManager: No playable songs found in playlist');
    }
    notifyListeners();
  }
  
  // 播放指定索引的歌曲
  Future<void> playAtIndex(int index) async {
    if (index < 0 || index >= _playlist.length) return;
    
    _currentIndex = index;
    await _playCurrent();
  }
  
  // 添加歌曲到播放列表
  void addToPlaylist(Song song) {
    _playlist.add(song);
  }
  
  // 添加多首歌曲到播放列表
  void addAllToPlaylist(List<Song> songs) {
    _playlist.addAll(songs);
  }
  
  // 从播放列表中移除歌曲
  void removeFromPlaylist(int index) {
    if (index < 0 || index >= _playlist.length) return;
    
    // 如果要删除的是当前正在播放的歌曲
    if (index == _currentIndex) {
      if (_playlist.length > 1) {
        // 如果是最后一首，播放第一首，否则播放下一首
        if (_currentIndex == _playlist.length - 1) {
          _playlist.removeAt(index);
          _currentIndex = 0;
          _playCurrent();
        } else {
          _playlist.removeAt(index);
          _playCurrent();
        }
      } else {
        // 如果是唯一的一首歌，停止播放
        _playlist.clear();
        _currentIndex = -1;
        _audioPlayerService.stop();
      }
    } else {
      // 如果删除的歌曲在当前歌曲之前，需要调整当前索引
      if (index < _currentIndex) {
        _currentIndex--;
      }
      _playlist.removeAt(index);
    }
  }
  
  // 清空播放列表
  void clearPlaylist() {
    _playlist.clear();
    _currentIndex = -1;
    _audioPlayerService.stop();
  }
  
  // 获取下一首歌曲
  Song? getNextSong() {
    if (_playlist.isEmpty) return null;
    
    int nextIndex = _currentIndex + 1;
    if (nextIndex >= _playlist.length) {
      nextIndex = 0;
    }
    
    return _playlist[nextIndex];
  }
  
  // 获取上一首歌曲
  Song? getPreviousSong() {
    if (_playlist.isEmpty) return null;
    
    int prevIndex = _currentIndex - 1;
    if (prevIndex < 0) {
      prevIndex = _playlist.length - 1;
    }
    
    return _playlist[prevIndex];
  }
  
  /// 设置播放列表但不立即播放
  Future<void> setPlaylist(List<Song> songs, int currentIndex) async {
    if (songs.isEmpty) return;
    
    // 设置播放列表（不播放）
    
    _playlist = List.from(songs);
    _currentIndex = currentIndex.clamp(0, songs.length - 1);
    
    // 通知监听器播放列表已更新
    notifyListeners();
  }
} 