import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/kuwo_ranking_model.dart';

class KuwoRankingService {
  static final KuwoRankingService _instance = KuwoRankingService._internal();
  factory KuwoRankingService() => _instance;
  KuwoRankingService._internal() {
    _initDio();
  }

  late final Dio _dio;
  static const String _baseUrl = 'https://api.xiaodaokg.com/kuwoapi.php';

  // 初始化Dio配置
  void _initDio() {
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'HiMusic/1.0.0',
      },
    ));

    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => print('KuwoAPI: $obj'),
      ));
    }
  }

  /// 获取排行榜数据
  /// [rankingType] 排行榜类型
  /// [page] 页码，从0开始
  /// [pageSize] 每页数量，默认20
  Future<KuwoRankingResponse?> getRankingData({
    required KuwoRankingType rankingType,
    int page = 0,
    int pageSize = 20,
  }) async {
    try {
      if (kDebugMode) {
        print('KuwoRankingService: Fetching ${rankingType.title} data, page: $page');
      }

      final response = await _dio.get(
        '',
        queryParameters: {
          'id': rankingType.id,
          'pn': page,
          'rn': pageSize,
          'simple': 1,
          'force_refresh': 0,
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        final kuwoResponse = KuwoRankingResponse.fromJson(response.data);
        
        if (kuwoResponse.success) {
          if (kDebugMode) {
            print('KuwoRankingService: Successfully fetched ${kuwoResponse.data.songs.length} songs');
          }
          return kuwoResponse;
        } else {
          if (kDebugMode) {
            print('KuwoRankingService: API returned error: ${kuwoResponse.message}');
          }
          return null;
        }
      } else {
        if (kDebugMode) {
          print('KuwoRankingService: HTTP error: ${response.statusCode}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('KuwoRankingService: Exception occurred: $e');
      }
      rethrow;
    }
  }

  /// 获取所有排行榜数据
  Future<Map<KuwoRankingType, KuwoRankingResponse?>> getAllRankings({
    int page = 0,
    int pageSize = 20,
  }) async {
    final Map<KuwoRankingType, KuwoRankingResponse?> results = {};
    
    try {
      // 并行请求所有排行榜数据
      final futures = KuwoRankingType.values.map((type) => 
        getRankingData(rankingType: type, page: page, pageSize: pageSize)
      );
      
      final responses = await Future.wait(futures);
      
      for (int i = 0; i < KuwoRankingType.values.length; i++) {
        results[KuwoRankingType.values[i]] = responses[i];
      }
      
      if (kDebugMode) {
        print('KuwoRankingService: Fetched all rankings successfully');
      }
      
      return results;
    } catch (e) {
      if (kDebugMode) {
        print('KuwoRankingService: Failed to fetch all rankings: $e');
      }
      rethrow;
    }
  }

  /// 搜索歌曲（可选功能，用于未来扩展）
  Future<List<KuwoRankingSong>> searchSongs(String keyword) async {
    try {
      // 这里可以实现搜索功能，需要对应的API端点
      // 暂时返回空列表
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('KuwoRankingService: Search failed: $e');
      }
      return [];
    }
  }

  /// 获取歌曲详情（可选功能，用于未来扩展）
  Future<KuwoRankingSong?> getSongDetail(String songId) async {
    try {
      // 这里可以实现获取歌曲详情功能，需要对应的API端点
      // 暂时返回null
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('KuwoRankingService: Get song detail failed: $e');
      }
      return null;
    }
  }

  /// 获取热歌榜数据
  Future<KuwoRankingResponse?> getHotHits({int page = 0, int pageSize = 20}) {
    return getRankingData(
      rankingType: KuwoRankingType.hotHits,
      page: page,
      pageSize: pageSize,
    );
  }

  /// 获取新歌榜数据
  Future<KuwoRankingResponse?> getNewSongs({int page = 0, int pageSize = 20}) {
    return getRankingData(
      rankingType: KuwoRankingType.newSongs,
      page: page,
      pageSize: pageSize,
    );
  }

  /// 获取抖音榜数据
  Future<KuwoRankingResponse?> getTikTokHits({int page = 0, int pageSize = 20}) {
    return getRankingData(
      rankingType: KuwoRankingType.shortVideo,
      page: page,
      pageSize: pageSize,
    );
  }

  /// 搜索排行榜中的歌曲
  List<KuwoRankingSong> searchSongsInRanking(
    List<KuwoRankingSong> songs,
    String query,
  ) {
    if (query.isEmpty) return songs;
    
    final lowerQuery = query.toLowerCase();
    return songs.where((song) {
      return song.name.toLowerCase().contains(lowerQuery) ||
             song.artist.toLowerCase().contains(lowerQuery) ||
             song.album.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// 根据排名筛选歌曲
  List<KuwoRankingSong> filterSongsByRank(
    List<KuwoRankingSong> songs,
    int maxRank,
  ) {
    return songs.take(maxRank).toList();
  }

  /// 获取VIP歌曲
  List<KuwoRankingSong> getVipSongs(List<KuwoRankingSong> songs) {
    return songs.where((song) => song.isVip).toList();
  }

  /// 获取免费歌曲
  List<KuwoRankingSong> getFreeSongs(List<KuwoRankingSong> songs) {
    return songs.where((song) => !song.isVip).toList();
  }
} 