{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Himusic-Flutter\\android\\app\\.cxx\\Debug\\pl493j22\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Himusic-Flutter\\android\\app\\.cxx\\Debug\\pl493j22\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}