import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:palette_generator/palette_generator.dart';
import 'dart:math' as math;
import 'dart:typed_data';
import '../data/services/unified_cache_manager.dart';

/// 颜色提取工具类
/// 参考E4A组件的PaletteHelper和ColorUtil实现
class ColorExtractor {
  /// 从图片URL提取主题色（使用缓存，避免网络请求）
  static Future<ExtractedColors> extractColorsFromUrl(String imageUrl) async {
    try {
      // 如果缓存中没有，返回默认颜色，不进行网络请求
      if (kDebugMode) {
        print('ColorExtractor: extractColorsFromUrl called, but should use extractColorsFromSong instead');
      }
      return ExtractedColors.defaultColors();
    } catch (e) {
      if (kDebugMode) {
        print('ColorExtractor: Failed to extract colors: $e');
      }
      return ExtractedColors.defaultColors();
    }
  }

  /// 从歌曲信息提取主题色（使用缓存，避免网络请求）
  static Future<ExtractedColors> extractColorsFromSong(String songId, String imageUrl) async {
    try {
      // 首先尝试从缓存获取图片数据
      final cacheManager = UnifiedCacheManager();

      // 直接使用传入的songId
      final imageData = await cacheManager.getAlbumCover(songId, imageUrl, requestedSize: 500);

      if (imageData != null) {
        // 使用缓存的图片数据
        final imageProvider = MemoryImage(imageData);
        // 使用缓存图片提取颜色

        final paletteGenerator = await PaletteGenerator.fromImageProvider(
          imageProvider,
          size: const Size(200, 200),
          maximumColorCount: 20,
        );

        final colors = _processColors(paletteGenerator);
        // 颜色提取成功
        return colors;
      } else {
        // 缓存中没有图片，返回默认颜色，不进行网络请求
        if (kDebugMode) {
          print('ColorExtractor: No cached image found for songId: $songId, using default colors');
        }
        return ExtractedColors.defaultColors();
      }
    } catch (e) {
      if (kDebugMode) {
        print('ColorExtractor: Failed to extract colors from cached image: $e');
      }
      // 如果提取失败，返回默认颜色
      return ExtractedColors.defaultColors();
    }
  }

  /// 从图片URL中提取songId
  static String? _extractSongIdFromUrl(String imageUrl) {
    try {
      // 尝试从URL中提取songId
      // 酷我音乐的图片URL通常包含数字ID
      final RegExp idRegex = RegExp(r'/(\d+)\.jpg');
      final match = idRegex.firstMatch(imageUrl);
      if (match != null) {
        return match.group(1);
      }

      // 如果没有找到，返回null
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 从PaletteGenerator处理颜色 - 支持多种模式随机选择
  /// 参考E4A ColorUtil的6种颜色模式
  static ExtractedColors _processColors(PaletteGenerator paletteGenerator) {
    // 获取各种颜色 - 对应E4A的6种颜色
    final vibrant = paletteGenerator.vibrantColor?.color;           // 鲜艳色
    final darkVibrant = paletteGenerator.darkVibrantColor?.color;   // 鲜艳深色
    final lightVibrant = paletteGenerator.lightVibrantColor?.color; // 鲜艳浅色
    final muted = paletteGenerator.mutedColor?.color;               // 柔和色
    final darkMuted = paletteGenerator.darkMutedColor?.color;       // 柔和深色
    final lightMuted = paletteGenerator.lightMutedColor?.color;     // 柔和浅色
    final dominant = paletteGenerator.dominantColor?.color;

    // 随机选择一种颜色模式（1-6对应E4A的6种模式）
    // 使用当前时间作为种子确保真正的随机性
    final random = math.Random(DateTime.now().millisecondsSinceEpoch);
    final colorMode = random.nextInt(6) + 1;

    Color primaryColor;
    Color backgroundColor;
    String modeName;

    // 创建随机备用颜色列表，确保即使提取失败也有随机性
    final randomBackupColors = [
      const Color(0xFFE91E63), // 粉红
      const Color(0xFF8E24AA), // 紫色
      const Color(0xFF42A5F5), // 蓝色
      const Color(0xFF795548), // 棕色
      const Color(0xFF5D4037), // 深棕
      const Color(0xFFA1887F), // 浅棕
      const Color(0xFF4CAF50), // 绿色
      const Color(0xFFFF9800), // 橙色
      const Color(0xFFF44336), // 红色
      const Color(0xFF9C27B0), // 深紫
      const Color(0xFF2196F3), // 深蓝
      const Color(0xFF00BCD4), // 青色
    ];

    switch (colorMode) {
      case 1: // 鲜艳色模式
        primaryColor = vibrant ?? randomBackupColors[random.nextInt(randomBackupColors.length)];
        backgroundColor = _getBackgroundColor(primaryColor);
        modeName = "Vibrant";
        break;
      case 2: // 鲜艳深色模式
        primaryColor = darkVibrant ?? randomBackupColors[random.nextInt(randomBackupColors.length)];
        backgroundColor = _getBackgroundColor(primaryColor);
        modeName = "DarkVibrant";
        break;
      case 3: // 鲜艳浅色模式
        primaryColor = lightVibrant ?? randomBackupColors[random.nextInt(randomBackupColors.length)];
        backgroundColor = _getBackgroundColor(primaryColor);
        modeName = "LightVibrant";
        break;
      case 4: // 柔和色模式
        primaryColor = muted ?? randomBackupColors[random.nextInt(randomBackupColors.length)];
        backgroundColor = _getBackgroundColor(primaryColor);
        modeName = "Muted";
        break;
      case 5: // 柔和深色模式
        primaryColor = darkMuted ?? randomBackupColors[random.nextInt(randomBackupColors.length)];
        backgroundColor = _getBackgroundColor(primaryColor);
        modeName = "DarkMuted";
        break;
      case 6: // 柔和浅色模式
      default:
        primaryColor = lightMuted ?? randomBackupColors[random.nextInt(randomBackupColors.length)];
        backgroundColor = _getBackgroundColor(primaryColor);
        modeName = "LightMuted";
        break;
    }

    // 生成渐变色
    final gradientColors = _generateGradientColors(primaryColor, backgroundColor);

    // 确定文字颜色
    final textColor = _getTitleColor(backgroundColor);

    // 颜色提取完成

    return ExtractedColors(
      primary: primaryColor,
      background: backgroundColor,
      gradientStart: gradientColors[0],
      gradientEnd: gradientColors[1],
      textColor: textColor,
      progressColor: _lightenColor(primaryColor, 0.2),
    );
  }

  /// 获取背景色 - 参考E4A ColorUtil.getBackgroundColor
  static Color _getBackgroundColor(Color color) {
    final red = color.red;
    final green = color.green;
    final blue = color.blue;

    final hsvValues = <double>[0, 0, 0];
    _rgbToHsv(red, green, blue, hsvValues);

    final hue = hsvValues[0];
    final saturation = hsvValues[1];
    final value = hsvValues[2];

    // 判断是否为接近白色或黑色
    final isNearWhite = saturation <= 0.05 && value >= 0.95;
    final isNearBlack = value <= 0.09;
    final isBright = value > 0.4;

    if (isNearWhite) {
      return const Color(0xFFd6e0e9);
    } else if (isNearBlack) {
      return const Color(0xFF5f6467);
    } else {
      if (isBright) {
        hsvValues[2] = 0.62; // 调暗
      } else {
        hsvValues[2] = 0.85; // 调亮
      }
      return _hsvToColor(hsvValues[0], hsvValues[1], hsvValues[2]);
    }
  }

  /// 获取标题颜色 - 参考E4A ColorUtil.setTittleColor
  static Color _getTitleColor(Color backgroundColor) {
    final gray = (backgroundColor.red * 0.299 +
                  backgroundColor.green * 0.587 +
                  backgroundColor.blue * 0.114).round();

    if (gray >= 192) {
      return Colors.black; // 浅色系背景用黑色文字
    } else {
      return Colors.white; // 深色系背景用白色文字
    }
  }

  /// 选择最佳颜色（避免过于接近白色或黑色）
  static Color? _selectBestColor(List<Color?> colors) {
    for (final color in colors) {
      if (color != null && _isColorSuitable(color)) {
        return color;
      }
    }
    return null;
  }

  /// 判断颜色是否适合作为主题色
  static bool _isColorSuitable(Color color) {
    final luminance = color.computeLuminance();
    // 避免过于亮（接近白色）或过于暗（接近黑色）的颜色
    return luminance > 0.1 && luminance < 0.8;
  }

  /// 加深颜色
  static Color _darkenColor(Color color, double factor) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness * (1 - factor)).clamp(0.0, 1.0)).toColor();
  }

  /// 提亮颜色
  static Color _lightenColor(Color color, double factor) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness + factor).clamp(0.0, 1.0)).toColor();
  }

  /// 生成渐变色
  static List<Color> _generateGradientColors(Color primary, Color background) {
    // 创建从背景色到主色调的渐变
    return [
      background,
      Color.lerp(background, primary, 0.3) ?? primary,
    ];
  }

  /// 获取对比色（用于文字）
  static Color _getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// 调整颜色透明度
  static Color adjustOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  /// 混合两种颜色
  static Color blendColors(Color color1, Color color2, double ratio) {
    return Color.lerp(color1, color2, ratio) ?? color1;
  }

  /// RGB转HSV - 参考E4A ColorUtil
  static void _rgbToHsv(int red, int green, int blue, List<double> hsv) {
    final r = red / 255.0;
    final g = green / 255.0;
    final b = blue / 255.0;

    final max = math.max(r, math.max(g, b));
    final min = math.min(r, math.min(g, b));
    final diff = max - min;

    // Value
    hsv[2] = max;

    // Saturation
    hsv[1] = max == 0 ? 0 : diff / max;

    // Hue
    if (diff == 0) {
      hsv[0] = 0;
    } else if (max == r) {
      hsv[0] = (60 * ((g - b) / diff) + 360) % 360;
    } else if (max == g) {
      hsv[0] = (60 * ((b - r) / diff) + 120) % 360;
    } else {
      hsv[0] = (60 * ((r - g) / diff) + 240) % 360;
    }
  }

  /// HSV转Color - 参考E4A ColorUtil
  static Color _hsvToColor(double hue, double saturation, double value) {
    final c = value * saturation;
    final x = c * (1 - ((hue / 60) % 2 - 1).abs());
    final m = value - c;

    double r, g, b;

    if (hue >= 0 && hue < 60) {
      r = c; g = x; b = 0;
    } else if (hue >= 60 && hue < 120) {
      r = x; g = c; b = 0;
    } else if (hue >= 120 && hue < 180) {
      r = 0; g = c; b = x;
    } else if (hue >= 180 && hue < 240) {
      r = 0; g = x; b = c;
    } else if (hue >= 240 && hue < 300) {
      r = x; g = 0; b = c;
    } else {
      r = c; g = 0; b = x;
    }

    return Color.fromARGB(
      255,
      ((r + m) * 255).round(),
      ((g + m) * 255).round(),
      ((b + m) * 255).round(),
    );
  }

  /// 颜色变深 - 参考E4A ColorUtil.TranslateDark
  static Color translateDark(Color color, int darkValue) {
    int red = color.red - darkValue;
    int green = color.green - darkValue;
    int blue = color.blue - darkValue;

    if (red < 0) red = 0;
    if (green < 0) green = 0;
    if (blue < 0) blue = 0;

    return Color.fromARGB(color.alpha, red, green, blue);
  }

  /// 颜色变浅 - 参考E4A ColorUtil.TranslateLight
  static Color translateLight(Color color, int lightValue) {
    int red = color.red + lightValue;
    int green = color.green + lightValue;
    int blue = color.blue + lightValue;

    if (red > 255) red = 255;
    if (green > 255) green = 255;
    if (blue > 255) blue = 255;

    return Color.fromARGB(color.alpha, red, green, blue);
  }
}

/// 提取的颜色数据类
class ExtractedColors {
  final Color primary;
  final Color background;
  final Color gradientStart;
  final Color gradientEnd;
  final Color textColor;
  final Color progressColor;

  const ExtractedColors({
    required this.primary,
    required this.background,
    required this.gradientStart,
    required this.gradientEnd,
    required this.textColor,
    required this.progressColor,
  });

  /// 默认颜色方案
  factory ExtractedColors.defaultColors() {
    return const ExtractedColors(
      primary: Color(0xFF2196F3),
      background: Color(0xFF1565C0),
      gradientStart: Color(0xFF1565C0),
      gradientEnd: Color(0xFF42A5F5),
      textColor: Colors.white,
      progressColor: Color(0xFF64B5F6),
    );
  }

  /// 复制并修改某些颜色
  ExtractedColors copyWith({
    Color? primary,
    Color? background,
    Color? gradientStart,
    Color? gradientEnd,
    Color? textColor,
    Color? progressColor,
  }) {
    return ExtractedColors(
      primary: primary ?? this.primary,
      background: background ?? this.background,
      gradientStart: gradientStart ?? this.gradientStart,
      gradientEnd: gradientEnd ?? this.gradientEnd,
      textColor: textColor ?? this.textColor,
      progressColor: progressColor ?? this.progressColor,
    );
  }
}
