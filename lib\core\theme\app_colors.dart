import 'package:flutter/material.dart';

class AppColors {
  // 主色调
  static const Color primaryColor = Color(0xFFFEB6D3); // 淡粉色主色调
  
  // 辅助色
  static const Color accentBlue1 = Color(0xFF675FDE); // 蓝紫色
  static const Color accentBlue2 = Color(0xFF273FCA); // 深蓝色
  static const Color grey1 = Color(0xFFE5E5E5); // 浅灰色
  static const Color grey2 = Color(0xFF999999); // 中灰色
  static const Color grey3 = Color(0xFF333333); // 近黑色
  
  // 功能色
  static const Color success = Color(0xFF4CAF50); // 成功色
  static const Color warning = Color(0xFFFFC107); // 警告色
  static const Color error = Color(0xFFF44336); // 错误色
  static const Color info = Color(0xFF2196F3); // 信息色
  
  // 背景色
  static const Color background = Colors.white; // 白色背景
  static const Color cardBackground = Color(0xFFFAFAFA); // 卡片背景色
  static const Color bottomBarBackground = Colors.white; // 底部导航栏背景色
  
  // 文字颜色
  static const Color textPrimary = Color(0xFF333333); // 主文本色
  static const Color textSecondary = Color(0xFF666666); // 次要文本色
  static const Color textHint = Color(0xFF999999); // 提示文本色
  
  // 渐变色
  static const List<Color> primaryGradient = [
    Color(0xFFFEB6D3),
    Color(0xFFFFDAE9),
  ];
  
  static const List<Color> purpleGradient = [
    Color(0xFFEFD7FC),
    Color(0xFFB388FF),
  ];
} 