import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/song_model.dart';
import '../../utils/color_extractor.dart';
import '../services/kuwo_music_service.dart';
import '../services/unified_cache_manager.dart';

/// 增强版迷你播放器状态管理
/// 包含颜色主题、动画状态等增强功能
class EnhancedMiniPlayerProvider extends ChangeNotifier {
  StreamSubscription<String>? _imageCacheSubscription;

  // 基础播放状态
  bool _isVisible = false;
  bool _isPlaying = false;
  Song? _currentSong;
  double _progress = 0.0;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;

  // 增强功能状态
  ExtractedColors _currentColors = ExtractedColors.defaultColors();
  bool _isLoadingColors = false;
  bool _isExpanded = false;

  EnhancedMiniPlayerProvider() {
    // 监听图片缓存更新事件
    _imageCacheSubscription = UnifiedCacheManager.imageCacheUpdateStream.listen((songId) {
      _onImageCacheUpdated(songId);
    });
  }
  
  // Getters
  bool get isVisible => _isVisible;
  bool get isPlaying => _isPlaying;
  Song? get currentSong => _currentSong;
  double get progress => _progress;
  Duration get position => _position;
  Duration get duration => _duration;
  ExtractedColors get currentColors => _currentColors;
  bool get isLoadingColors => _isLoadingColors;
  bool get isExpanded => _isExpanded;

  /// 显示迷你播放器
  void show() {
    if (!_isVisible) {
      _isVisible = true;
      notifyListeners();
    }
  }

  /// 隐藏迷你播放器
  void hide() {
    if (_isVisible) {
      _isVisible = false;
      notifyListeners();
    }
  }

  /// 开始播放
  void play() {
    if (!_isPlaying) {
      _isPlaying = true;
      notifyListeners();
    }
  }

  /// 暂停播放
  void pause() {
    if (_isPlaying) {
      _isPlaying = false;
      notifyListeners();
    }
  }

  /// 更新当前歌曲
  void updateCurrentSong(Song song) {
    if (_currentSong?.id != song.id) {
      _currentSong = song;
      // 播放时不立即提取颜色，使用默认颜色
      _currentColors = ExtractedColors.defaultColors();
      notifyListeners();
    }
  }

  /// 更新播放进度
  void updateProgress(Duration position, Duration duration) {
    _position = position;
    _duration = duration;
    
    if (duration.inMilliseconds > 0) {
      _progress = position.inMilliseconds / duration.inMilliseconds;
    } else {
      _progress = 0.0;
    }
    
    notifyListeners();
  }

  /// 设置播放进度
  void setProgress(double progress) {
    _progress = progress.clamp(0.0, 1.0);
    notifyListeners();
  }

  /// 切换展开状态
  void toggleExpanded() {
    _isExpanded = !_isExpanded;
    notifyListeners();
  }

  /// 从歌曲提取颜色主题
  Future<void> _extractColorsFromSong(Song song) async {
    _isLoadingColors = true;
    notifyListeners();

    try {
      // 只使用高质量URL，不使用默认的coverUrl（可能是默认图标）
      String? imageUrl = song.highQualityCoverUrl;

      // 如果没有高质量URL，尝试获取
      if (imageUrl == null || imageUrl.isEmpty) {
        final kuwoService = KuwoMusicService();
        imageUrl = await kuwoService.getHighQualityAlbumCover(song.id);
      }

      // 如果还是没有URL，使用默认颜色
      if (imageUrl == null || imageUrl.isEmpty) {
        _currentColors = ExtractedColors.defaultColors();
        _isLoadingColors = false;
        notifyListeners();
        return;
      }

      final colors = await ColorExtractor.extractColorsFromSong(song.id, imageUrl);
      
      _currentColors = colors;
      _isLoadingColors = false;
      
      // 颜色提取完成
      
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('EnhancedMiniPlayerProvider: Failed to extract colors: $e');
      }
      
      _currentColors = ExtractedColors.defaultColors();
      _isLoadingColors = false;
      notifyListeners();
    }
  }

  /// 手动设置颜色主题
  void setColors(ExtractedColors colors) {
    _currentColors = colors;
    notifyListeners();
  }

  /// 重置到默认颜色
  void resetColors() {
    _currentColors = ExtractedColors.defaultColors();
    notifyListeners();
  }

  /// 清除当前歌曲
  void clearCurrentSong() {
    _currentSong = null;
    _progress = 0.0;
    _position = Duration.zero;
    _duration = Duration.zero;
    _isPlaying = false;
    resetColors();
    notifyListeners();
  }

  /// 获取格式化的时间字符串
  String get formattedPosition {
    return _formatDuration(_position);
  }

  String get formattedDuration {
    return _formatDuration(_duration);
  }

  String get formattedProgress {
    return '${formattedPosition} / ${formattedDuration}';
  }

  /// 格式化时间
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  /// 获取进度百分比文本
  String get progressPercentage {
    return '${(_progress * 100).toInt()}%';
  }

  /// 检查是否有有效的歌曲
  bool get hasValidSong {
    return _currentSong != null && 
           _currentSong!.title.isNotEmpty && 
           _currentSong!.artistName.isNotEmpty;
  }

  /// 检查是否可以播放
  bool get canPlay {
    return hasValidSong && _currentSong!.audioUrl != null;
  }

  /// 处理图片缓存更新事件
  void _onImageCacheUpdated(String songId) {
    // 如果当前歌曲的图片缓存完成，开始提取颜色
    if (_currentSong?.id == songId) {
      // 提取颜色
      _extractColorsFromSong(_currentSong!);
    }
  }

  @override
  void dispose() {
    _imageCacheSubscription?.cancel();
    super.dispose();
  }
}
