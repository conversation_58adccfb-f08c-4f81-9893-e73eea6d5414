import 'package:flutter/material.dart';

/// 简单可靠的走马灯文本组件
class MarqueeText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final Duration duration;
  final Duration pauseDuration;
  final TextAlign textAlign;
  final int maxLines;
  final TextOverflow overflow;

  const MarqueeText({
    super.key,
    required this.text,
    this.style,
    this.duration = const Duration(seconds: 6),
    this.pauseDuration = const Duration(seconds: 1),
    this.textAlign = TextAlign.center,
    this.maxLines = 1,
    this.overflow = TextOverflow.ellipsis,
  });

  @override
  State<MarqueeText> createState() => _MarqueeTextState();
}

class _MarqueeTextState extends State<MarqueeText> {
  bool _needsScrolling = false;

  @override
  void initState() {
    super.initState();
    // 延迟检查是否需要滚动
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkIfNeedsScrolling();
    });
  }

  @override
  void didUpdateWidget(MarqueeText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _checkIfNeedsScrolling();
      });
    }
  }

  void _checkIfNeedsScrolling() {
    if (!mounted || widget.text.isEmpty) return;

    try {
      // 计算文本宽度
      final TextPainter textPainter = TextPainter(
        text: TextSpan(text: widget.text, style: widget.style),
        textDirection: TextDirection.ltr,
        maxLines: widget.maxLines,
      );
      textPainter.layout();

      // 安全地获取容器宽度
      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox != null && renderBox.hasSize) {
        final containerWidth = renderBox.size.width;
        final textWidth = textPainter.size.width;

        if (mounted) {
          setState(() {
            _needsScrolling = textWidth > containerWidth;
          });
        }
      }
    } catch (e) {
      // 忽略错误，避免在组件销毁时出现异常
      if (mounted) {
        setState(() {
          _needsScrolling = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.text.isEmpty) {
      return const SizedBox.shrink();
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // 实时计算是否需要滚动
        final TextPainter textPainter = TextPainter(
          text: TextSpan(text: widget.text, style: widget.style),
          textDirection: TextDirection.ltr,
          maxLines: widget.maxLines,
        );
        textPainter.layout();

        final textWidth = textPainter.size.width;
        final containerWidth = constraints.maxWidth;
        final needsScrolling = textWidth > containerWidth;

        return SizedBox(
          width: constraints.maxWidth,
          child: needsScrolling
              ? _MarqueeWidget(
                  text: widget.text,
                  style: widget.style,
                  duration: widget.duration,
                  pauseDuration: widget.pauseDuration,
                  maxLines: widget.maxLines,
                  textAlign: widget.textAlign,
                )
              : Text(
                  widget.text,
                  style: widget.style,
                  textAlign: widget.textAlign,
                  maxLines: widget.maxLines,
                  overflow: widget.overflow,
                ),
        );
      },
    );
  }
}

/// 内部走马灯动画组件
class _MarqueeWidget extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final Duration duration;
  final Duration pauseDuration;
  final int maxLines;
  final TextAlign textAlign;

  const _MarqueeWidget({
    required this.text,
    this.style,
    required this.duration,
    required this.pauseDuration,
    required this.maxLines,
    this.textAlign = TextAlign.center,
  });

  @override
  State<_MarqueeWidget> createState() => _MarqueeWidgetState();
}

class _MarqueeWidgetState extends State<_MarqueeWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _startAnimation();
  }

  void _startAnimation() async {
    await Future.delayed(widget.pauseDuration);
    if (mounted) {
      _controller.forward().then((_) {
        if (mounted) {
          Future.delayed(widget.pauseDuration, () {
            if (mounted) {
              _controller.reset();
              _startAnimation();
            }
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算文本宽度
        final TextPainter textPainter = TextPainter(
          text: TextSpan(text: widget.text, style: widget.style),
          textDirection: TextDirection.ltr,
          maxLines: widget.maxLines,
        );
        textPainter.layout();

        final textWidth = textPainter.size.width;
        final containerWidth = constraints.maxWidth;

        if (textWidth <= containerWidth) {
          // 如果文本不需要滚动，直接显示并居中
          return Text(
            widget.text,
            style: widget.style,
            textAlign: widget.textAlign, // 使用传入的textAlign属性
            maxLines: widget.maxLines,
            overflow: TextOverflow.visible,
          );
        }

        // 为无缝循环添加间距和重复文本
        const spacing = '    '; // 4个空格作为间距
        final repeatedText = '${widget.text}$spacing${widget.text}$spacing';

        // 重新计算重复文本的宽度
        final repeatedTextPainter = TextPainter(
          text: TextSpan(text: repeatedText, style: widget.style),
          textDirection: TextDirection.ltr,
          maxLines: widget.maxLines,
        );
        repeatedTextPainter.layout();

        // 计算间距的实际宽度
        final spacingPainter = TextPainter(
          text: TextSpan(text: spacing, style: widget.style),
          textDirection: TextDirection.ltr,
          maxLines: widget.maxLines,
        );
        spacingPainter.layout();
        final spacingWidth = spacingPainter.size.width;

        // 一个完整循环的距离 = 原文本宽度 + 间距宽度
        final scrollDistance = textWidth + spacingWidth;

        return ClipRect(
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              // 计算当前偏移量，实现无缝循环
              final currentOffset = _animation.value * scrollDistance;

              return Transform.translate(
                offset: Offset(-currentOffset, 0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    repeatedText,
                    style: widget.style,
                    maxLines: widget.maxLines,
                    overflow: TextOverflow.visible,
                    softWrap: false, // 防止文本换行
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
