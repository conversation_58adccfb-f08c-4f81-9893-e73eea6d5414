import 'package:equatable/equatable.dart';

enum SongQuality {
  standard, // 标准音质
  high, // 高音质
  hiFi, // HiFi音质
  master, // 母带音质
}

class Song extends Equatable {
  final String id;
  final String title;
  final String artistId;
  final String artistName;
  final String albumId;
  final String albumName;
  final String? coverUrl;
  final String? highQualityCoverUrl; // 高清专辑封面URL
  final String? audioUrl;
  final int duration; // 歌曲时长（秒）
  final DateTime releaseDate;
  final int playCount;
  final int likeCount;
  final int commentCount;
  final String? lyrics;
  final List<String> tags;
  final List<String> genres;
  final bool isLiked;
  final SongQuality quality;
  final bool isFeatured;
  final bool isExplicit;
  
  const Song({
    required this.id,
    required this.title,
    required this.artistId,
    required this.artistName,
    required this.albumId,
    required this.albumName,
    this.coverUrl,
    this.highQualityCoverUrl,
    this.audioUrl,
    required this.duration,
    required this.releaseDate,
    this.playCount = 0,
    this.likeCount = 0,
    this.commentCount = 0,
    this.lyrics,
    this.tags = const [],
    this.genres = const [],
    this.isLiked = false,
    this.quality = SongQuality.standard,
    this.isFeatured = false,
    this.isExplicit = false,
  });
  
  Song copyWith({
    String? id,
    String? title,
    String? artistId,
    String? artistName,
    String? albumId,
    String? albumName,
    String? coverUrl,
    String? highQualityCoverUrl,
    String? audioUrl,
    int? duration,
    DateTime? releaseDate,
    int? playCount,
    int? likeCount,
    int? commentCount,
    String? lyrics,
    List<String>? tags,
    List<String>? genres,
    bool? isLiked,
    SongQuality? quality,
    bool? isFeatured,
    bool? isExplicit,
  }) {
    return Song(
      id: id ?? this.id,
      title: title ?? this.title,
      artistId: artistId ?? this.artistId,
      artistName: artistName ?? this.artistName,
      albumId: albumId ?? this.albumId,
      albumName: albumName ?? this.albumName,
      coverUrl: coverUrl ?? this.coverUrl,
      highQualityCoverUrl: highQualityCoverUrl ?? this.highQualityCoverUrl,
      audioUrl: audioUrl ?? this.audioUrl,
      duration: duration ?? this.duration,
      releaseDate: releaseDate ?? this.releaseDate,
      playCount: playCount ?? this.playCount,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      lyrics: lyrics ?? this.lyrics,
      tags: tags ?? this.tags,
      genres: genres ?? this.genres,
      isLiked: isLiked ?? this.isLiked,
      quality: quality ?? this.quality,
      isFeatured: isFeatured ?? this.isFeatured,
      isExplicit: isExplicit ?? this.isExplicit,
    );
  }
  
  // 从JSON映射到模型
  factory Song.fromJson(Map<String, dynamic> json) {
    return Song(
      id: json['id'] as String,
      title: json['title'] as String,
      artistId: json['artist_id'] as String,
      artistName: json['artist_name'] as String,
      albumId: json['album_id'] as String,
      albumName: json['album_name'] as String,
      coverUrl: json['cover_url'] as String?,
      highQualityCoverUrl: json['high_quality_cover_url'] as String?,
      audioUrl: json['audio_url'] as String?,
      duration: json['duration'] as int,
      releaseDate: DateTime.parse(json['release_date'] as String),
      playCount: json['play_count'] as int? ?? 0,
      likeCount: json['like_count'] as int? ?? 0,
      commentCount: json['comment_count'] as int? ?? 0,
      lyrics: json['lyrics'] as String?,
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      genres: (json['genres'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      isLiked: json['is_liked'] as bool? ?? false,
      quality: SongQuality.values[json['quality'] as int? ?? 0],
      isFeatured: json['is_featured'] as bool? ?? false,
      isExplicit: json['is_explicit'] as bool? ?? false,
    );
  }
  
  // 从模型映射到JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'artist_id': artistId,
      'artist_name': artistName,
      'album_id': albumId,
      'album_name': albumName,
      'cover_url': coverUrl,
      'high_quality_cover_url': highQualityCoverUrl,
      'audio_url': audioUrl,
      'duration': duration,
      'release_date': releaseDate.toIso8601String(),
      'play_count': playCount,
      'like_count': likeCount,
      'comment_count': commentCount,
      'lyrics': lyrics,
      'tags': tags,
      'genres': genres,
      'is_liked': isLiked,
      'quality': quality.index,
      'is_featured': isFeatured,
      'is_explicit': isExplicit,
    };
  }
  
  // 格式化歌曲时长
  String get durationFormatted {
    final minutes = (duration / 60).floor();
    final seconds = duration % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
  
  // 格式化播放次数（如：1.2k, 3.5M）
  String get formattedPlayCount {
    if (playCount >= 1000000) {
      return '${(playCount / 1000000).toStringAsFixed(1)}M';
    } else if (playCount >= 1000) {
      return '${(playCount / 1000).toStringAsFixed(1)}k';
    } else {
      return playCount.toString();
    }
  }
  
  // 以下是Equatable所需的方法
  @override
  List<Object?> get props => [
    id,
    title,
    artistId,
    artistName,
    albumId,
    albumName,
    coverUrl,
    highQualityCoverUrl,
    audioUrl,
    duration,
    releaseDate,
    playCount,
    likeCount,
    commentCount,
    lyrics,
    tags,
    genres,
    isLiked,
    quality,
    isFeatured,
    isExplicit,
  ];
  
  @override
  bool get stringify => true;
} 