import 'package:flutter/material.dart';

import 'search_page.dart';
import 'player_page.dart';
import 'hot_hits_page.dart';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/common/cached_album_image.dart';

class HomePageV3 extends StatefulWidget {
  const HomePageV3({super.key});

  @override
  State<HomePageV3> createState() => _HomePageV3State();
}

class _HomePageV3State extends State<HomePageV3> {
  int _selectedCategoryIndex = 0;
  final List<String> _categories = ['推荐', '音频', '小说', '视频', 'R&B', '跑步'];
  final TextEditingController _searchController = TextEditingController();


  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // 分类标签栏
            _buildCategoryTabs(),

            // 搜索栏
            _buildSearchBar(),

            // 主内容
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 主要轮播图
                    _buildMainBanner(),

                    // 功能导航区
                    _buildFunctionNav(),

                    // 歌单分类区域
                    const PlaylistCategoriesSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      height: 50,
      color: Colors.white,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildCategoryTab('推荐', 0),
          _buildCategoryTab('音频', 1),
          _buildCategoryTab('小说', 2),
          _buildCategoryTab('视频', 3),
          _buildCategoryTab('R&B', 4),
          _buildCategoryTab('跑步', 5),
        ],
      ),
    );
  }

  Widget _buildCategoryTab(String text, int index) {
    final isSelected = index == _selectedCategoryIndex;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategoryIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF8BBE4) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.black87,
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SearchPage()),
          );
        },
        child: Container(
          height: 36,
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(18),
          ),
          child: Row(
            children: [
              const SizedBox(width: 16),
              Icon(Icons.search, size: 18, color: Colors.grey[500]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '歌曲/视频/歌单/专辑/歌词/电台/小说',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[500],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 主要轮播图 - 使用BannerSection组件
  Widget _buildMainBanner() {
    return BannerSection(
      playlists: _recommendedPlaylists.take(3).toList(),
    );
  }

  // 功能导航区 - 按照设计图样式
  Widget _buildFunctionNav() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem('🎵', '乐库', const Color(0xFFE8F5E8)),
          _buildNavItem('❤️', '猜你喜欢', const Color(0xFFFFE8E8)),
          _buildNavItem('📅', '每日推荐', const Color(0xFFE8F0FF)),
          _buildNavItem('📊', '排行榜', const Color(0xFFFFF8E8)),
          _buildNavItem('➕', '更多', const Color(0xFFF0E8FF)),
        ],
      ),
    );
  }

  Widget _buildNavItem(String icon, String label, Color bgColor) {
    return GestureDetector(
      onTap: () {
        if (label == '排行榜') {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const HotHitsPage()),
          );
        }
      },
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: bgColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                icon,
                style: const TextStyle(fontSize: 24),
              ),
            ),
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}