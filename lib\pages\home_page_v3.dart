import 'package:flutter/material.dart';
import '../data/models/playlist_model.dart';
import '../data/models/song_model.dart';
import 'package:provider/provider.dart';
import '../data/providers/mock_data_provider.dart';
import '../data/providers/mini_player_provider.dart';
import '../data/providers/enhanced_mini_player_provider.dart';
import '../data/providers/playback_state_provider.dart';
import '../data/services/player_state_sync_service.dart';
import '../data/services/audio_player_service.dart';
import 'playlist_detail_page.dart';
import 'search_page.dart';
import 'player_page.dart';
import 'hot_hits_page.dart';
import '../widgets/home/<USER>';
import '../widgets/common/cached_album_image.dart';

class HomePageV3 extends StatefulWidget {
  const HomePageV3({super.key});

  @override
  State<HomePageV3> createState() => _HomePageV3State();
}

class _HomePageV3State extends State<HomePageV3> {
  int _selectedCategoryIndex = 0;
  final List<String> _categories = ['推荐', '音频', '小说', '视频', 'R&B', '跑步'];
  final TextEditingController _searchController = TextEditingController();
  late List<Playlist> _recommendedPlaylists;
  late List<Song> _personalSongs;
  late MockDataProvider _dataProvider;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _dataProvider = Provider.of<MockDataProvider>(context, listen: false);
    _loadData();
  }

  void _loadData() {
    _recommendedPlaylists = _dataProvider.getRecommendedPlaylists();
    _personalSongs = _dataProvider.getRandomSongs(3);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // 分类标签栏
            _buildCategoryTabs(),

            // 搜索栏
            _buildSearchBar(),

            // 主内容
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 主要轮播图
                    _buildMainBanner(),

                    // 功能导航区
                    _buildFunctionNav(),

                    // 推荐歌单区域
                    _buildRecommendedSection(),

                    // 私人专属好歌区域
                    _buildPersonalSection(),

                    // 排行榜区域
                    _buildRankingSection(),

                    // 测试通知栏按钮
                    _buildTestNotificationButton(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      height: 50,
      color: Colors.white,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildCategoryTab('推荐', 0),
          _buildCategoryTab('音频', 1),
          _buildCategoryTab('小说', 2),
          _buildCategoryTab('视频', 3),
          _buildCategoryTab('R&B', 4),
          _buildCategoryTab('跑步', 5),
        ],
      ),
    );
  }

  Widget _buildCategoryTab(String text, int index) {
    final isSelected = index == _selectedCategoryIndex;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategoryIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF8BBE4) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.black87,
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SearchPage()),
          );
        },
        child: Container(
          height: 36,
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(18),
          ),
          child: Row(
            children: [
              const SizedBox(width: 16),
              Icon(Icons.search, size: 18, color: Colors.grey[500]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '歌曲/视频/歌单/专辑/歌词/电台/小说',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[500],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 主要轮播图 - 使用BannerSection组件
  Widget _buildMainBanner() {
    return BannerSection(
      playlists: _recommendedPlaylists.take(3).toList(),
    );
  }

  // 功能导航区 - 按照设计图样式
  Widget _buildFunctionNav() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem('🎵', '乐库', const Color(0xFFE8F5E8)),
          _buildNavItem('❤️', '猜你喜欢', const Color(0xFFFFE8E8)),
          _buildNavItem('📅', '每日推荐', const Color(0xFFE8F0FF)),
          _buildNavItem('📊', '排行榜', const Color(0xFFFFF8E8)),
          _buildNavItem('➕', '更多', const Color(0xFFF0E8FF)),
        ],
      ),
    );
  }

  Widget _buildNavItem(String icon, String label, Color bgColor) {
    return GestureDetector(
      onTap: () {
        if (label == '排行榜') {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const HotHitsPage()),
          );
        }
      },
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: bgColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                icon,
                style: const TextStyle(fontSize: 24),
              ),
            ),
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // 推荐歌单区域 - 按照设计图样式
  Widget _buildRecommendedSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '推荐歌单',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Icon(
                Icons.chevron_right,
                size: 20,
                color: Colors.grey[600],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 推荐歌单卡片
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFE8F0FF),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                // 左侧图标和文字
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: const BoxDecoration(
                              color: Color(0xFF4A90E2),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.headphones,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            '每日推荐',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '来自各类音乐精选',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),

                // 右侧播放按钮
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Color(0xFF4A90E2),
                    size: 24,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 第二个推荐卡片
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF8E8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                // 左侧图标和文字
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: const BoxDecoration(
                              color: Color(0xFFFF9500),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.music_note,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            '单曲推荐1000首',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '出行不愁的歌曲',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),

                // 右侧播放按钮
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Color(0xFFFF9500),
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 私人专属好歌区域 - 按照设计图样式
  Widget _buildPersonalSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '私人专属好歌',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Icon(
                Icons.chevron_right,
                size: 20,
                color: Colors.grey[600],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 歌曲列表
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildPersonalSongItem(
                  '小美满',
                  '周深 · 《你微笑时很美》电视剧原声带',
                  'https://picsum.photos/50/50?random=1',
                  isFirst: true,
                ),
                _buildPersonalSongItem(
                  '万国儿童',
                  '方国儿 · 《我是歌手的歌手》电视剧原声带',
                  'https://picsum.photos/50/50?random=2',
                ),
                _buildPersonalSongItem(
                  '黄昏晓',
                  '主人公 · 《微笑PASTA》电视剧原声带',
                  'https://picsum.photos/50/50?random=3',
                  isLast: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalSongItem(
    String title,
    String subtitle,
    String imageUrl, {
    bool isFirst = false,
    bool isLast = false,
  }) {
    // 为每个歌曲项目生成一个唯一ID（基于标题和副标题）
    final songId = '${title}_${subtitle}'.hashCode.toString();

    return GestureDetector(
      onTap: () async {
        print('HomePage: Song tapped - $title');

        // 创建一个测试歌曲
        final song = Song(
          id: songId, // 使用预先生成的ID
          title: title,
          artistId: 'artist_1',
          artistName: subtitle.split(' · ')[0],
          albumId: 'album_1',
          albumName: subtitle.split(' · ').length > 1 ? subtitle.split(' · ')[1] : 'Unknown Album',
          coverUrl: imageUrl,
          duration: 240, // 4分钟
          releaseDate: DateTime.now(),
          audioUrl: 'https://music.163.com/song/media/outer/url?id=**********.mp3', // 示例音频URL
        );

        print('HomePage: Created song - ${song.title}');

        // 直接更新Provider状态 - 同时更新新旧Provider确保兼容性
        final enhancedProvider = Provider.of<EnhancedMiniPlayerProvider>(context, listen: false);
        final playbackProvider = Provider.of<PlaybackStateProvider>(context, listen: false);

        print('HomePage: Updating providers...');

        // 更新旧Provider（保持兼容性）
        enhancedProvider.updateCurrentSong(song);
        enhancedProvider.show();
        enhancedProvider.play();

        // 更新新Provider
        playbackProvider.updateCurrentSong(song);
        playbackProvider.show();
        playbackProvider.play();

        print('HomePage: All providers updated');

        // 使用同步服务播放歌曲
        final syncService = PlayerStateSyncService();
        print('HomePage: Starting sync service...');
        await syncService.playSong(song);
        print('HomePage: Sync service completed');
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: isLast
                ? BorderSide.none
                : BorderSide(color: Colors.grey[200]!, width: 0.5),
          ),
        ),
        child: Row(
          children: [
            // 歌曲封面 - 统一使用高质量500px图片
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedAlbumImage(
                songId: songId, // 使用预先生成的songId
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                placeholder: Container(
                  width: 50,
                  height: 50,
                  color: Colors.grey[200],
                ),
                errorWidget: Container(
                  width: 50,
                  height: 50,
                  color: Colors.grey[200],
                  child: const Icon(Icons.music_note, color: Colors.grey),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // 歌曲信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // 播放按钮
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  // 排行榜区域 - 按照设计图样式
  Widget _buildRankingSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '排行榜',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Icon(
                Icons.chevron_right,
                size: 20,
                color: Colors.grey[600],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 排行榜网格
          Row(
            children: [
              Expanded(
                child: _buildRankingItem(
                  'TOP排行500',
                  'https://picsum.photos/100/100?random=10',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildRankingItem(
                  '抖音热歌榜',
                  'https://picsum.photos/100/100?random=11',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildRankingItem(
                  '流行RAP歌曲',
                  'https://picsum.photos/100/100?random=12',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildRankingItem(
                  '欧美女声榜',
                  'https://picsum.photos/100/100?random=13',
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildRankingItem(String title, String imageUrl) {
    return GestureDetector(
      onTap: () {
        // 跳转到排行榜详情
      },
      child: Column(
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedAlbumImage(
                  songId: title.hashCode.toString(), // 使用标题的hashCode作为临时ID
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                  placeholder: Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey[200],
                  ),
                  errorWidget: Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey[200],
                    child: const Icon(Icons.music_note, color: Colors.grey),
                  ),
                ),
              ),

              // 播放按钮
              Positioned(
                right: 4,
                bottom: 4,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.black,
                    size: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            title,
            style: const TextStyle(
              fontSize: 11,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 测试通知栏按钮
  Widget _buildTestNotificationButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: ElevatedButton(
        onPressed: _testNotification,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFF8BBE4),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          '测试音乐通知栏',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  // 测试通知栏功能
  void _testNotification() async {
    final audioPlayerService = AudioPlayerService();
    final miniPlayerProvider = Provider.of<MiniPlayerProvider>(context, listen: false);

    // 创建一个测试歌曲
    final testSong = Song(
      id: 'test_song_001',
      title: '我听过你的歌',
      artistId: 'artist_001',
      artistName: '王炎&何影',
      albumId: 'album_001',
      albumName: '测试专辑',
      coverUrl: 'https://picsum.photos/300/300?random=1',
      audioUrl: 'https://music.163.com/song/media/outer/url?id=**********.mp3', // 示例音频URL
      duration: 240, // 4分钟
      releaseDate: DateTime.now(),
      isLiked: false,
    );

    try {
      // 设置到播放器
      miniPlayerProvider.setCurrentSong(testSong);
      miniPlayerProvider.play();

      // 播放歌曲（这会自动显示通知栏）
      await audioPlayerService.playSong(testSong);

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('通知栏已显示，请查看系统通知栏'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('测试失败: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }


}