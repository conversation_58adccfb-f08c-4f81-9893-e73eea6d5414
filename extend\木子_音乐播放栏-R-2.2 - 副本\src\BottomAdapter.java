package com.muzi.mzmusic.ui.minibar;

import android.view.View;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import android.view.ViewGroup;
import android.widget.TextView;
import android.support.v4.view.PagerAdapter; 
import com.muzi.mzmusic.ui.minibar.video.BottomTextView;
public class BottomAdapter
extends PagerAdapter {
	BottomTextView CustomText;
	public List<Map<String, String>> List;
	public int[] getextColor = new int[]{14, -1};
	
	public BottomAdapter() {
        this.List = new ArrayList<Map<String, String>>();
    }
	
	public int getCount() {
        return List.size();
    }
	
	public Object instantiateItem(ViewGroup container, int position) {
		View view = View.inflate(container.getContext(),R.layout.an_muzi,null);
		CustomText=view.findViewById(R.id.e0d);
		CustomText.setTextColor(this.getextColor[1]);
		CustomText.setTextSize((float)this.getextColor[0]);
		CustomText.setText(List.get(position).get("name"));
		container.addView(view,0);
        return view;
    }
	
	public void destroyItem(ViewGroup container, int position, Object object) {
        container.removeView((View)object);
    }
	

    public int getItemPosition(Object object) {
        return POSITION_NONE;
    }

    public boolean isViewFromObject(View view, Object object) {
		return view == object;
    }
}