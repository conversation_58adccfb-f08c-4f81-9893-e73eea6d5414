package com.muzi.mzmusic.ui.view;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.widget.RelativeLayout;
public class BottomPaintView
extends RelativeLayout {
	public BottomPaintView(Context context) {
        super(context);
    }
 
    public BottomPaintView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
	
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        Paint paint = new Paint();
        paint.setStyle(Paint.Style.FILL);
        paint.setColor(Color.DKGRAY);
        paint.setStrokeWidth(2);
		paint.setARGB(15, 255, 0, 0);
        canvas.drawLine(0, getHeight()/2, getWidth(), getHeight()/2, paint);
    }
}