import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String username;
  final String email;
  final String? nickname;
  final String? avatar;
  final String? bio;
  final bool isPremium;
  final String? phoneNumber;
  final String? gender;
  final DateTime? birthday;
  final DateTime createdAt;
  final List<String> followingIds;
  final List<String> followerIds;
  final List<String> favoriteSongIds;
  final List<String> favoritePlaylistIds;
  
  const User({
    required this.id,
    required this.username,
    required this.email,
    this.nickname,
    this.avatar,
    this.bio,
    this.isPremium = false,
    this.phoneNumber,
    this.gender,
    this.birthday,
    required this.createdAt,
    this.followingIds = const [],
    this.followerIds = const [],
    this.favoriteSongIds = const [],
    this.favoritePlaylistIds = const [],
  });
  
  User copyWith({
    String? id,
    String? username,
    String? email,
    String? nickname,
    String? avatar,
    String? bio,
    bool? isPremium,
    String? phoneNumber,
    String? gender,
    DateTime? birthday,
    DateTime? createdAt,
    List<String>? followingIds,
    List<String>? followerIds,
    List<String>? favoriteSongIds,
    List<String>? favoritePlaylistIds,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      isPremium: isPremium ?? this.isPremium,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      gender: gender ?? this.gender,
      birthday: birthday ?? this.birthday,
      createdAt: createdAt ?? this.createdAt,
      followingIds: followingIds ?? this.followingIds,
      followerIds: followerIds ?? this.followerIds,
      favoriteSongIds: favoriteSongIds ?? this.favoriteSongIds,
      favoritePlaylistIds: favoritePlaylistIds ?? this.favoritePlaylistIds,
    );
  }
  
  // 从JSON映射到模型
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      nickname: json['nickname'] as String?,
      avatar: json['avatar'] as String?,
      bio: json['bio'] as String?,
      isPremium: json['is_premium'] as bool? ?? false,
      phoneNumber: json['phone_number'] as String?,
      gender: json['gender'] as String?,
      birthday: json['birthday'] != null 
          ? DateTime.parse(json['birthday'] as String) 
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      followingIds: (json['following_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      followerIds: (json['follower_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      favoriteSongIds: (json['favorite_song_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      favoritePlaylistIds: (json['favorite_playlist_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
    );
  }
  
  // 从模型映射到JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'nickname': nickname,
      'avatar': avatar,
      'bio': bio,
      'is_premium': isPremium,
      'phone_number': phoneNumber,
      'gender': gender,
      'birthday': birthday?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'following_ids': followingIds,
      'follower_ids': followerIds,
      'favorite_song_ids': favoriteSongIds,
      'favorite_playlist_ids': favoritePlaylistIds,
    };
  }
  
  // 获取显示名称
  String get displayName => nickname ?? username;
  
  // 是否有头像
  bool get hasAvatar => avatar != null && avatar!.isNotEmpty;
  
  // 以下是Equatable所需的方法
  @override
  List<Object?> get props => [
    id,
    username,
    email,
    nickname,
    avatar,
    bio,
    isPremium,
    phoneNumber,
    gender,
    birthday,
    createdAt,
    followingIds,
    followerIds,
    favoriteSongIds,
    favoritePlaylistIds,
  ];
  
  @override
  bool get stringify => true;
} 