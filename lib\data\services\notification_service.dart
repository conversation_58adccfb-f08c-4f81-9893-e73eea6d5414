import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'dart:async';
import 'package:http/http.dart' as http;
import '../models/song_model.dart';
import 'unified_cache_manager.dart';

/// 音乐通知栏服务
/// 负责管理音乐播放的通知栏显示和控制
class NotificationService {
  // 单例模式
  static final NotificationService _instance = NotificationService._internal();
  
  factory NotificationService() {
    return _instance;
  }
  
  NotificationService._internal();
  
  // MethodChannel 用于与Android原生代码通信
  static const MethodChannel _channel = MethodChannel('music_notification');
  final UnifiedCacheManager _cacheManager = UnifiedCacheManager();

  // 当前播放状态
  bool _isPlaying = false;
  Song? _currentSong;

  // 缓存事件监听
  StreamSubscription<String>? _cacheSubscription;
  
  // Getters
  bool get isPlaying => _isPlaying;
  Song? get currentSong => _currentSong;
  
  /// 初始化通知服务
  /// 设置方法调用处理器，监听来自Android端的回调
  Future<void> initialize() async {
    try {
      // 设置方法调用处理器
      _channel.setMethodCallHandler(_handleMethodCall);
      
      // 初始化Android端的通知渠道
      await _channel.invokeMethod('initializeNotification');

      // 监听图片缓存更新事件
      _cacheSubscription = UnifiedCacheManager.imageCacheUpdateStream.listen(_onImageCacheUpdated);

      if (kDebugMode) {
        print('NotificationService: Initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Initialization failed: $e');
      }
    }
  }
  
  /// 处理来自Android端的方法调用
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    if (kDebugMode) {
      print('NotificationService: Received method call: ${call.method}');
    }
    
    switch (call.method) {
      case 'onPlayPause':
        await _handlePlayPause();
        break;
      case 'onNext':
        await _handleNext();
        break;
      case 'onPrevious':
        await _handlePrevious();
        break;
      case 'onLike':
        await _handleLike();
        break;
      case 'onClose':
        await _handleClose();
        break;

      default:
        if (kDebugMode) {
          print('NotificationService: Unknown method: ${call.method}');
        }
    }
  }
  
  /// 显示音乐通知栏
  /// [song] 当前播放的歌曲
  /// [isPlaying] 是否正在播放
  /// [position] 当前播放位置（毫秒）
  /// [duration] 歌曲总时长（毫秒）
  /// [albumArt] 专辑封面字节数据
  Future<void> showNotification({
    required Song song,
    required bool isPlaying,
    int position = 0,
    int duration = 0,
    Uint8List? albumArt,
  }) async {
    try {
      _currentSong = song;
      _isPlaying = isPlaying;

      // 如果没有提供专辑封面，尝试加载默认封面
      if (albumArt == null) {
        albumArt = await _getDefaultAlbumArt();
      }

      final Map<String, dynamic> params = {
        'title': song.title,
        'artist': song.artistName, // 修正字段名
        'album': song.albumName,
        'albumArt': albumArt,
        'albumArtUrl': song.highQualityCoverUrl ?? song.coverUrl ?? '',
        'isPlaying': isPlaying,
        'position': position,
        'duration': duration,
        'songId': song.id,
        'isLiked': song.isLiked,
      };

      await _channel.invokeMethod('showNotification', params);

      // 移除重复的通知显示日志
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Failed to show notification: $e');
      }
    }
  }
  
  /// 更新通知栏播放状态
  Future<void> updatePlaybackState({
    required bool isPlaying,
    int position = 0,
  }) async {
    try {
      _isPlaying = isPlaying;
      
      final Map<String, dynamic> params = {
        'isPlaying': isPlaying,
        'position': position,
      };
      
      await _channel.invokeMethod('updatePlaybackState', params);
      
      if (kDebugMode) {
        print('NotificationService: Playback state updated - playing: $isPlaying');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Failed to update playback state: $e');
      }
    }
  }
  
  /// 更新通知栏播放进度
  Future<void> updateProgress({
    required int position,
    required int duration,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'position': position,
        'duration': duration,
      };
      
      await _channel.invokeMethod('updateProgress', params);
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Failed to update progress: $e');
      }
    }
  }
  
  /// 隐藏通知栏
  Future<void> hideNotification() async {
    try {
      await _channel.invokeMethod('hideNotification');
      _currentSong = null;
      _isPlaying = false;
      
      if (kDebugMode) {
        print('NotificationService: Notification hidden');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Failed to hide notification: $e');
      }
    }
  }
  
  /// 处理播放/暂停按钮点击
  Future<void> _handlePlayPause() async {
    if (kDebugMode) {
      print('NotificationService: Play/Pause button clicked');
    }
    
    // 这里需要与音频播放服务集成
    // 通过事件总线或回调通知播放器切换播放状态
    _notifyPlayPauseClicked();
  }
  
  /// 处理下一首按钮点击
  Future<void> _handleNext() async {
    if (kDebugMode) {
      print('NotificationService: Next button clicked');
    }
    
    _notifyNextClicked();
  }
  
  /// 处理上一首按钮点击
  Future<void> _handlePrevious() async {
    if (kDebugMode) {
      print('NotificationService: Previous button clicked');
    }

    _notifyPreviousClicked();
  }

  /// 处理喜欢按钮点击
  Future<void> _handleLike() async {
    if (kDebugMode) {
      print('NotificationService: Like button clicked');
    }

    _notifyLikeClicked();
  }

  /// 处理关闭通知栏
  Future<void> _handleClose() async {
    if (kDebugMode) {
      print('NotificationService: Close button clicked');
    }

    await hideNotification();
    _notifyCloseClicked();
  }


  
  // 事件回调 - 这些方法将被音频播放服务监听
  Function()? onPlayPauseClicked;
  Function()? onNextClicked;
  Function()? onPreviousClicked;
  Function()? onLikeClicked;
  Function()? onCloseClicked;


  void _notifyPlayPauseClicked() {
    onPlayPauseClicked?.call();
  }

  void _notifyNextClicked() {
    onNextClicked?.call();
  }

  void _notifyPreviousClicked() {
    onPreviousClicked?.call();
  }

  void _notifyLikeClicked() {
    onLikeClicked?.call();
  }

  void _notifyCloseClicked() {
    onCloseClicked?.call();
  }


  
  /// 设置事件监听器
  void setEventListeners({
    Function()? onPlayPause,
    Function()? onNext,
    Function()? onPrevious,
    Function()? onLike,
    Function()? onClose,

  }) {
    onPlayPauseClicked = onPlayPause;
    onNextClicked = onNext;
    onPreviousClicked = onPrevious;
    onLikeClicked = onLike;
    onCloseClicked = onClose;

  }
  
  /// 获取默认专辑封面
  Future<Uint8List?> _getDefaultAlbumArt() async {
    try {
      // 加载默认专辑封面图片
      final ByteData data = await rootBundle.load('assets/images/mbyz.jpg');
      return data.buffer.asUint8List();
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Failed to load default album art: $e');
      }
      return null;
    }
  }

  /// 从Song对象加载最佳专辑封面（只使用缓存）
  Future<Uint8List?> loadAlbumArtFromSong(Song song) async {
    try {
      // 只检查缓存，不下载
      final cachedImage = await _cacheManager.getAlbumCover(
        song.id,
        '', // 空URL，只检查缓存
        requestedSize: 500,
        checkCacheOnly: true,
      );

      if (cachedImage != null) {
        return cachedImage;
      }

      // 没有缓存，使用默认封面
      return await _getDefaultAlbumArt();
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Error loading album art from song: $e');
      }
      return await _getDefaultAlbumArt();
    }
  }

  /// 从网络URL加载专辑封面（使用缓存）
  Future<Uint8List?> loadAlbumArtFromUrl(String? url) async {
    if (url == null || url.isEmpty) {
      return await _getDefaultAlbumArt();
    }

    try {
      // 使用统一缓存管理器加载图片
      if (_currentSong != null) {
        final cachedImage = await _cacheManager.getAlbumCover(
          _currentSong!.id,
          url,
          requestedSize: 500 // 通知栏使用500px尺寸
        );

        if (cachedImage != null) {
          // 缓存命中，不输出日志
          return cachedImage;
        }
      }

      // 如果缓存失败，回退到默认封面
      if (kDebugMode) {
        print('NotificationService: Failed to load from cache, using default album art');
      }
      return await _getDefaultAlbumArt();
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Failed to load album art from URL: $e');
      }
      return await _getDefaultAlbumArt();
    }
  }

  /// 更新喜欢状态
  Future<void> updateLikeState(bool isLiked) async {
    try {
      final Map<String, dynamic> params = {
        'isLiked': isLiked,
      };

      await _channel.invokeMethod('updateLikeState', params);

      if (kDebugMode) {
        print('NotificationService: Like state updated to $isLiked');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Failed to update like state: $e');
      }
    }
  }

  /// 处理图片缓存更新事件
  void _onImageCacheUpdated(String songId) {
    // 如果当前播放的歌曲图片缓存完成，更新通知栏
    if (_currentSong != null && _currentSong!.id == songId) {
      // 更新通知栏图片
      // 异步更新通知栏
      _updateNotificationWithCachedImage();
    }
  }

  /// 使用缓存的图片更新通知栏
  Future<void> _updateNotificationWithCachedImage() async {
    if (_currentSong == null) return;

    try {
      // 重新加载专辑封面（现在应该有缓存了）
      final albumArt = await loadAlbumArtFromSong(_currentSong!);

      // 更新通知栏（使用showNotification方法）
      await _channel.invokeMethod('showNotification', {
        'title': _currentSong!.title,
        'artist': _currentSong!.artistName,
        'album': _currentSong!.albumName,
        'albumArt': albumArt,
        'albumArtUrl': _currentSong!.highQualityCoverUrl ?? _currentSong!.coverUrl ?? '',
        'isPlaying': _isPlaying,
        'position': 0, // 当前位置，这里可以传0或实际位置
        'duration': _currentSong!.duration * 1000, // 转换为毫秒
        'songId': _currentSong!.id,
        'isLiked': _currentSong!.isLiked,
      });

      // 通知栏更新成功
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Error updating notification with cached image: $e');
      }
    }
  }

  /// 清理资源
  Future<void> dispose() async {
    try {
      await hideNotification();

      // 取消缓存事件监听
      await _cacheSubscription?.cancel();
      _cacheSubscription = null;

      onPlayPauseClicked = null;
      onNextClicked = null;
      onPreviousClicked = null;
      onLikeClicked = null;
      onCloseClicked = null;

      if (kDebugMode) {
        print('NotificationService: Disposed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationService: Error during dispose: $e');
      }
    }
  }
} 