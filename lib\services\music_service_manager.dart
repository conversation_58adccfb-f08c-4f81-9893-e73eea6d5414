import 'package:flutter/services.dart';
import 'dart:developer' as developer;

/// 音乐服务管理器
/// 用于控制Android端的前台服务、WakeLock等后台运行功能
class MusicServiceManager {
  static const MethodChannel _serviceChannel = MethodChannel('music_service');
  
  static MusicServiceManager? _instance;
  
  MusicServiceManager._internal();
  
  factory MusicServiceManager() {
    _instance ??= MusicServiceManager._internal();
    return _instance!;
  }
  
  /// 启动音乐前台服务
  /// 确保应用在后台时能够持续运行
  Future<bool> startMusicService() async {
    try {
      developer.log('Starting music service...', name: 'MusicServiceManager');
      
      final result = await _serviceChannel.invokeMethod('startMusicService');
      developer.log('Music service start command result: $result', name: 'MusicServiceManager');
      
      // 服务启动后，等待更长时间确保连接建立
      if (result == true) {
        await Future.delayed(const Duration(milliseconds: 3000)); // 增加到3秒
        
        // 验证服务是否真的在运行（带重试）
        for (int i = 0; i < 3; i++) {
          try {
            final isRunning = await isServiceRunning();
            if (isRunning) {
              developer.log('Music service verified running on attempt ${i + 1}', name: 'MusicServiceManager');
              return true;
            }
            await Future.delayed(Duration(milliseconds: 1000 * (i + 1)));
          } catch (e) {
            developer.log('Service verification failed on attempt ${i + 1}: $e', name: 'MusicServiceManager');
          }
        }
        
        developer.log('Music service verification failed after 3 attempts', name: 'MusicServiceManager');
        return false;
      }
      
      return false;
    } catch (e) {
      developer.log('Failed to start music service: $e', name: 'MusicServiceManager');
      return false;
    }
  }
  
  /// 停止音乐前台服务
  Future<bool> stopMusicService() async {
    try {
      final result = await _serviceChannel.invokeMethod('stopMusicService');
      developer.log('Music service stopped: $result', name: 'MusicServiceManager');
      return result == true;
    } catch (e) {
      developer.log('Failed to stop music service: $e', name: 'MusicServiceManager');
      return false;
    }
  }
  
  /// 获取WakeLock，防止设备休眠影响音乐播放
  /// 建议在开始播放音乐时调用
  Future<bool> acquireWakeLock() async {
    try {
      final result = await _serviceChannel.invokeMethod('acquireWakeLock');
      developer.log('WakeLock acquired: $result', name: 'MusicServiceManager');
      return result == true;
    } catch (e) {
      developer.log('Failed to acquire WakeLock: $e', name: 'MusicServiceManager');
      return false;
    }
  }
  
  /// 释放WakeLock
  /// 建议在暂停播放或关闭应用时调用
  Future<bool> releaseWakeLock() async {
    try {
      final result = await _serviceChannel.invokeMethod('releaseWakeLock');
      developer.log('WakeLock released: $result', name: 'MusicServiceManager');
      return result == true;
    } catch (e) {
      developer.log('Failed to release WakeLock: $e', name: 'MusicServiceManager');
      return false;
    }
  }
  
  /// 检查WakeLock是否已获取
  Future<bool> isWakeLockHeld() async {
    for (int i = 0; i < 3; i++) {
      try {
        final result = await _serviceChannel.invokeMethod('isWakeLockHeld');
        return result == true;
      } catch (e) {
        developer.log('Failed to check WakeLock status (attempt ${i + 1}): $e', name: 'MusicServiceManager');
        if (i < 2) {
          await Future.delayed(Duration(milliseconds: 500 * (i + 1)));
        }
      }
    }
    return false;
  }
  
  /// 确保服务持久运行
  /// 在开始播放音乐时调用，强化后台运行能力
  Future<void> ensureServicePersistence() async {
    try {
      await _serviceChannel.invokeMethod('ensureServicePersistence');
      developer.log('Service persistence ensured', name: 'MusicServiceManager');
    } catch (e) {
      developer.log('Failed to ensure service persistence: $e', name: 'MusicServiceManager');
    }
  }
  
  /// 允许系统管理服务
  /// 在暂停播放时调用，允许系统优化资源使用
  Future<void> allowServiceManagement() async {
    try {
      await _serviceChannel.invokeMethod('allowServiceManagement');
      developer.log('Service management allowed', name: 'MusicServiceManager');
    } catch (e) {
      developer.log('Failed to allow service management: $e', name: 'MusicServiceManager');
    }
  }
  
  /// 检查服务是否正在运行
  Future<bool> isServiceRunning() async {
    for (int i = 0; i < 3; i++) {
      try {
        final result = await _serviceChannel.invokeMethod('isServiceRunning');
        return result == true;
      } catch (e) {
        developer.log('Failed to check service status (attempt ${i + 1}): $e', name: 'MusicServiceManager');
        if (i < 2) {
          await Future.delayed(Duration(milliseconds: 500 * (i + 1)));
        }
      }
    }
    return false;
  }
  
  /// 音乐播放开始时的优化设置
  Future<void> onMusicPlayStarted() async {
    developer.log('Optimizing for music playback start', name: 'MusicServiceManager');
    
    // 获取WakeLock防止设备休眠
    await acquireWakeLock();
    
    // 确保服务持久运行
    await ensureServicePersistence();
    
    // 更新前台服务通知状态
    await updateForegroundNotification(true);
    
    developer.log('Music playback optimization completed', name: 'MusicServiceManager');
  }
  
  /// 音乐播放暂停时的优化设置
  Future<void> onMusicPlayPaused() async {
    developer.log('Optimizing for music playback pause', name: 'MusicServiceManager');
    
    // 允许系统管理服务资源
    await allowServiceManagement();
    
    // 更新前台服务通知状态
    await updateForegroundNotification(false);
    
    // 注意：暂停时不释放WakeLock，因为用户可能很快继续播放
    // 如果需要完全释放，可以在这里调用 releaseWakeLock()
    
    developer.log('Music playback pause optimization completed', name: 'MusicServiceManager');
  }
  
  /// 音乐播放停止时的清理
  Future<void> onMusicPlayStopped() async {
    developer.log('Cleaning up after music playback stop', name: 'MusicServiceManager');
    
    // 释放WakeLock
    await releaseWakeLock();
    
    // 允许系统管理服务
    await allowServiceManagement();
    
    // 更新前台服务通知状态
    await updateForegroundNotification(false);
    
    developer.log('Music playback cleanup completed', name: 'MusicServiceManager');
  }
  
  /// 应用进入后台时的优化
  Future<void> onAppGoesToBackground() async {
    developer.log('Optimizing for app background', name: 'MusicServiceManager');
    
    // 确保服务持续运行
    await ensureServicePersistence();
    
    developer.log('App background optimization completed', name: 'MusicServiceManager');
  }
  
  /// 应用回到前台时的优化
  Future<void> onAppComesToForeground() async {
    developer.log('Optimizing for app foreground', name: 'MusicServiceManager');
    
    // 检查服务状态
    final isRunning = await isServiceRunning();
    if (!isRunning) {
      // 如果服务未运行，重新启动
      await startMusicService();
    }
    
    developer.log('App foreground optimization completed', name: 'MusicServiceManager');
  }
  
  /// 获取服务状态信息
  Future<Map<String, dynamic>> getServiceStatus() async {
    return {
      'isServiceRunning': await isServiceRunning(),
      'isWakeLockHeld': await isWakeLockHeld(),
    };
  }
  
  /// 更新前台服务通知状态
  /// 协调前台服务通知和音乐播放通知的显示
  Future<void> updateForegroundNotification(bool isMusicPlaying) async {
    try {
      await _serviceChannel.invokeMethod('updateForegroundNotification', {
        'isMusicPlaying': isMusicPlaying,
      });
      developer.log('Foreground notification updated, music playing: $isMusicPlaying', 
        name: 'MusicServiceManager');
    } catch (e) {
      developer.log('Failed to update foreground notification: $e', 
        name: 'MusicServiceManager');
    }
  }
} 