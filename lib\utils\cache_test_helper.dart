import 'package:flutter/foundation.dart';
import '../data/services/unified_cache_manager.dart';
import '../data/services/kuwo_music_service.dart';

/// 缓存系统测试辅助类
/// 用于验证新的缓存系统是否正常工作
class CacheTestHelper {
  static final UnifiedCacheManager _cacheManager = UnifiedCacheManager();
  static final KuwoMusicService _kuwoService = KuwoMusicService();

  /// 测试音乐文件缓存
  static Future<void> testMusicFileCache() async {
    if (kDebugMode) {
      print('=== 测试音乐文件缓存 ===');
    }

    const testSongId = '324244'; // 测试歌曲ID
    
    try {
      // 第一次获取（应该从API获取）
      final startTime1 = DateTime.now();
      final audioUrl1 = await _kuwoService.getSongPlayUrl(testSongId, quality: 'mq');
      final duration1 = DateTime.now().difference(startTime1);
      
      if (kDebugMode) {
        print('第一次获取: ${duration1.inMilliseconds}ms');
        print('音乐文件路径: $audioUrl1');
      }

      // 第二次获取（应该从缓存获取）
      final startTime2 = DateTime.now();
      final audioUrl2 = await _kuwoService.getSongPlayUrl(testSongId, quality: 'mq');
      final duration2 = DateTime.now().difference(startTime2);
      
      if (kDebugMode) {
        print('第二次获取: ${duration2.inMilliseconds}ms');
        print('音乐文件路径: $audioUrl2');
        print('缓存命中: ${duration2.inMilliseconds < duration1.inMilliseconds ? '是' : '否'}');
      }

      // 测试不同音质
      final hqUrl = await _kuwoService.getSongPlayUrl(testSongId, quality: 'hq');
      if (kDebugMode) {
        print('高音质文件: $hqUrl');
      }

    } catch (e) {
      if (kDebugMode) {
        print('音乐文件缓存测试失败: $e');
      }
    }
  }

  /// 测试歌词缓存
  static Future<void> testLyricsCache() async {
    if (kDebugMode) {
      print('\n=== 测试歌词缓存 ===');
    }

    const testSongId = '324244';
    
    try {
      // 第一次获取歌词
      final startTime1 = DateTime.now();
      final lyrics1 = await _kuwoService.getLyrics(testSongId);
      final duration1 = DateTime.now().difference(startTime1);
      
      if (kDebugMode) {
        print('第一次获取歌词: ${duration1.inMilliseconds}ms');
        print('歌词长度: ${lyrics1?.length ?? 0} 字符');
      }

      // 第二次获取歌词
      final startTime2 = DateTime.now();
      final lyrics2 = await _kuwoService.getLyrics(testSongId);
      final duration2 = DateTime.now().difference(startTime2);
      
      if (kDebugMode) {
        print('第二次获取歌词: ${duration2.inMilliseconds}ms');
        print('缓存命中: ${duration2.inMilliseconds < duration1.inMilliseconds ? '是' : '否'}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('歌词缓存测试失败: $e');
      }
    }
  }

  /// 测试专辑封面缓存
  static Future<void> testAlbumCoverCache() async {
    if (kDebugMode) {
      print('\n=== 测试专辑封面缓存 ===');
    }

    const testSongId = '324244';
    const testImageUrl = 'https://img1.kuwo.cn/star/albumcover/300/84/84/1341831084.jpg';
    
    try {
      // 第一次获取封面
      final startTime1 = DateTime.now();
      final image1 = await _cacheManager.getAlbumCover(testSongId, testImageUrl, requestedSize: 100);
      final duration1 = DateTime.now().difference(startTime1);
      
      if (kDebugMode) {
        print('第一次获取封面: ${duration1.inMilliseconds}ms');
        print('图片大小: ${image1?.length ?? 0} 字节');
      }

      // 第二次获取封面
      final startTime2 = DateTime.now();
      final image2 = await _cacheManager.getAlbumCover(testSongId, testImageUrl, requestedSize: 100);
      final duration2 = DateTime.now().difference(startTime2);
      
      if (kDebugMode) {
        print('第二次获取封面: ${duration2.inMilliseconds}ms');
        print('缓存命中: ${duration2.inMilliseconds < duration1.inMilliseconds ? '是' : '否'}');
      }

      // 测试智能尺寸匹配
      final startTime3 = DateTime.now();
      final image3 = await _cacheManager.getAlbumCover(testSongId, testImageUrl, requestedSize: 150);
      final duration3 = DateTime.now().difference(startTime3);
      
      if (kDebugMode) {
        print('请求150px封面: ${duration3.inMilliseconds}ms');
        print('智能匹配: ${duration3.inMilliseconds < 100 ? '使用了缓存' : '重新下载'}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('专辑封面缓存测试失败: $e');
      }
    }
  }

  /// 获取缓存统计信息
  static Future<void> printCacheStats() async {
    if (kDebugMode) {
      print('\n=== 缓存统计信息 ===');
    }

    try {
      final stats = _cacheManager.getCacheStats();
      
      if (kDebugMode) {
        print('音乐文件缓存:');
        print('  内存: ${stats['audioFiles']['memory']} 个');
        print('  本地: ${stats['audioFiles']['local']} 个');
        
        print('歌词缓存:');
        print('  内存: ${stats['lyrics']['memory']} 个');
        print('  本地: ${stats['lyrics']['local']} 个');
        
        print('图片缓存:');
        print('  内存: ${stats['images']['memory']} 个');
        print('  本地: ${stats['images']['local']} 个');
      }

      // 获取特定歌曲的缓存信息
      const testSongId = '324244';
      final songCache = await _cacheManager.getSongCacheInfo(testSongId);
      
      if (kDebugMode) {
        print('\n歌曲 $testSongId 的缓存:');
        print('  音乐文件: ${songCache['audioFiles']}');
        print('  图片: ${songCache['images']}');
        print('  歌词: ${songCache['hasLyrics'] ? '已缓存' : '未缓存'}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('获取缓存统计失败: $e');
      }
    }
  }

  /// 测试通知栏图片缓存
  static Future<void> testNotificationImageCache() async {
    if (kDebugMode) {
      print('\n=== 测试通知栏图片缓存 ===');
    }

    const testSongId = '324244';
    const testImageUrl = 'https://img4.kuwo.cn/star/albumcover/500/s4s60/91/4260853561.jpg';

    try {
      // 第一次获取（应该从网络获取）
      final startTime1 = DateTime.now();
      final image1 = await _cacheManager.getAlbumCover(testSongId, testImageUrl, requestedSize: 500);
      final duration1 = DateTime.now().difference(startTime1);

      if (kDebugMode) {
        print('第一次获取通知栏图片: ${duration1.inMilliseconds}ms');
        print('图片大小: ${image1?.length ?? 0} 字节');
      }

      // 第二次获取（应该从缓存获取）
      final startTime2 = DateTime.now();
      final image2 = await _cacheManager.getAlbumCover(testSongId, testImageUrl, requestedSize: 500);
      final duration2 = DateTime.now().difference(startTime2);

      if (kDebugMode) {
        print('第二次获取通知栏图片: ${duration2.inMilliseconds}ms');
        print('缓存命中: ${duration2.inMilliseconds < duration1.inMilliseconds ? '是' : '否'}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('通知栏图片缓存测试失败: $e');
      }
    }
  }

  /// 测试网络流量监控
  static Future<void> testNetworkTrafficReduction() async {
    if (kDebugMode) {
      print('\n=== 测试网络流量减少效果 ===');
    }

    const testSongId = '436126241';
    const testImageUrl = 'https://img4.kuwo.cn/star/albumcover/500/s4s55/86/4126158180.jpg';

    try {
      // 模拟多次请求同一张图片（模拟用户重复点击同一首歌）
      final requests = <Future<Uint8List?>>[];

      for (int i = 0; i < 5; i++) {
        requests.add(_cacheManager.getAlbumCover(testSongId, testImageUrl, requestedSize: 400));
      }

      final startTime = DateTime.now();
      final results = await Future.wait(requests);
      final duration = DateTime.now().difference(startTime);

      final successCount = results.where((r) => r != null).length;

      if (kDebugMode) {
        print('5次并发请求同一图片: ${duration.inMilliseconds}ms');
        print('成功获取: $successCount/5');
        print('预期效果: 只有第一次从网络下载，其余4次从缓存获取');
        print('性能提升: ${duration.inMilliseconds < 1000 ? '显著' : '需要优化'}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('网络流量测试失败: $e');
      }
    }
  }

  /// 运行完整的缓存测试
  static Future<void> runFullCacheTest() async {
    if (kDebugMode) {
      print('🎵 开始缓存系统测试...\n');
    }

    await testMusicFileCache();
    await testLyricsCache();
    await testAlbumCoverCache();
    await testNotificationImageCache();
    await testNetworkTrafficReduction();
    await printCacheStats();

    if (kDebugMode) {
      print('\n✅ 缓存系统测试完成！');
    }
  }

  /// 清理测试缓存
  static Future<void> clearTestCache() async {
    try {
      await _cacheManager.clearAllCache();
      if (kDebugMode) {
        print('🗑️ 测试缓存已清理');
      }
    } catch (e) {
      if (kDebugMode) {
        print('清理缓存失败: $e');
      }
    }
  }
}
