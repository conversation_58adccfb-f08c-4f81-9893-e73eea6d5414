<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    android:clickable="true"
    android:layout_width="fill_parent"
    android:layout_height="@dimen/a9o"
    android:background="@android:color/transparent"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <com.muzi.mzmusic.ui.minibar.ArcRelativeLayout
        android:id="@+id/du6"
        android:focusableInTouchMode="false"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/a9n"
        android:layout_marginBottom="@dimen/a9h"
        android:background="@drawable/skin_mini_player_bg"
        android:scaleType="fitXY"
        android:layout_alignParentBottom="true" />
    <com.muzi.mzmusic.ui.view.BottomPaintView
        android:id="@+id/du4"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/a9n"
        android:layout_alignTop="@+id/du6" />
    <RelativeLayout
        android:id="@+id/e08"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/a9n"
        android:layout_marginBottom="@dimen/a9h"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true">
        <FrameLayout
            android:id="@+id/f9r"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10.0dip"
            android:layout_centerVertical="true">
            <ProgressBar
                android:id="@+id/e0i"
                android:visibility="gone"
                android:layout_width="@dimen/ars"
                android:layout_height="@dimen/ars"
                android:indeterminateBehavior="repeat"
                android:indeterminateDrawable="@drawable/anim_loading" />
            <com.muzi.mzmusic.ui.minibar.ArcImageView
                android:id="@+id/e0s"
                android:layout_width="@dimen/ars"
                android:layout_height="@dimen/ars"
                android:src="@drawable/minibar_circle"
                android:scaleType="centerCrop" />
            <ImageView
                android:id="@+id/e0o"
                android:layout_width="@dimen/ars"
                android:layout_height="@dimen/ars"
                android:src="@drawable/minibar_btn_play"
                android:scaleType="centerCrop"
                android:contentDescription="@string/e2v" />
        </FrameLayout>
        <ImageView
            android:id="@+id/e0h"
            android:layout_width="30.0dip"
            android:layout_height="@dimen/ars"
            android:layout_marginRight="14.0dip"
            android:src="@drawable/minibar_btn_playlist_highlight"
            android:scaleType="fitXY"
            android:layout_toRightOf="@+id/f9r"
            android:layout_centerVertical="true"
            android:contentDescription="@string/e37"/>
            <!--android:alpha="0.3" />-->
    </RelativeLayout>
    <com.muzi.mzmusic.ui.minibar.BottomViewPager
        android:id="@+id/e0a"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/a9n"
        android:layout_marginRight="@dimen/aro"
        android:layout_marginBottom="@dimen/a9h"
        android:layout_toLeftOf="@+id/e08"
        android:layout_alignParentBottom="true" />
    <ImageView
        android:id="@+id/e0m"
        android:layout_width="@dimen/a9r"
        android:layout_height="@dimen/a9r"
        android:src="@drawable/minibar_pic_bottom_shadow"
        android:scaleType="fitXY" />
    <com.muzi.mzmusic.ui.minibar.AlbImageView
        android:id="@+id/pic"
        android:layout_width="@dimen/a9e"
        android:layout_height="@dimen/a9e"
        android:src="@drawable/fad"
        android:scaleType="centerCrop"
        android:layout_marginBottom="@dimen/a9h"
        android:importantForAccessibility="no" />
    <ImageView
        android:id="@+id/e0n"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/a9h"
        android:src="@drawable/minibar_pic_right_album"
        android:scaleType="fitXY"
        android:layout_toRightOf="@+id/pic"
        android:layout_alignParentBottom="true" />
</RelativeLayout>