import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';

/// 权限管理服务
/// 负责管理应用所需的各种权限
class PermissionService {
  // 单例模式
  static final PermissionService _instance = PermissionService._internal();
  
  factory PermissionService() {
    return _instance;
  }
  
  PermissionService._internal();
  
  /// 检查通知权限
  Future<bool> checkNotificationPermission() async {
    try {
      final status = await Permission.notification.status;
      // 移除重复的权限状态日志
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('PermissionService: Failed to check notification permission: $e');
      }
      return false;
    }
  }

  /// 请求通知权限
  Future<bool> requestNotificationPermission() async {
    try {
      if (kDebugMode) {
        print('PermissionService: Requesting notification permission...');
      }

      // 首先检查当前状态
      final currentStatus = await Permission.notification.status;
      if (kDebugMode) {
        print('PermissionService: Current notification status: $currentStatus');
      }

      if (currentStatus.isGranted) {
        return true;
      }

      // 如果是永久拒绝，直接打开设置
      if (currentStatus.isPermanentlyDenied) {
        if (kDebugMode) {
          print('PermissionService: Permission permanently denied, opening settings...');
        }
        await openAppSettings();
        return false;
      }

      // 请求权限
      final status = await Permission.notification.request();

      if (kDebugMode) {
        print('PermissionService: Notification permission request result: $status');
      }

      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('PermissionService: Failed to request notification permission: $e');
      }
      return false;
    }
  }
  
  /// 检查存储权限
  Future<bool> checkStoragePermission() async {
    try {
      final status = await Permission.storage.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('PermissionService: Failed to check storage permission: $e');
      }
      return false;
    }
  }
  
  /// 请求存储权限
  Future<bool> requestStoragePermission() async {
    try {
      final status = await Permission.storage.request();
      
      if (kDebugMode) {
        print('PermissionService: Storage permission status: $status');
      }
      
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('PermissionService: Failed to request storage permission: $e');
      }
      return false;
    }
  }
  
  /// 检查音频权限
  Future<bool> checkAudioPermission() async {
    try {
      final status = await Permission.audio.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('PermissionService: Failed to check audio permission: $e');
      }
      return false;
    }
  }
  
  /// 请求音频权限
  Future<bool> requestAudioPermission() async {
    try {
      final status = await Permission.audio.request();
      
      if (kDebugMode) {
        print('PermissionService: Audio permission status: $status');
      }
      
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('PermissionService: Failed to request audio permission: $e');
      }
      return false;
    }
  }
  
  /// 请求所有必要权限
  Future<Map<String, bool>> requestAllPermissions() async {
    final results = <String, bool>{};
    
    // 请求通知权限
    results['notification'] = await requestNotificationPermission();
    
    // 请求存储权限
    results['storage'] = await requestStoragePermission();
    
    // 请求音频权限
    results['audio'] = await requestAudioPermission();
    
    if (kDebugMode) {
      print('PermissionService: All permissions requested: $results');
    }
    
    return results;
  }
  
  /// 检查所有必要权限
  Future<Map<String, bool>> checkAllPermissions() async {
    final results = <String, bool>{};
    
    // 检查通知权限
    results['notification'] = await checkNotificationPermission();
    
    // 检查存储权限
    results['storage'] = await checkStoragePermission();
    
    // 检查音频权限
    results['audio'] = await checkAudioPermission();
    
    if (kDebugMode) {
      print('PermissionService: All permissions checked: $results');
    }
    
    return results;
  }
  
  /// 打开应用设置页面
  Future<bool> openSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      if (kDebugMode) {
        print('PermissionService: Failed to open app settings: $e');
      }
      return false;
    }
  }
  
  /// 检查是否所有关键权限都已授予
  Future<bool> areAllCriticalPermissionsGranted() async {
    final permissions = await checkAllPermissions();
    
    // 通知权限是关键权限
    return permissions['notification'] == true;
  }
  
  /// 获取权限状态描述
  String getPermissionStatusDescription(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return '已授权';
      case PermissionStatus.denied:
        return '已拒绝';
      case PermissionStatus.restricted:
        return '受限制';
      case PermissionStatus.limited:
        return '有限授权';
      case PermissionStatus.permanentlyDenied:
        return '永久拒绝';
      case PermissionStatus.provisional:
        return '临时授权';
      default:
        return '未知状态';
    }
  }
}
