import 'package:equatable/equatable.dart';
import 'song_model.dart';

class Playlist extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String? coverUrl;
  final String creatorId;
  final String creatorName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<Song> songs;
  final int playCount;
  final int likeCount;
  final int commentCount;
  final bool isPublic;
  final bool isOfficial;
  final bool isLiked;
  final List<String> tags;
  
  const Playlist({
    required this.id,
    required this.name,
    this.description,
    this.coverUrl,
    required this.creatorId,
    required this.creatorName,
    required this.createdAt,
    required this.updatedAt,
    this.songs = const [],
    this.playCount = 0,
    this.likeCount = 0,
    this.commentCount = 0,
    this.isPublic = true,
    this.isOfficial = false,
    this.isLiked = false,
    this.tags = const [],
  });
  
  Playlist copyWith({
    String? id,
    String? name,
    String? description,
    String? coverUrl,
    String? creatorId,
    String? creatorName,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<Song>? songs,
    int? playCount,
    int? likeCount,
    int? commentCount,
    bool? isPublic,
    bool? isOfficial,
    bool? isLiked,
    List<String>? tags,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      coverUrl: coverUrl ?? this.coverUrl,
      creatorId: creatorId ?? this.creatorId,
      creatorName: creatorName ?? this.creatorName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      songs: songs ?? this.songs,
      playCount: playCount ?? this.playCount,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      isPublic: isPublic ?? this.isPublic,
      isOfficial: isOfficial ?? this.isOfficial,
      isLiked: isLiked ?? this.isLiked,
      tags: tags ?? this.tags,
    );
  }
  
  // 从JSON映射到模型
  factory Playlist.fromJson(Map<String, dynamic> json) {
    return Playlist(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      coverUrl: json['cover_url'] as String?,
      creatorId: json['creator_id'] as String,
      creatorName: json['creator_name'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      songs: (json['songs'] as List<dynamic>?)
          ?.map((e) => Song.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      playCount: json['play_count'] as int? ?? 0,
      likeCount: json['like_count'] as int? ?? 0,
      commentCount: json['comment_count'] as int? ?? 0,
      isPublic: json['is_public'] as bool? ?? true,
      isOfficial: json['is_official'] as bool? ?? false,
      isLiked: json['is_liked'] as bool? ?? false,
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
    );
  }
  
  // 从模型映射到JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'cover_url': coverUrl,
      'creator_id': creatorId,
      'creator_name': creatorName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'songs': songs.map((song) => song.toJson()).toList(),
      'play_count': playCount,
      'like_count': likeCount,
      'comment_count': commentCount,
      'is_public': isPublic,
      'is_official': isOfficial,
      'is_liked': isLiked,
      'tags': tags,
    };
  }
  
  // 获取歌单总时长
  int get totalDuration {
    return songs.fold(0, (sum, song) => sum + song.duration);
  }
  
  // 格式化歌单总时长
  String get totalDurationFormatted {
    final totalSeconds = totalDuration;
    final hours = (totalSeconds / 3600).floor();
    final minutes = ((totalSeconds % 3600) / 60).floor();
    final seconds = totalSeconds % 60;
    
    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    }
  }
  
  // 获取歌单歌曲数量
  int get songCount => songs.length;
  
  // 格式化播放次数（如：1.2k, 3.5M）
  String get formattedPlayCount {
    if (playCount >= 1000000) {
      return '${(playCount / 1000000).toStringAsFixed(1)}M';
    } else if (playCount >= 1000) {
      return '${(playCount / 1000).toStringAsFixed(1)}k';
    } else {
      return playCount.toString();
    }
  }
  
  // 以下是Equatable所需的方法
  @override
  List<Object?> get props => [
    id,
    name,
    description,
    coverUrl,
    creatorId,
    creatorName,
    createdAt,
    updatedAt,
    songs,
    playCount,
    likeCount,
    commentCount,
    isPublic,
    isOfficial,
    isLiked,
    tags,
  ];
  
  @override
  bool get stringify => true;
} 