import 'package:flutter/material.dart';
import 'song_model.dart';

// 酷我音乐排行榜API响应模型
class KuwoRankingResponse {
  final bool success;
  final int code;
  final String message;
  final KuwoRankingParams params;
  final KuwoRankingData data;
  final String timestamp;

  KuwoRankingResponse({
    required this.success,
    required this.code,
    required this.message,
    required this.params,
    required this.data,
    required this.timestamp,
  });

  factory KuwoRankingResponse.fromJson(Map<String, dynamic> json) {
    return KuwoRankingResponse(
      success: json['success'] ?? false,
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      params: KuwoRankingParams.fromJson(json['params'] ?? {}),
      data: KuwoRankingData.fromJson(json['data'] ?? {}),
      timestamp: json['timestamp'] ?? '',
    );
  }
}

// API请求参数模型
class KuwoRankingParams {
  final int id;
  final int rn;
  final int pn;
  final int simple;
  final int forceRefresh;

  KuwoRankingParams({
    required this.id,
    required this.rn,
    required this.pn,
    required this.simple,
    required this.forceRefresh,
  });

  factory KuwoRankingParams.fromJson(Map<String, dynamic> json) {
    return KuwoRankingParams(
      id: json['id'] ?? 0,
      rn: json['rn'] ?? 20,
      pn: json['pn'] ?? 0,
      simple: json['simple'] ?? 1,
      forceRefresh: json['force_refresh'] ?? 0,
    );
  }
}

// 排行榜数据模型
class KuwoRankingData {
  final KuwoRankInfo rankInfo;
  final int totalSongs;
  final List<KuwoRankingSong> songs;

  KuwoRankingData({
    required this.rankInfo,
    required this.totalSongs,
    required this.songs,
  });

  factory KuwoRankingData.fromJson(Map<String, dynamic> json) {
    return KuwoRankingData(
      rankInfo: KuwoRankInfo.fromJson(json['rank_info'] ?? {}),
      totalSongs: json['total_songs'] ?? 0,
      songs: (json['songs'] as List<dynamic>?)
          ?.map((song) => KuwoRankingSong.fromJson(song))
          .toList() ?? [],
    );
  }
}

// 排行榜信息模型
class KuwoRankInfo {
  final String name;
  final String info;
  final String pic;
  final String pub;
  final int num;

  KuwoRankInfo({
    required this.name,
    required this.info,
    required this.pic,
    required this.pub,
    required this.num,
  });

  factory KuwoRankInfo.fromJson(Map<String, dynamic> json) {
    return KuwoRankInfo(
      name: json['name'] ?? '',
      info: json['info'] ?? '',
      pic: json['pic'] ?? '',
      pub: json['pub'] ?? '',
      num: json['num'] ?? 0,
    );
  }
}

// 排行榜歌曲模型
class KuwoRankingSong {
  final String id;
  final String name;
  final String artist;
  final String album;
  final String rankChange;
  final String highestRank;
  final String vip;

  KuwoRankingSong({
    required this.id,
    required this.name,
    required this.artist,
    required this.album,
    required this.rankChange,
    required this.highestRank,
    required this.vip,
  });

  factory KuwoRankingSong.fromJson(Map<String, dynamic> json) {
    return KuwoRankingSong(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      artist: json['artist'] ?? '',
      album: json['album'] ?? '',
      rankChange: json['rank_change']?.toString() ?? '0',
      highestRank: json['highest_rank']?.toString() ?? '0',
      vip: json['vip']?.toString() ?? '0',
    );
  }

  // 转换为应用内部的Song模型
  Song toSong({String? coverUrl, String? audioUrl}) {
    return Song(
      id: id,
      title: name,
      artistId: 'kuwo_artist_$id',
      artistName: artist,
      albumId: 'kuwo_album_$id',
      albumName: album,
      coverUrl: coverUrl ?? 'https://picsum.photos/200/200?random=${id.hashCode}',
      duration: 180, // 默认时长，实际需要从其他API获取
      releaseDate: DateTime.now(),
      audioUrl: audioUrl ?? '',
      isFeatured: vip == '1',
    );
  }

  // 获取排名变化图标
  IconData get rankChangeIcon {
    final change = int.tryParse(rankChange) ?? 0;
    if (change > 0) {
      return Icons.keyboard_arrow_up;
    } else if (change < 0) {
      return Icons.keyboard_arrow_down;
    } else {
      return Icons.remove;
    }
  }

  // 获取排名变化颜色
  Color get rankChangeColor {
    final change = int.tryParse(rankChange) ?? 0;
    if (change > 0) {
      return const Color(0xFF4CAF50); // 上升 - 绿色
    } else if (change < 0) {
      return const Color(0xFFFF6B6B); // 下降 - 红色
    } else {
      return Colors.grey; // 持平 - 灰色
    }
  }

  // 是否为VIP歌曲
  bool get isVip => vip == '1';
}

// 排行榜类型枚举
enum KuwoRankingType {
  hotHits(16, '热歌榜', 'HOT-HITS'),
  newSongs(17, '新歌榜', 'NEW-CHARTS'),
  shortVideo(158, '抖音榜', 'TIKTOK-HITS');

  const KuwoRankingType(this.id, this.title, this.subtitle);

  final int id;
  final String title;
  final String subtitle;

  // 获取排行榜的渐变色彩
  List<Color> get gradientColors {
    switch (this) {
      case KuwoRankingType.hotHits:
        return [
          const Color(0xFF8B5CF6),
          const Color(0xFF7C3AED),
          const Color(0xFF6D28D9),
        ];
      case KuwoRankingType.newSongs:
        return [
          const Color(0xFF10B981),
          const Color(0xFF059669),
          const Color(0xFF047857),
        ];
      case KuwoRankingType.shortVideo:
        return [
          const Color(0xFFEF4444),
          const Color(0xFFDC2626),
          const Color(0xFFB91C1C),
        ];
    }
  }
} 