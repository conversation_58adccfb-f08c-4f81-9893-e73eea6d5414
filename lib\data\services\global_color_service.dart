import 'dart:math';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/song_model.dart';
import '../../utils/color_extractor.dart';
import 'unified_cache_manager.dart';
import 'audio_player_service.dart';
import 'kuwo_music_service.dart';

/// 全局颜色管理服务
/// 确保所有页面使用相同的颜色提取结果，避免重复提取和不一致的颜色
class GlobalColorService extends ChangeNotifier {
  static final GlobalColorService _instance = GlobalColorService._internal();
  factory GlobalColorService() => _instance;
  GlobalColorService._internal() {
    // 监听图片缓存更新事件
    _imageCacheSubscription = UnifiedCacheManager.imageCacheUpdateStream.listen((songId) {
      _onImageCacheUpdated(songId);
    });
  }

  // 当前颜色主题
  ExtractedColors _currentColors = ExtractedColors.defaultColors();
  ExtractedColors get currentColors => _currentColors;

  // 当前歌曲ID，用于避免重复提取
  String? _currentSongId;
  String? get currentSongId => _currentSongId;

  // 是否正在提取颜色
  bool _isExtracting = false;
  bool get isExtracting => _isExtracting;

  // 颜色提取缓存
  final Map<String, ExtractedColors> _colorCache = {};

  // 图片缓存监听器
  StreamSubscription<String>? _imageCacheSubscription;

  /// 为歌曲提取颜色主题
  /// 如果已经为该歌曲提取过颜色，直接返回缓存结果
  Future<void> extractColorsForSong(Song song) async {
    // 如果是同一首歌且已有颜色，不重复提取
    if (song.id == _currentSongId && _currentColors != ExtractedColors.defaultColors()) {
      return;
    }

    // 检查缓存，如果有缓存直接使用，避免重复提取和随机颜色问题
    if (_colorCache.containsKey(song.id)) {
      _currentColors = _colorCache[song.id]!;
      _currentSongId = song.id;
      notifyListeners();
      if (kDebugMode) {
        print('GlobalColorService: Using cached colors for ${song.title}');
      }
      return;
    }

    // 播放时不立即提取颜色，只设置默认颜色
    _currentColors = ExtractedColors.defaultColors();
    _currentSongId = song.id;

    // 清除该歌曲的事件触发标志，允许重新触发缓存事件
    UnifiedCacheManager.clearEventFlag(song.id);

    notifyListeners();

    // 设置默认颜色，等待图片缓存
  }

  /// 执行实际的颜色提取
  Future<void> _performColorExtraction(Song song) async {
    // 避免并发提取
    if (_isExtracting) return;

    _isExtracting = true;
    notifyListeners();

    try {
      // 只使用高质量URL，不使用默认的coverUrl（可能是默认图标）
      String? imageUrl = song.highQualityCoverUrl;

      // 如果没有高质量URL，尝试获取
      if (imageUrl == null || imageUrl.isEmpty) {
        final kuwoService = KuwoMusicService();
        imageUrl = await kuwoService.getHighQualityAlbumCover(song.id);
      }

      // 如果还是没有URL，保持默认颜色
      if (imageUrl == null || imageUrl.isEmpty) {
        _isExtracting = false;
        notifyListeners();
        return;
      }

      // 提取颜色

      // 使用增强的颜色提取器，包含渐变效果
      final colors = await _extractColorsWithGradient(imageUrl!, song.id);
      
      // 缓存结果
      _colorCache[song.id] = colors;
      _currentColors = colors;
      _currentSongId = song.id;
      
      // 颜色提取完成
      
    } catch (e) {
      if (kDebugMode) {
        print('GlobalColorService: Failed to extract colors: $e');
      }
      
      _currentColors = ExtractedColors.defaultColors();
      _currentSongId = song.id;
    } finally {
      _isExtracting = false;
      notifyListeners();
    }
  }

  /// 处理图片缓存更新事件
  void _onImageCacheUpdated(String songId) {
    // 收到图片缓存更新事件

    // 如果当前歌曲的图片缓存完成，开始提取颜色
    if (songId == _currentSongId) {
      // 开始提取颜色

      // 获取当前歌曲信息并提取颜色
      final audioPlayerService = AudioPlayerService();
      final currentSong = audioPlayerService.currentSong;

      if (currentSong != null && currentSong.id == songId) {
        // 现在图片已经缓存，可以安全提取颜色
        _performColorExtraction(currentSong);
      }
    }
  }



  /// 清理资源
  void dispose() {
    _imageCacheSubscription?.cancel();
  }

  /// 增强的颜色提取，包含渐变效果
  /// 参考原版E4A组件的ColorUtil实现，特别处理白色和黑色情况
  Future<ExtractedColors> _extractColorsWithGradient(String imageUrl, String songId) async {
    // 使用新的颜色提取器，直接传入songId
    final baseColors = await ColorExtractor.extractColorsFromSong(songId, imageUrl);

    // 检查并处理特殊颜色情况（白色、黑色、灰色）- 使用基于歌曲ID的稳定算法
    final processedPrimary = _processSpecialColors(baseColors.primary, songId);

    // 应用原版渐变算法
    final gradientColors = _createGradientColors(processedPrimary);

    return ExtractedColors(
      primary: processedPrimary,
      background: _getBackgroundColor(processedPrimary),
      gradientStart: gradientColors.start,
      gradientEnd: gradientColors.end,
      textColor: _getTextColor(processedPrimary),
      progressColor: _getProgressColor(processedPrimary),
    );
  }

  /// 处理特殊颜色情况（白色、黑色、灰色）
  /// 使用基于歌曲ID的稳定算法，避免随机颜色闪烁
  Color _processSpecialColors(Color color, String songId) {
    final hsl = HSLColor.fromColor(color);

    // 检查是否为白色或接近白色（亮度 > 0.85）
    if (hsl.lightness > 0.85) {
      if (kDebugMode) {
        print('GlobalColorService: Detected light color, applying dark alternative');
      }
      // 使用基于歌曲ID的稳定深色方案
      return _getStableDarkAlternativeColor(songId);
    }

    // 检查是否为黑色或接近黑色（亮度 < 0.15）
    if (hsl.lightness < 0.15) {
      if (kDebugMode) {
        print('GlobalColorService: Detected dark color, applying light alternative');
      }
      // 使用基于歌曲ID的稳定浅色方案
      return _getStableLightAlternativeColor(songId);
    }

    // 检查是否为灰色（饱和度 < 0.1）
    if (hsl.saturation < 0.1) {
      if (kDebugMode) {
        print('GlobalColorService: Detected gray color, applying colorful alternative');
      }
      // 使用基于歌曲ID的稳定有色彩方案
      return _getStableColorfulAlternativeColor(songId);
    }

    return color;
  }

  /// 获取基于歌曲ID的稳定深色替代颜色（避免随机闪烁）
  Color _getStableDarkAlternativeColor(String songId) {
    final alternatives = [
      const Color(0xFF2E3440), // 深蓝灰
      const Color(0xFF3B4252), // 深灰蓝
      const Color(0xFF434C5E), // 中灰蓝
      const Color(0xFF5E81AC), // 蓝色
      const Color(0xFF81A1C1), // 浅蓝
    ];
    // 使用歌曲ID的哈希值来选择稳定的颜色，避免随机性
    final index = songId.hashCode.abs() % alternatives.length;
    return alternatives[index];
  }

  /// 获取基于歌曲ID的稳定浅色替代颜色（避免随机闪烁）
  Color _getStableLightAlternativeColor(String songId) {
    final alternatives = [
      const Color(0xFF88C0D0), // 浅青
      const Color(0xFF81A1C1), // 浅蓝
      const Color(0xFF5E81AC), // 蓝色
      const Color(0xFFBF616A), // 红色
      const Color(0xFFD08770), // 橙色
    ];
    // 使用歌曲ID的哈希值来选择稳定的颜色，避免随机性
    final index = songId.hashCode.abs() % alternatives.length;
    return alternatives[index];
  }

  /// 获取基于歌曲ID的稳定有色彩替代颜色（避免随机闪烁）
  Color _getStableColorfulAlternativeColor(String songId) {
    final alternatives = [
      const Color(0xFF5E81AC), // 蓝色
      const Color(0xFF88C0D0), // 青色
      const Color(0xFFBF616A), // 红色
      const Color(0xFFD08770), // 橙色
      const Color(0xFFEBCB8B), // 黄色
      const Color(0xFFA3BE8C), // 绿色
      const Color(0xFFB48EAD), // 紫色
    ];
    // 使用歌曲ID的哈希值来选择稳定的颜色，避免随机性
    final index = songId.hashCode.abs() % alternatives.length;
    return alternatives[index];
  }

  /// 创建渐变颜色 - 参考原版ColorUtil，增强版本
  GradientColors _createGradientColors(Color primaryColor) {
    final hsl = HSLColor.fromColor(primaryColor);

    // 确保有足够的对比度和视觉效果
    Color startColor, endColor;

    // 根据亮度调整渐变策略
    if (hsl.lightness > 0.7) {
      // 亮色：从较深开始到较浅结束
      startColor = hsl
          .withLightness((hsl.lightness * 0.6).clamp(0.0, 1.0))
          .withSaturation((hsl.saturation * 1.2).clamp(0.0, 1.0))
          .toColor();
      endColor = hsl
          .withLightness((hsl.lightness * 0.9).clamp(0.0, 1.0))
          .withSaturation((hsl.saturation * 0.8).clamp(0.0, 1.0))
          .toColor();
    } else if (hsl.lightness < 0.3) {
      // 暗色：从较暗开始到较亮结束
      startColor = hsl
          .withLightness((hsl.lightness * 0.8).clamp(0.0, 1.0))
          .toColor();
      endColor = hsl
          .withLightness((hsl.lightness * 2.0).clamp(0.0, 1.0))
          .withSaturation((hsl.saturation * 1.1).clamp(0.0, 1.0))
          .toColor();
    } else {
      // 中等亮度：标准渐变
      startColor = hsl
          .withLightness((hsl.lightness * 0.8).clamp(0.0, 1.0))
          .withSaturation((hsl.saturation * 1.1).clamp(0.0, 1.0))
          .toColor();
      endColor = hsl
          .withLightness((hsl.lightness * 1.2).clamp(0.0, 1.0))
          .withSaturation((hsl.saturation * 0.9).clamp(0.0, 1.0))
          .toColor();
    }

    return GradientColors(start: startColor, end: endColor);
  }

  /// 获取背景颜色 - 增强版本，确保良好的对比度
  Color _getBackgroundColor(Color primaryColor) {
    final hsl = HSLColor.fromColor(primaryColor);

    // 检查特殊情况
    final isNearWhite = hsl.saturation <= 0.05 && hsl.lightness >= 0.95;
    final isNearBlack = hsl.lightness <= 0.09;
    final isGray = hsl.saturation <= 0.1;

    if (isNearWhite) {
      // 白色背景使用深色调
      return const Color(0xFF2E3440);
    } else if (isNearBlack) {
      // 黑色背景使用中等亮度
      return const Color(0xFF5E81AC);
    } else if (isGray) {
      // 灰色背景使用有色彩的深色
      return const Color(0xFF434C5E);
    } else {
      // 根据原色亮度调整背景
      double targetLightness;
      if (hsl.lightness > 0.7) {
        // 亮色使用深背景
        targetLightness = 0.25;
      } else if (hsl.lightness < 0.3) {
        // 暗色使用中等背景
        targetLightness = 0.4;
      } else {
        // 中等亮度使用适中背景
        targetLightness = 0.35;
      }

      return hsl
          .withLightness(targetLightness)
          .withSaturation((hsl.saturation * 0.8).clamp(0.0, 1.0))
          .toColor();
    }
  }

  /// 获取文本颜色
  Color _getTextColor(Color backgroundColor) {
    // 计算亮度
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// 获取进度条颜色
  Color _getProgressColor(Color primaryColor) {
    final hsl = HSLColor.fromColor(primaryColor);

    // 如果原色太暗，使用更亮的版本
    if (hsl.lightness < 0.3) {
      return hsl.withLightness(0.6).withSaturation(0.8).toColor();
    }

    // 如果原色太亮，使用稍暗的版本
    if (hsl.lightness > 0.8) {
      return hsl.withLightness(0.5).withSaturation(0.8).toColor();
    }

    // 增强饱和度
    return hsl.withSaturation((hsl.saturation * 1.2).clamp(0.0, 1.0)).toColor();
  }

  /// 清除缓存
  void clearCache() {
    _colorCache.clear();
    if (kDebugMode) {
      print('GlobalColorService: Cache cleared');
    }
  }

  /// 获取当前颜色是否为默认颜色
  bool get isDefaultColors => _currentColors == ExtractedColors.defaultColors();
}

/// 渐变颜色数据类
class GradientColors {
  final Color start;
  final Color end;

  const GradientColors({
    required this.start,
    required this.end,
  });
}
