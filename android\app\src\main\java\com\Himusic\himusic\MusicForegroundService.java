package com.Himusic.himusic;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.Binder;
import android.os.PowerManager;
import androidx.annotation.Nullable;
import io.flutter.plugin.common.MethodChannel;
import android.util.Log;
import android.app.Notification;

/**
 * 音乐播放前台服务
 * 确保音乐播放不被系统杀死，支持WakeLock管理和持久化运行
 */
public class MusicForegroundService extends Service {
    private static final String TAG = "MusicForegroundService";
    private static final int FOREGROUND_SERVICE_ID = 1000;
    private static final String WAKELOCK_TAG = "HiMusic:MusicPlayback";
    
    private MusicNotificationManager notificationManager;
    private final IBinder binder = new MusicServiceBinder();
    
    // WakeLock 管理
    private PowerManager.WakeLock wakeLock;
    private PowerManager powerManager;
    
    // 服务状态
    private boolean isServiceRunning = false;
    private boolean isWakeLockAcquired = false;
    
    public class MusicServiceBinder extends Binder {
        MusicForegroundService getService() {
            return MusicForegroundService.this;
        }
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service onCreate");
        
        // 初始化PowerManager和WakeLock
        powerManager = (PowerManager) getSystemService(POWER_SERVICE);
        if (powerManager != null) {
            // 使用PARTIAL_WAKE_LOCK，允许CPU继续运行但可以关闭屏幕
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK, 
                WAKELOCK_TAG
            );
            // 设置WakeLock引用计数，防止被意外释放
            wakeLock.setReferenceCounted(false);
        }
        
        // 初始化通知管理器
        notificationManager = new MusicNotificationManager(this);
        notificationManager.initialize();
        
        isServiceRunning = true;
        Log.d(TAG, "Service initialized successfully");
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Service onStartCommand");
        
        // 启动前台服务，使用音乐通知作为前台通知
        if (notificationManager != null) {
            try {
            // 创建一个默认通知来启动前台服务
            startForeground(FOREGROUND_SERVICE_ID, 
                notificationManager.createDefaultNotification());
                Log.d(TAG, "Foreground service started successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to start foreground service", e);
            }
        }
        
        // 获取WakeLock，确保服务在后台持续运行
        acquireWakeLock();
        
        // 处理特定的Intent动作
        if (intent != null) {
            String action = intent.getAction();
            if (action != null) {
                handleServiceAction(action, intent);
            }
        }
        
        // 返回 START_STICKY 确保服务被杀死后会重启
        // 这样即使系统内存不足杀死服务，系统也会尝试重新启动服务
        return START_STICKY;
    }
    
    /**
     * 处理服务动作
     */
    private void handleServiceAction(String action, Intent intent) {
        switch (action) {
            case "START_MUSIC_SERVICE":
                Log.d(TAG, "Starting music service");
                break;
            case "STOP_MUSIC_SERVICE":
                Log.d(TAG, "Stopping music service");
                stopSelf();
                break;
            case "ACQUIRE_WAKELOCK":
                acquireWakeLock();
                break;
            case "RELEASE_WAKELOCK":
                releaseWakeLock();
                break;
            default:
                Log.d(TAG, "Unknown action: " + action);
                break;
        }
    }
    
    /**
     * 获取WakeLock，防止设备休眠影响音乐播放
     */
    public void acquireWakeLock() {
        if (wakeLock != null && !isWakeLockAcquired) {
            try {
                wakeLock.acquire();
                isWakeLockAcquired = true;
                Log.d(TAG, "WakeLock acquired successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to acquire WakeLock", e);
            }
        }
    }
    
    /**
     * 释放WakeLock
     */
    public void releaseWakeLock() {
        if (wakeLock != null && isWakeLockAcquired) {
            try {
                wakeLock.release();
                isWakeLockAcquired = false;
                Log.d(TAG, "WakeLock released successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to release WakeLock", e);
            }
        }
    }
    
    /**
     * 检查WakeLock状态
     */
    public boolean isWakeLockHeld() {
        return wakeLock != null && wakeLock.isHeld();
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "Service onBind");
        return binder;
    }
    
    @Override
    public void onDestroy() {
        Log.d(TAG, "Service onDestroy");
        
        isServiceRunning = false;
        
        // 释放WakeLock
        releaseWakeLock();
        
        // 清理通知管理器
        if (notificationManager != null) {
            notificationManager.cleanup();
        }
        
        super.onDestroy();
        Log.d(TAG, "Service destroyed");
    }
    
    /**
     * 当系统内存不足时被调用
     */
    @Override
    public void onLowMemory() {
        Log.w(TAG, "System is low on memory");
        super.onLowMemory();
    }
    
    /**
     * 当系统清理内存时被调用
     */
    @Override
    public void onTrimMemory(int level) {
        Log.w(TAG, "System is trimming memory, level: " + level);
        
        // 根据内存压力级别决定是否需要释放一些资源
        switch (level) {
            case TRIM_MEMORY_RUNNING_CRITICAL:
            case TRIM_MEMORY_COMPLETE:
                // 内存极度紧张，但我们仍然保持核心功能运行
                Log.w(TAG, "Critical memory situation, but keeping music service alive");
                break;
            default:
                break;
        }
        
        super.onTrimMemory(level);
    }
    
    /**
     * 设置方法通道
     */
    public void setMethodChannel(MethodChannel channel) {
        if (notificationManager != null) {
            notificationManager.setMethodChannel(channel);
        }
    }
    
    /**
     * 获取通知管理器
     */
    public MusicNotificationManager getNotificationManager() {
        return notificationManager;
    }
    
    /**
     * 检查服务是否正在运行
     */
    public boolean isServiceRunning() {
        return isServiceRunning;
    }
    
    /**
     * 强制保持服务运行（在播放音乐时调用）
     */
    public void ensureServicePersistence() {
        acquireWakeLock();
        
        // 确保前台通知始终存在，不被音乐通知覆盖
        try {
            startForeground(FOREGROUND_SERVICE_ID, 
                notificationManager.createDefaultNotification());
            Log.d(TAG, "Service persistence ensured with foreground notification");
        } catch (Exception e) {
            Log.e(TAG, "Failed to ensure foreground service", e);
        }
    }
    
    /**
     * 允许服务可以被系统管理（在暂停播放时调用）
     */
    public void allowServiceManagement() {
        // 即使允许系统管理，我们仍然保持前台服务状态
        // 这确保了服务不会被轻易杀死
        Log.d(TAG, "Allowing system to manage service resources while keeping foreground state");
        
        // 可以选择性释放WakeLock，但保持前台服务
        // releaseWakeLock(); // 如果需要完全释放可以取消注释
    }
    
    /**
     * 更新前台通知（当音乐播放状态改变时）
     */
    public void updateForegroundNotification(boolean isMusicPlaying) {
        if (notificationManager != null) {
            try {
                Notification notification = notificationManager.createDefaultNotification();
                
                // 根据播放状态调整通知内容
                if (isMusicPlaying) {
                    // 音乐播放中，可以使用更低调的前台服务通知
                    startForeground(FOREGROUND_SERVICE_ID, notification);
                } else {
                    // 没有播放音乐，使用标准的前台服务通知
                    startForeground(FOREGROUND_SERVICE_ID, notification);
                }
                
                Log.d(TAG, "Foreground notification updated, music playing: " + isMusicPlaying);
            } catch (Exception e) {
                Log.e(TAG, "Failed to update foreground notification", e);
            }
        }
    }
}
