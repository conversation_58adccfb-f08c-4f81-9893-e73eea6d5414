@echo off
echo 请选择运行设备:
echo 1. Android 设备
echo 2. iOS 模拟器
echo 3. Chrome 浏览器
echo 4. 显示所有设备并选择

set /p choice="请输入选项 (默认: 1): "

if "%choice%"=="" set choice=1

if "%choice%"=="1" (
  echo 在 Android 设备上运行...
  flutter run -d android
) else if "%choice%"=="2" (
  echo 在 iOS 模拟器上运行...
  flutter run -d ios
) else if "%choice%"=="3" (
  echo 在 Chrome 浏览器上运行...
  flutter run -d chrome
) else if "%choice%"=="4" (
  echo 可用设备列表:
  flutter devices
  echo.
  set /p device_id="请输入设备ID: "
  flutter run -d %device_id%
) else (
  echo 无效选项，默认使用 Android 设备
  flutter run -d android
) 