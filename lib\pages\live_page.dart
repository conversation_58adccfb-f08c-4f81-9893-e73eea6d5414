import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../core/theme/app_colors.dart';
import '../core/theme/app_text_styles.dart';
import '../data/providers/mock_data_provider.dart';
import '../data/models/models.dart';
import '../widgets/common/custom_app_bar.dart';
import '../widgets/live/live_category_tabs.dart';
import '../widgets/live/live_card.dart';
import '../widgets/live/featured_live_card.dart';
import '../widgets/home/<USER>';

class LivePage extends StatefulWidget {
  const LivePage({super.key});

  @override
  State<LivePage> createState() => _LivePageState();
}

class _LivePageState extends State<LivePage> with SingleTickerProviderStateMixin {
  late MockDataProvider _dataProvider;
  late List<Live> _hotLives;
  late List<Live> _upcomingLives;
  late TabController _tabController;
  
  final List<String> _tabs = [
    'All',
    'Music',
    'Talk',
    'Gaming',
    'Sports',
  ];
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _dataProvider = Provider.of<MockDataProvider>(context, listen: false);
    _loadData();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  void _loadData() {
    _hotLives = _dataProvider.getHotLives();
    _upcomingLives = _dataProvider.getUpcomingLives();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // 自定义应用栏
            CustomAppBar(
              title: 'Live',
              actions: [
                IconButton(
                  icon: const Icon(
                    Icons.search,
                    color: AppColors.textPrimary,
                  ),
                  onPressed: () {
                    // TODO: 导航到搜索页面
                  },
                ),
              ],
            ),
            
            // 分类标签
            LiveCategoryTabs(
              tabs: _tabs,
              controller: _tabController,
            ),
            
            // 主内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: List.generate(_tabs.length, (index) {
                  return _buildTabContent(index);
                }),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: 开始直播
        },
        backgroundColor: AppColors.primaryColor,
        child: const Icon(
          Icons.videocam,
          color: AppColors.grey3,
        ),
      ),
    );
  }
  
  Widget _buildTabContent(int tabIndex) {
    // 所有标签显示相同内容，在实际应用中可以根据标签显示不同内容
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            
            // 推荐主播
            SectionHeader(
              title: 'Featured Live',
              onMoreTap: () {
                // TODO: 查看更多推荐主播
              },
            ),
            
            if (_hotLives.isNotEmpty)
              FeaturedLiveCard(
                live: _hotLives.first,
                onTap: () {
                  // TODO: 观看直播
                },
              ),
            
            const SizedBox(height: 24),
            
            // 热门直播
            SectionHeader(
              title: 'Hot Live',
              onMoreTap: () {
                // TODO: 查看更多热门直播
              },
            ),
            
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: _hotLives.length,
              itemBuilder: (context, index) {
                return LiveCard(
                  live: _hotLives[index],
                  onTap: () {
                    // TODO: 观看直播
                  },
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // 即将开始的直播
            SectionHeader(
              title: 'Upcoming Live',
              onMoreTap: () {
                // TODO: 查看更多即将开始的直播
              },
            ),
            
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _upcomingLives.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: LiveCard(
                    live: _upcomingLives[index],
                    isHorizontal: true,
                    onTap: () {
                      // TODO: 设置提醒
                    },
                  ),
                );
              },
            ),
            
            const SizedBox(height: 80), // 为FloatingActionButton留出空间
          ],
        ),
      ),
    );
  }
} 