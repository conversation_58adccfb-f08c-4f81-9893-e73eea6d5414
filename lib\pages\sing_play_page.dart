import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../core/theme/app_colors.dart';
import '../core/theme/app_text_styles.dart';
import '../data/providers/mock_data_provider.dart';
import '../data/models/models.dart';
import '../widgets/common/custom_app_bar.dart';
import '../widgets/sing/sing_feature_card.dart';
import '../widgets/sing/sing_room_card.dart';
import '../widgets/home/<USER>';

class SingPlayPage extends StatefulWidget {
  const SingPlayPage({super.key});

  @override
  State<SingPlayPage> createState() => _SingPlayPageState();
}

class _SingPlayPageState extends State<SingPlayPage> with SingleTickerProviderStateMixin {
  late MockDataProvider _dataProvider;
  late List<Live> _singRooms;
  late TabController _tabController;
  
  final List<String> _tabs = [
    'Online Rooms',
    'Recent',
    'Recommended',
    'Friends',
    'Songs',
    'My Rooms',
  ];
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _dataProvider = Provider.of<MockDataProvider>(context, listen: false);
    _loadData();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  void _loadData() {
    // 使用直播数据模拟K歌房间
    _singRooms = _dataProvider.getHotLives();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // 自定义应用栏
            const CustomAppBar(
              title: 'Sing & Play',
            ),
            
            // 功能区
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  SingFeatureCard(
                    title: 'High Quality\nKaraoke',
                    color: Colors.pink.shade100,
                    icon: Icons.mic,
                    onTap: () {
                      // TODO: 打开K歌功能
                    },
                  ),
                  SingFeatureCard(
                    title: 'I Sing\nCover',
                    color: Colors.blue.shade100,
                    icon: Icons.music_note,
                    onTap: () {
                      // TODO: 打开翻唱功能
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SingFeatureCard(
                  title: 'AI\nCompanion',
                  color: Colors.green.shade100,
                  icon: Icons.smart_toy,
                  onTap: () {
                    // TODO: 打开AI伴唱功能
                  },
                ),
                SingFeatureCard(
                  title: 'Practice\nMode',
                  color: Colors.orange.shade100,
                  icon: Icons.music_video,
                  onTap: () {
                    // TODO: 打开练习模式
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 标签栏
            TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: AppColors.primaryColor,
              unselectedLabelColor: AppColors.textSecondary,
              indicatorColor: AppColors.primaryColor,
              indicatorSize: TabBarIndicatorSize.label,
              labelStyle: AppTextStyles.tabText,
              unselectedLabelStyle: AppTextStyles.tabText,
              tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
            ),
            
            // 主内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: List.generate(_tabs.length, (index) {
                  return _buildTabContent(index);
                }),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: 创建K歌房间
        },
        backgroundColor: AppColors.primaryColor,
        child: const Icon(
          Icons.add,
          color: AppColors.grey3,
        ),
      ),
    );
  }
  
  Widget _buildTabContent(int tabIndex) {
    // 简单起见，所有标签显示相同内容
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            
            // 热门K歌房
            SectionHeader(
              title: 'Popular Singing Rooms',
              onMoreTap: () {
                // TODO: 查看更多热门K歌房
              },
            ),
            
            // 房间列表
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.85,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: _singRooms.length,
              itemBuilder: (context, index) {
                return SingRoomCard(
                  room: _singRooms[index],
                  onTap: () {
                    // TODO: 加入K歌房
                  },
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // 推荐直播
            SectionHeader(
              title: 'Recommended Live',
              onMoreTap: () {
                // TODO: 查看更多推荐直播
              },
            ),
            
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _singRooms.take(3).length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: SingRoomCard(
                    room: _singRooms[index],
                    isHorizontal: true,
                    onTap: () {
                      // TODO: 加入直播
                    },
                  ),
                );
              },
            ),
            
            const SizedBox(height: 80), // 为FloatingActionButton留出空间
          ],
        ),
      ),
    );
  }
} 