import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../data/models/playlist_model.dart';

class PlaylistCard extends StatelessWidget {
  final Playlist playlist;
  final VoidCallback? onTap;
  final double width;
  
  const PlaylistCard({
    super.key,
    required this.playlist,
    this.onTap,
    this.width = 150,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: 190,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        clipBehavior: Clip.antiAlias,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 封面图片 - 使用默认图片，歌单不使用歌曲缓存
            SizedBox(
              width: width,
              height: width * 0.75,
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.cardBackground,
                  image: playlist.coverUrl != null
                      ? DecorationImage(
                          image: NetworkImage(playlist.coverUrl!),
                          fit: BoxFit.cover,
                          onError: (exception, stackTrace) {
                            // 图片加载失败时的处理
                          },
                        )
                      : null,
                ),
                child: playlist.coverUrl == null
                    ? const Center(
                        child: Icon(Icons.music_note, size: 40, color: Colors.grey),
                      )
                    : null,
              ),
            ),
            
            // 歌单信息
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 歌单名称
                    Text(
                      playlist.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    // 创建者名称
                    Text(
                      'By ${playlist.creatorName}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    // 播放和点赞信息
                    SizedBox(
                      width: double.infinity,
                      child: Row(
                        children: [
                          // 播放次数
                          Icon(
                            Icons.headphones,
                            size: 12,
                            color: AppColors.textSecondary,
                          ),
                          SizedBox(width: 2),
                          Text(
                            playlist.formattedPlayCount,
                            style: const TextStyle(
                              fontSize: 10,
                              color: AppColors.textSecondary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          Spacer(),
                          
                          // 点赞数
                          Icon(
                            Icons.favorite,
                            size: 12,
                            color: Colors.red[300],
                          ),
                          SizedBox(width: 2),
                          Text(
                            '${playlist.likeCount}',
                            style: const TextStyle(
                              fontSize: 10,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 