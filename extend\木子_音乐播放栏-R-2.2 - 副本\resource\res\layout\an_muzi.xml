<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    android:gravity="center_vertical"
    android:paddingRight="5.0dip"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:paddingBottom="3.0dip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/arq">
        <FrameLayout
            android:id="@+id/e0c"
            android:paddingLeft="@dimen/a9u"
            android:focusable="true"
            android:layout_width="0.0dip"
            android:layout_height="wrap_content"
            android:layout_weight="1.0">
            <com.muzi.mzmusic.ui.minibar.video.BottomTextView
                android:textSize="13.0dip"
                android:textColor="@android:color/white"
                android:ellipsize="marquee"
                android:id="@+id/e0d"
                android:focusable="true"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:text="@string/bxx"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"/>
            <!--<ViewStub
                android:id="@+id/h2r"
                android:visibility="gone"
                android:layout="@layout/anb"
                android:inflatedId="@+id/h2r"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content" />
                com.tencent.qqmusic.ui.minibar.video.VideoMinibarMarqueeView-->
        </FrameLayout>
        <ImageView
            android:layout_gravity="center_vertical"
            android:id="@+id/e0e"
            android:padding="4.0dip"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/pay_icon_in_minibar" />
        <ImageView
            android:layout_gravity="center_vertical"
            android:id="@+id/e0b"
            android:padding="4.0dip"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/limit_free_icon_in_minibar" />
        <ImageView
            android:layout_gravity="center_vertical"
            android:id="@+id/e0j"
            android:padding="4.0dip"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/long_track_vip_icon_minibar" />
    </LinearLayout>
</RelativeLayout>