import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:just_audio/just_audio.dart';
import 'package:palette_generator/palette_generator.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

import '../core/theme/app_colors.dart';
import '../core/theme/app_text_styles.dart';
import '../data/models/song_model.dart';
import '../data/models/lyric_model.dart';
import '../widgets/player/player_controls.dart';
import '../widgets/player/lyrics_view.dart';
import '../widgets/common/custom_app_bar.dart';
import '../data/providers/mock_data_provider.dart';
import '../data/providers/player_page_provider.dart';
import '../data/providers/mini_player_provider.dart';
import '../data/services/audio_player_service.dart';
import '../data/services/playlist_manager.dart';
import '../data/services/kuwo_music_service.dart';
import '../data/services/unified_cache_manager.dart';
import '../data/services/global_color_service.dart';
import '../data/services/enhanced_playlist_manager.dart';
import '../widgets/marquee_text.dart';
import '../widgets/common/cached_album_image.dart';
import '../widgets/player/playlist_bottom_sheet.dart';

class PlayerPage extends StatefulWidget {
  final Song? song;
  final bool autoPlay;
  
  const PlayerPage({
    super.key,
    this.song,
    this.autoPlay = false,
  });

  @override
  State<PlayerPage> createState() => _PlayerPageState();
}

class _PlayerPageState extends State<PlayerPage> {
  Song? _currentSong;
  bool _isPlaying = false;
  bool _isShuffle = false;
  bool _isRepeat = false;
  bool _isFavorite = false;
  double _progress = 0.0;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  final AudioPlayerService _audioPlayerService = AudioPlayerService();
  final PlaylistManager _playlistManager = PlaylistManager();
  final KuwoMusicService _musicService = KuwoMusicService();
  final GlobalColorService _globalColorService = GlobalColorService();
  late EnhancedPlaylistManager _enhancedPlaylistManager;
  Lyric? _lyric;
  bool _isLoadingLyric = false;
  bool _isInitialized = false;
  bool _showLyrics = false;
  late PageController _pageController;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<String>? _lyricsCacheSubscription;

  // 歌词滚动控制器
  final ScrollController _lyricsScrollController = ScrollController();

  // 歌词拖动相关状态
  bool _isDraggingLyrics = false;
  int _draggedLyricIndex = -1;
  Timer? _dragEndTimer;
  bool _isAutoScrolling = false; // 标记是否正在自动滚动

  // 进度条拖动相关状态
  bool _isDraggingSlider = false; // 是否正在拖动进度条
  double _tempProgress = 0.0; // 拖动时的临时进度值

  // 歌词加载状态跟踪
  String? _currentLoadedLyricSongId; // 当前已加载歌词的歌曲ID

  // 移除独立的颜色变量，改为使用全局颜色服务


  
  @override
  void initState() {
    super.initState();

    _pageController = PageController(initialPage: 0);

    // 初始化播放页面

    // 获取播放页面状态
    final playerPageProvider = Provider.of<PlayerPageProvider>(context, listen: false);
    _showLyrics = playerPageProvider.showLyrics;

    // 获取增强播放列表管理器
    _enhancedPlaylistManager = Provider.of<EnhancedPlaylistManager>(context, listen: false);

    // 监听歌词缓存更新事件
    _lyricsCacheSubscription = UnifiedCacheManager.lyricsCacheUpdateStream.listen(_onLyricsCacheUpdated);

    // 使用 WidgetsBinding.instance.addPostFrameCallback 来延迟初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initSongData();
      _initAudioPlayer();
    });
  }
  
  void _initSongData() {
    // 初始化歌曲数据

    final dataProvider = Provider.of<MockDataProvider>(context, listen: false);
    final playerPageProvider = Provider.of<PlayerPageProvider>(context, listen: false);

    if (widget.song != null) {
      // 使用传入的歌曲



      _currentSong = widget.song!;

      // 检查是否需要重新初始化
      if (!playerPageProvider.needsReinitialization(widget.song)) {
        // 如果不需要重新初始化，使用保存的状态
        _showLyrics = playerPageProvider.showLyrics;
        if (kDebugMode) {
          print('PlayerPage: Using saved state');
        }
      }
    } else {
      // 如果没有传入歌曲，使用当前播放列表中的歌曲或随机歌曲
      final currentPlaylistSong = _playlistManager.currentSong;
      if (currentPlaylistSong != null) {
        if (kDebugMode) {
          print('PlayerPage: Using current playlist song: ${currentPlaylistSong.title}');
        }
        _currentSong = currentPlaylistSong;
      } else {
        final randomSong = dataProvider.getRandomSong();
        if (kDebugMode) {
          print('PlayerPage: Using random song: ${randomSong.title}');
        }
        _currentSong = randomSong;
      }
    }

    if (_currentSong != null) {
      _isFavorite = _currentSong!.isLiked;
      _duration = Duration(seconds: _currentSong!.duration);

      // 更新provider状态
      playerPageProvider.updateCurrentSong(_currentSong!);

      // 使用全局颜色服务提取颜色
      _globalColorService.extractColorsForSong(_currentSong!);
    }

    // 标记为已初始化
    _isInitialized = true;
  }
  

  
  // 静态变量：记录正在请求歌词的歌曲ID，防止同一首歌的重复请求
  static final Set<String> _loadingLyricsSet = <String>{};

  Future<void> _loadLyrics() async {
    if (_currentSong == null || _currentSong!.id.isEmpty) return;

    final songId = _currentSong!.id;

    // 防止重复加载：如果已经有歌词且是同一首歌，跳过
    if (_lyric != null && _currentLoadedLyricSongId == songId) {
      if (kDebugMode) {
        print('PlayerPage: Lyrics already loaded for $songId, skipping');
      }
      return;
    }

    // 防止同一首歌的并发请求
    if (_loadingLyricsSet.contains(songId) || _isLoadingLyric) {
      if (kDebugMode) {
        print('PlayerPage: Lyrics already being loaded for $songId, skipping');
      }
      return;
    }

    // 添加到正在加载集合，防止重复请求
    _loadingLyricsSet.add(songId);

    if (mounted) {
      setState(() {
        _isLoadingLyric = true;
      });
    }

    try {
      // 加载歌词

      final lyricText = await _musicService.getLyrics(songId);
      if (lyricText != null && lyricText.isNotEmpty) {
        if (mounted) {
          setState(() {
            _lyric = Lyric.parse(lyricText);
            _isLoadingLyric = false;
            _currentLoadedLyricSongId = songId; // 记录当前加载的歌曲ID
          });
        }

        // 歌词加载成功

        // 确保在下一帧渲染时歌词视图能够正确滚动到当前位置
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              // 触发一次重建，确保歌词视图能够正确初始化
            });
          }
        });
      } else {
        if (mounted) {
          setState(() {
            _lyric = null;
            _isLoadingLyric = false;
            _currentLoadedLyricSongId = songId; // 记录尝试加载的歌曲ID，避免重复尝试
          });
        }

        if (kDebugMode) {
          print('PlayerPage: No lyrics found for this song');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _lyric = null;
          _isLoadingLyric = false;
        });
      }

      if (kDebugMode) {
        print('PlayerPage: Error loading lyrics: $e');
      }
    } finally {
      // 无论成功还是失败，都要从加载集合中移除
      _loadingLyricsSet.remove(songId);
    }
  }
  
  void _initAudioPlayer() async {

    // 监听音频播放器的播放状态变化
    _playerStateSubscription = _audioPlayerService.audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state.playing;
        });
      }
    });

    // 监听音频播放器的进度变化
    _positionSubscription = _audioPlayerService.audioPlayer.positionStream.listen((position) {
      if (!_isDraggingSlider && mounted) {
        setState(() {
          _position = position;
          if (_duration.inMilliseconds > 0) {
            _progress = position.inMilliseconds / _duration.inMilliseconds;
          }
        });

        // 自动滚动歌词到当前播放位置
        _scrollToCurrentLyric();
      }
    });

    // 监听音频播放器的时长变化
    _audioPlayerService.audioPlayer.durationStream.listen((duration) {
      if (duration != null && mounted) {
        setState(() {
          _duration = duration;
        });

        // 移除重复的时长更新日志
      }
    });

    // 监听当前歌曲变化（用于自动播放下一曲时更新UI）
    _audioPlayerService.currentSongStream.listen((newSong) {
      if (newSong != null && mounted && newSong.id != _currentSong?.id) {
        if (kDebugMode) {
          print('PlayerPage: Current song changed to: ${newSong.title}');
        }
        setState(() {
          _currentSong = newSong;
          _isFavorite = newSong.isLiked;
          _duration = Duration(seconds: newSong.duration);
          _position = Duration.zero;
          _progress = 0.0;
          // 清理旧歌词状态，确保新歌曲能正确加载歌词
          _lyric = null;
          _isLoadingLyric = false;
          _currentLoadedLyricSongId = null;
        });

        // 加载新歌曲的歌词
        _loadLyrics();

        // 更新PlayerPageProvider
        final playerPageProvider = Provider.of<PlayerPageProvider>(context, listen: false);
        playerPageProvider.updateCurrentSong(newSong);
      }
    });

    // 注意：播放完成事件现在由AudioPlayerService统一处理，不再在PlayerPage中处理
    // 这样可以确保无论用户在哪个页面，都能自动播放下一首

    // 确保当前歌曲有音频URL
    if (_currentSong != null && (_currentSong!.audioUrl == null || _currentSong!.audioUrl!.isEmpty)) {
      if (kDebugMode) {
        print('PlayerPage: No audio URL found, fetching from API');
      }
      
      try {
        // 使用新的缓存系统获取音乐文件，默认标准音质
        final audioUrl = await _musicService.getSongPlayUrl(_currentSong!.id, quality: 'mq');
        if (audioUrl != null) {
          if (mounted) {
            setState(() {
              _currentSong = _currentSong!.copyWith(audioUrl: audioUrl);
            });
          }



          // 如果autoPlay为true，开始播放
          if (widget.autoPlay) {
            _playSong();
          }
        } else {
          if (kDebugMode) {
            print('PlayerPage: Failed to get audio URL');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('PlayerPage: Error fetching audio URL: $e');
        }
      }
    } else {
      // 如果autoPlay为true，开始播放
      if (widget.autoPlay) {
        _playSong();
      }
    }
    
    // 加载歌词
    _loadLyrics();

    // 移除：不再调用_loadHighQualityAlbumCover()，完全由CachedAlbumImage处理
  }
  
  Future<void> _playSong() async {
    if (_currentSong == null) return;

    // 开始播放

    try {
      await _audioPlayerService.playSong(_currentSong!);
    } catch (e) {
      if (kDebugMode) {
        print('PlayerPage: Error playing song: $e');
      }
    }
  }
  
  void _togglePlay() {
    if (_isPlaying) {
      _audioPlayerService.pause();
    } else {
      _audioPlayerService.resume();
    }
  }
  
  Future<void> _playPrevious() async {
    await _enhancedPlaylistManager.playPrevious();
  }

  Future<void> _playNext() async {
    await _enhancedPlaylistManager.playNext();
  }
  
  void _updateCurrentSong() {
    final currentSong = _playlistManager.currentSong;
    if (currentSong != null && (_currentSong == null || currentSong.id != _currentSong!.id)) {
      if (kDebugMode) {
        print('PlayerPage: Updating current song to: ${currentSong.title}');
      }

      if (mounted) {
        setState(() {
          _currentSong = currentSong;
          _isFavorite = _currentSong!.isLiked;
          _duration = Duration(seconds: _currentSong!.duration);
          _lyric = null; // 清空旧歌词
        });
      }

      // 使用全局颜色服务提取新歌曲的颜色
      _globalColorService.extractColorsForSong(currentSong);

      // 歌词会在_initSongData中加载，这里不重复调用
      // _loadLyrics(); // 移除重复调用

      // 注意：不再需要单独加载高清专辑封面，CachedAlbumImage会自动处理
    }
  }

  // 已移除：不再需要单独加载高质量封面
  // CachedAlbumImage组件会自动处理高质量封面的获取和缓存
  Future<void> _loadHighQualityAlbumCover() async {
    // 方法保留但不执行任何操作，避免破坏现有调用
    if (kDebugMode) {
      print('PlayerPage: High quality album cover loading delegated to CachedAlbumImage');
    }
  }

  // 移除独立的颜色提取逻辑，改为使用全局颜色服务

  // 移除颜色处理方法，改为使用全局颜色服务

  void _toggleShuffle() {
    if (mounted) {
      setState(() {
        _isShuffle = !_isShuffle;
      });
    }
    _audioPlayerService.audioPlayer.setShuffleModeEnabled(_isShuffle);
  }

  void _toggleRepeat() {
    if (mounted) {
      setState(() {
        _isRepeat = !_isRepeat;
      });
    }
    _audioPlayerService.audioPlayer.setLoopMode(
      _isRepeat ? LoopMode.one : LoopMode.off
    );
  }

  void _toggleFavorite() {
    if (mounted) {
      setState(() {
        _isFavorite = !_isFavorite;
      });
    }
  }
  
  void _onSliderChanged(double value) {
    // 拖动时只更新临时进度值，不实际跳转
    final clampedValue = value.clamp(0.0, 1.0);
    if (mounted) {
      setState(() {
        _tempProgress = clampedValue;
      });
    }
  }

  void _onSliderChangeStart(double value) {
    // 开始拖动
    if (mounted) {
      setState(() {
        _isDraggingSlider = true;
        _tempProgress = value.clamp(0.0, 1.0);
      });
    }
  }

  void _onSliderChangeEnd(double value) {
    // 结束拖动，执行实际的跳转
    final clampedValue = value.clamp(0.0, 1.0);
    final newPosition = Duration(milliseconds: (clampedValue * _duration.inMilliseconds).round());
    _audioPlayerService.seekTo(newPosition);
    
    if (mounted) {
      setState(() {
        _isDraggingSlider = false;
        _progress = clampedValue; // 更新实际进度
      });
    }
  }

  // 自动滚动到当前歌词行
  void _scrollToCurrentLyric() {
    // 只在歌词页面显示且有歌词数据时才滚动
    if (_lyric == null || !_showLyrics || !_lyricsScrollController.hasClients || _isDraggingLyrics) return;

    final currentIndex = _lyric!.getCurrentLineIndex(_position);
    if (currentIndex >= 0 && currentIndex < _lyric!.lines.length) {
      // 计算目标滚动位置，让当前歌词行位于屏幕中央
      const double itemHeight = 60.0;
      const double topPadding = 150.0; // 考虑顶部padding

      // 计算当前行的实际位置
      final double targetOffset = topPadding + (currentIndex * itemHeight);

      // 获取可视区域高度，让当前歌词显示在中央偏下位置
      final double viewportHeight = _lyricsScrollController.position.viewportDimension;
      // 调整偏移量，让高亮歌词往下显示（减少向上滚动的距离）
      final double centeredOffset = targetOffset - (viewportHeight / 2) - (itemHeight * 0.5);

      // 确保滚动位置在有效范围内
      final double maxScrollExtent = _lyricsScrollController.position.maxScrollExtent;
      final double clampedOffset = centeredOffset.clamp(0.0, maxScrollExtent);

      // 标记开始自动滚动
      _isAutoScrolling = true;

      // 使用快速响应的动画
      _lyricsScrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 300), // 减少动画时长，快速响应
        curve: Curves.easeOut, // 使用更快的曲线
      ).then((_) {
        // 自动滚动完成后重置标记
        _isAutoScrolling = false;
      });
    }
  }

  // 开始拖动歌词
  void _startDraggingLyrics() {
    setState(() {
      _isDraggingLyrics = true;
    });

    // 取消之前的定时器
    _dragEndTimer?.cancel();
  }

  // 结束拖动歌词
  void _endDraggingLyrics() {
    // 延迟结束拖动状态，给用户时间点击播放按钮
    _dragEndTimer?.cancel();
    _dragEndTimer = Timer(const Duration(milliseconds: 2000), () {
      if (mounted) {
        setState(() {
          _isDraggingLyrics = false;
          _draggedLyricIndex = -1;
        });
      }
    });
  }

  // 更新拖动的歌词索引
  void _updateDraggedLyricIndex() {
    if (_lyric == null || !_lyricsScrollController.hasClients) return;

    const double itemHeight = 60.0;
    const double topPadding = 50.0; // 恢复较小的padding值

    // 计算当前滚动位置对应的歌词索引
    final double currentOffset = _lyricsScrollController.offset;
    final double viewportHeight = _lyricsScrollController.position.viewportDimension;
    final double centerOffset = currentOffset + (viewportHeight / 2);

    // 计算中心位置对应的歌词索引
    final int index = ((centerOffset - topPadding) / itemHeight).round();
    final int clampedIndex = index.clamp(0, _lyric!.lines.length - 1);

    if (clampedIndex != _draggedLyricIndex) {
      setState(() {
        _draggedLyricIndex = clampedIndex;
      });
    }
  }

  // 跳转到指定歌词时间
  void _seekToLyricTime(int index) {
    if (_lyric == null || index < 0 || index >= _lyric!.lines.length) return;

    final line = _lyric!.lines[index];
    _audioPlayerService.seekTo(line.timestamp);

    // 结束拖动状态
    setState(() {
      _isDraggingLyrics = false;
      _draggedLyricIndex = -1;
    });
    _dragEndTimer?.cancel();
  }

  // 格式化时间显示
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  /// 处理歌词缓存更新事件
  void _onLyricsCacheUpdated(String songId) {
    // 如果当前歌曲的歌词缓存完成，重新加载歌词
    if (_currentSong != null && _currentSong!.id == songId && _lyric == null) {
      // 重新加载歌词
      // 重新加载歌词
      _reloadLyricsFromCache(songId);
    }
  }

  /// 从缓存重新加载歌词
  Future<void> _reloadLyricsFromCache(String songId) async {
    try {
      final lyricText = await _musicService.getLyrics(songId);
      if (lyricText != null && lyricText.isNotEmpty && mounted) {
        setState(() {
          _lyric = Lyric.parse(lyricText);
          _isLoadingLyric = false;
        });

        // 歌词重新加载成功

        // 确保在下一帧渲染时歌词视图能够正确滚动到当前位置
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _showLyrics) {
            _scrollToCurrentLyric();
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('PlayerPage: Error reloading lyrics from cache: $e');
      }
    }
  }


  @override
  void dispose() {
    if (kDebugMode) {
      print('PlayerPage: Disposing resources');
    }

    _pageController.dispose();
    _positionSubscription?.cancel();
    _playerStateSubscription?.cancel();
    _lyricsCacheSubscription?.cancel();
    _dragEndTimer?.cancel();
    _lyricsScrollController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // 如果数据还没有初始化完成，显示加载指示器
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return PopScope(
      canPop: !_showLyrics, // 只有在专辑封面页面时才允许直接返回
      onPopInvoked: (didPop) {
        if (!didPop && _showLyrics) {
          // 如果在歌词页面，先切换到专辑封面页面
          setState(() {
            _showLyrics = false;
          });
          // 同步更新PageController到专辑封面页面
          _pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      },
      child: ListenableBuilder(
        listenable: _globalColorService,
        builder: (context, child) {
          return Scaffold(
            body: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    _globalColorService.currentColors.gradientStart.withOpacity(0.8),
                    _globalColorService.currentColors.gradientEnd.withOpacity(0.9),
                  ],
                ),
              ),
              child: SafeArea(
                child: GestureDetector(
                  onHorizontalDragEnd: (DragEndDetails details) {
                    // 检测滑动方向和速度
                    if (details.primaryVelocity != null) {
                      if (details.primaryVelocity! > 500) {
                        // 向右滑动 - 返回到封面页面
                        if (_showLyrics) {
                          _pageController.animateToPage(
                            0,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        }
                      } else if (details.primaryVelocity! < -500) {
                        // 向左滑动 - 进入歌词页面
                        if (!_showLyrics) {
                          _pageController.animateToPage(
                            1,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          ).then((_) {
                            // 页面切换完成后，滚动到当前播放位置
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              _scrollToCurrentLyric();
                            });
                          });
                          // 歌词会在_initSongData中自动加载，无需重复调用
                        }
                      }
                    }
                  },
                  child: Column(
                    children: [
                      // 顶部状态栏
                      _buildTopBar(),

                      // 主要内容区域 - 使用PageView避免重建
                      Expanded(
                        child: PageView(
                          controller: _pageController,
                          onPageChanged: (index) {
                            setState(() {
                              _showLyrics = index == 1;
                            });

                            // 当切换到歌词页面时，立即滚动到当前播放位置
                            if (index == 1) {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                _scrollToCurrentLyric();
                              });
                            }
                          },
                          children: [
                            _buildMainContent(),
                            _buildLyricsPage(),
                          ],
                        ),
                      ),

                      // 底部播放控制栏
                      _buildBottomControls(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 顶部状态栏 - 简化设计
  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧返回按钮
          GestureDetector(
            onTap: () {
              if (_showLyrics) {
                // 如果当前在歌词页面，返回到封面页面
                setState(() {
                  _showLyrics = false;
                });
                // 同步更新PageController到专辑封面页面
                _pageController.animateToPage(
                  0,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              } else {
                // 如果在封面页面，关闭播放页面
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }
              }
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                _showLyrics ? Icons.arrow_back_ios_new : Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),

          // 右侧占位，保持布局平衡
          const SizedBox(width: 40, height: 40),
        ],
      ),
    );
  }



  // 主要内容区域 - 按照截图设计
  Widget _buildMainContent() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          children: [
            const SizedBox(height: 20),

            // 专辑封面卡片 - 正方形设计，可点击进入歌词页面
            GestureDetector(
              onTap: () {
                setState(() {
                  _showLyrics = !_showLyrics;
                });
                // 同步更新PageController
                _pageController.animateToPage(
                  _showLyrics ? 1 : 0,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 30),
                child: AspectRatio(
                  aspectRatio: 1.0, // 确保始终保持正方形
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          // 专辑封面图片 - 统一使用高质量500px图片
                          _currentSong != null
                              ? CachedAlbumImage(
                                  songId: _currentSong!.id,
                                  fit: BoxFit.cover,
                                  placeholder: Container(
                                    decoration: const BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [Color(0xFFE8B4FF), Color(0xFFB388FF)],
                                      ),
                                    ),
                                    child: const Center(
                                      child: CircularProgressIndicator(
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    ),
                                  ),
                                  errorWidget: Container(
                                    decoration: const BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [Color(0xFFE8B4FF), Color(0xFFB388FF)],
                                      ),
                                    ),
                                    child: const Icon(Icons.music_note, size: 80, color: Colors.white),
                                  ),
                                )
                              : Container(
                                  decoration: const BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [Color(0xFFE8B4FF), Color(0xFFB388FF)],
                                    ),
                                  ),
                                  child: const Icon(Icons.music_note, size: 80, color: Colors.white),
                                ),

                          // 底部渐变遮罩
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              height: 120,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withOpacity(0.7),
                                  ],
                                ),
                              ),
                            ),
                          ),

                          // VIP标签（如截图所示）
                          // Positioned(
                          //   bottom: 20,
                          //   left: 20,
                          //   child: Container(
                          //     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          //     decoration: BoxDecoration(
                          //       color: Colors.black.withOpacity(0.6),
                          //       borderRadius: BorderRadius.circular(15),
                          //     ),
                          //     child: Row(
                          //       mainAxisSize: MainAxisSize.min,
                          //       children: [
                          //         Icon(
                          //           Icons.star,
                          //           color: Colors.amber,
                          //           size: 16,
                          //         ),
                          //         const SizedBox(width: 4),
                          //         Text(
                          //           'Premium Quality',
                          //           style: TextStyle(
                          //             color: Colors.white,
                          //             fontSize: 12,
                          //             fontWeight: FontWeight.w500,
                          //           ),
                          //         ),
                          //       ],
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 30),

            // 歌曲信息
            Column(
              children: [
                // 歌曲标题 - 可点击切换到歌词页面
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showLyrics = true;
                    });
                    // 同步更新PageController到歌词页面
                    _pageController.animateToPage(
                      1,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: _currentSong != null && _shouldUseTitleMarquee(_currentSong!.title, fontSize: 28)
                        ? ClipRect(
                            child: MarqueeText(
                              text: _currentSong!.title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                height: 1.2,
                              ),
                              textAlign: TextAlign.center,
                              duration: const Duration(seconds: 8),
                              pauseDuration: const Duration(seconds: 2),
                              maxLines: 1,
                            ),
                          )
                        : Text(
                            _currentSong?.title ?? 'Unknown Song',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              height: 1.2,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                  ),
                ),

                const SizedBox(height: 8),

                // 艺术家名称 - 可点击切换到歌词页面
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showLyrics = true;
                    });
                    // 同步更新PageController到歌词页面
                    _pageController.animateToPage(
                      1,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Text(
                      _currentSong?.artistName ?? 'Unknown Artist',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        height: 1.2,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // 互动按钮（分享、收藏、评论）
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildActionButton(
                      icon: Icons.share_outlined,
                      onTap: () {
                        // TODO: 实现分享功能
                      },
                    ),
                    const SizedBox(width: 32),
                    _buildActionButton(
                      icon: Icons.favorite_border,
                      onTap: () {
                        // TODO: 实现收藏功能
                      },
                    ),
                    const SizedBox(width: 32),
                    _buildActionButton(
                      icon: Icons.comment_outlined,
                      onTap: () {
                        // TODO: 实现评论功能
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // 当前歌词显示
                _buildCurrentLyrics(),

                const SizedBox(height: 20),
              ],
            ),

            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }



  // 歌词页面 - 专门的歌词显示页面
  Widget _buildLyricsPage() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 20),
      child: Column(
        children: [
          // 歌曲信息 - 在顶部
          Column(
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: _currentSong != null && _shouldUseTitleMarquee(_currentSong!.title, fontSize: 24)
                    ? ClipRect(
                        child: MarqueeText(
                          text: _currentSong!.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            height: 1.2,
                          ),
                          textAlign: TextAlign.center,
                          duration: const Duration(seconds: 8),
                          pauseDuration: const Duration(seconds: 2),
                          maxLines: 1,
                        ),
                      )
                    : Text(
                        _currentSong?.title ?? 'Unknown Song',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          height: 1.2,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  _currentSong?.artistName ?? 'Unknown Artist',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 16,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 30),

          // 歌词内容 - 占据大部分空间
          Expanded(
            child: _isLoadingLyric
                ? Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white.withOpacity(0.8)),
                    ),
                  )
                : NotificationListener<ScrollNotification>(
                    onNotification: (ScrollNotification notification) {
                      // 忽略自动滚动事件，只处理用户手动拖动
                      if (_isAutoScrolling) return false;

                      if (notification is ScrollStartNotification) {
                        _startDraggingLyrics();
                      } else if (notification is ScrollUpdateNotification) {
                        _updateDraggedLyricIndex();
                      } else if (notification is ScrollEndNotification) {
                        _endDraggingLyrics();
                      }
                      return false;
                    },
                    child: SingleChildScrollView(
                      controller: _lyricsScrollController,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 50), // 恢复较小的padding，会导致上下歌词被截断
                        child: Column(
                          children: [
                        if (_lyric != null && _lyric!.lines.isNotEmpty)
                          ..._lyric!.lines.asMap().entries.map((entry) {
                            final index = entry.key;
                            final line = entry.value;
                            final isCurrentLine = _isCurrentLyricLine(line);
                            // 只有在拖动状态下且是当前拖动中心行才显示控件
                            final isDraggedLine = _isDraggingLyrics && index == _draggedLyricIndex;

                            return AnimatedContainer(
                              duration: const Duration(milliseconds: 200), // 减少动画时长，快速响应
                              curve: Curves.easeOut,
                              height: isCurrentLine ? 80 : 60, // 当前行增加高度以容纳走马灯
                              alignment: Alignment.center,
                              transform: Matrix4.identity()
                                ..scale(isCurrentLine ? 1.05 : 1.0), // 稍微减小缩放比例
                              child: Stack(
                                children: [
                                  // 歌词文本 - 始终居中显示
                                  SizedBox(
                                    width: double.infinity,
                                    child: Center(
                                      child: isCurrentLine
                                          ? (_shouldUseMarquee(line.text, isCurrentLine)
                                              ? ClipRect(
                                                  child: MarqueeText(
                                                    text: line.text,
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 22,
                                                      fontWeight: FontWeight.w700,
                                                      height: 1.4,
                                                      shadows: [
                                                        Shadow(
                                                          color: Colors.white.withOpacity(0.4),
                                                          blurRadius: 10,
                                                        ),
                                                      ],
                                                    ),
                                                    textAlign: TextAlign.center,
                                                    duration: const Duration(seconds: 8),
                                                    pauseDuration: const Duration(seconds: 2),
                                                    maxLines: 1,
                                                  ),
                                                )
                                              : AnimatedDefaultTextStyle(
                                                  duration: const Duration(milliseconds: 200),
                                                  curve: Curves.easeOut,
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 22,
                                                    fontWeight: FontWeight.w700,
                                                    height: 1.4,
                                                    shadows: [
                                                      Shadow(
                                                        color: Colors.white.withOpacity(0.4),
                                                        blurRadius: 10,
                                                      ),
                                                    ],
                                                  ),
                                                  child: Text(
                                                    line.text,
                                                    textAlign: TextAlign.center,
                                                    maxLines: 1,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ))
                                          : AnimatedDefaultTextStyle(
                                              duration: const Duration(milliseconds: 200),
                                              curve: Curves.easeOut,
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(0.5),
                                                fontSize: 17,
                                                fontWeight: FontWeight.w400,
                                                height: 1.4,
                                              ),
                                              child: Text(
                                                line.text,
                                                textAlign: TextAlign.center,
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                      ),
                                    ),

                                    // 拖动时显示的时间标签和播放按钮 - 作为覆盖层
                                    if (isDraggedLine) ...[
                                      // 左侧时间标签
                                      Positioned(
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        child: Row(
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                                              decoration: BoxDecoration(
                                                color: Colors.white.withOpacity(0.25),
                                                borderRadius: BorderRadius.circular(10),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.white.withOpacity(0.1),
                                                    blurRadius: 4,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: Text(
                                                _formatDuration(line.timestamp),
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                            const SizedBox(width: 4),
                                            // 装饰细线
                                            Container(
                                              width: 20,
                                              height: 1,
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  colors: [
                                                    Colors.transparent,
                                                    Colors.white.withOpacity(0.3),
                                                    Colors.white.withOpacity(0.6),
                                                    Colors.white.withOpacity(0.3),
                                                    Colors.transparent,
                                                  ],
                                                  stops: const [0.0, 0.2, 0.5, 0.8, 1.0],
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.white.withOpacity(0.2),
                                                    blurRadius: 2,
                                                    spreadRadius: 0.5,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // 右侧播放按钮
                                      Positioned(
                                        right: 0,
                                        top: 0,
                                        bottom: 0,
                                        child: Row(
                                          children: [
                                            // 装饰细线
                                            Container(
                                              width: 20,
                                              height: 1,
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  colors: [
                                                    Colors.transparent,
                                                    Colors.white.withOpacity(0.3),
                                                    Colors.white.withOpacity(0.6),
                                                    Colors.white.withOpacity(0.3),
                                                    Colors.transparent,
                                                  ],
                                                  stops: const [0.0, 0.2, 0.5, 0.8, 1.0],
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.white.withOpacity(0.2),
                                                    blurRadius: 2,
                                                    spreadRadius: 0.5,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(width: 4),
                                            GestureDetector(
                                              onTap: () => _seekToLyricTime(index),
                                              child: Container(
                                                width: 32,
                                                height: 32,
                                                decoration: BoxDecoration(
                                                  color: Colors.white.withOpacity(0.9),
                                                  shape: BoxShape.circle,
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Colors.black.withOpacity(0.1),
                                                      blurRadius: 4,
                                                      offset: const Offset(0, 2),
                                                    ),
                                                    BoxShadow(
                                                      color: Colors.white.withOpacity(0.1),
                                                      blurRadius: 6,
                                                      spreadRadius: 1,
                                                    ),
                                                  ],
                                                ),
                                                child: Icon(
                                                  Icons.play_arrow,
                                                  color: _globalColorService.currentColors.primary,
                                                  size: 16,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                ],
                              ),
                            );
                          }).toList()
                        else
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Text(
                              _getLyricsText(),
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 16,
                                height: 1.8,
                                fontWeight: FontWeight.w400,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
          ),
        ],
      ),
    );
  }

  // 判断是否为当前播放的歌词行
  bool _isCurrentLyricLine(dynamic line) {
    if (_lyric == null || _position == Duration.zero) return false;

    // 使用Lyric模型的getCurrentLineIndex方法
    final currentIndex = _lyric!.getCurrentLineIndex(_position);

    if (currentIndex >= 0 && currentIndex < _lyric!.lines.length) {
      return _lyric!.lines[currentIndex] == line;
    }

    return false;
  }

  // 判断标题是否应该使用走马灯效果
  bool _shouldUseTitleMarquee(String title, {double fontSize = 28}) {
    if (title.isEmpty) return false;

    try {
      // 计算文本宽度
      final TextPainter textPainter = TextPainter(
        text: TextSpan(
          text: title,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
        maxLines: 1,
      );
      textPainter.layout();

      // 估算可用宽度（屏幕宽度减去padding）
      final screenWidth = MediaQuery.of(context).size.width;
      final availableWidth = screenWidth - 80; // 减去左右padding

      return textPainter.size.width > availableWidth;
    } catch (e) {
      // 如果计算失败，使用字符数作为备选方案
      return title.length > 15;
    }
  }

  // 判断是否应该使用走马灯效果
  bool _shouldUseMarquee(String text, bool isCurrentLine) {
    if (text.isEmpty) return false;

    try {
      // 计算文本宽度
      final TextPainter textPainter = TextPainter(
        text: TextSpan(
          text: text,
          style: TextStyle(
            fontSize: isCurrentLine ? 22 : 17,
            fontWeight: isCurrentLine ? FontWeight.w700 : FontWeight.w400,
          ),
        ),
        textDirection: TextDirection.ltr,
        maxLines: 1,
      );
      textPainter.layout();

      // 估算可用宽度（屏幕宽度减去padding和其他元素）
      final screenWidth = MediaQuery.of(context).size.width;
      final availableWidth = screenWidth - 100; // 减去左右padding和可能的按钮空间

      return textPainter.size.width > availableWidth;
    } catch (e) {
      // 如果计算失败，对于当前播放的歌词使用更保守的策略
      if (isCurrentLine) {
        return text.length > 15; // 当前播放的歌词更容易触发走马灯
      } else {
        return text.length > 25; // 非当前歌词需要更长才触发走马灯
      }
    }
  }

  // 底部播放控制栏 - 完全按照设计图
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 状态指示器 - 显示当前是封面还是歌词页面
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 封面页面指示器
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: !_showLyrics ? Colors.white : Colors.white.withOpacity(0.3),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              // 歌词页面指示器
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _showLyrics ? Colors.white : Colors.white.withOpacity(0.3),
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 进度条
          Column(
            children: [
              SliderTheme(
                data: SliderThemeData(
                  trackHeight: 3,
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 8,
                  ),
                  overlayShape: const RoundSliderOverlayShape(
                    overlayRadius: 16,
                  ),
                  activeTrackColor: Colors.white,
                  inactiveTrackColor: Colors.white.withOpacity(0.3),
                  thumbColor: Colors.white,
                  overlayColor: Colors.white.withOpacity(0.2),
                ),
                child: Slider(
                  value: (_isDraggingSlider ? _tempProgress : _progress).clamp(0.0, 1.0),
                  onChanged: _onSliderChanged,
                  onChangeStart: _onSliderChangeStart,
                  onChangeEnd: _onSliderChangeEnd,
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(_position),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      _formatDuration(_duration),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // 播放控制按钮 - 重新布局
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // 播放模式切换
              GestureDetector(
                onTap: () {
                  _enhancedPlaylistManager.togglePlayMode();
                },
                child: Icon(
                  _getPlayModeIcon(_enhancedPlaylistManager.playMode),
                  color: Colors.white.withOpacity(0.8),
                  size: 28,
                ),
              ),

              // 上一首
              GestureDetector(
                onTap: _playPrevious,
                child: Icon(
                  Icons.skip_previous,
                  color: Colors.white.withOpacity(0.8),
                  size: 36,
                ),
              ),

              // 播放/暂停
              GestureDetector(
                onTap: _togglePlay,
                child: Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _isPlaying ? Icons.pause : Icons.play_arrow,
                    color: _globalColorService.currentColors.primary,
                    size: 32,
                  ),
                ),
              ),

              // 下一首
              GestureDetector(
                onTap: _playNext,
                child: Icon(
                  Icons.skip_next,
                  color: Colors.white.withOpacity(0.8),
                  size: 36,
                ),
              ),

              // 播放列表
              GestureDetector(
                onTap: () {
                  showPlaylistBottomSheet(context);
                },
                child: Icon(
                  Icons.queue_music,
                  color: Colors.white.withOpacity(0.8),
                  size: 28,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取播放模式图标
  IconData _getPlayModeIcon(PlayMode mode) {
    switch (mode) {
      case PlayMode.listRepeat:
        return Icons.repeat;
      case PlayMode.singleRepeat:
        return Icons.repeat_one;
      case PlayMode.shuffle:
        return Icons.shuffle;
    }
  }

  // 构建互动按钮
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white.withOpacity(0.8),
          size: 20,
        ),
      ),
    );
  }

  // 构建当前歌词显示
  Widget _buildCurrentLyrics() {
    if (_lyric == null || _lyric!.lines.isEmpty) {
      return Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Center(
          child: Text(
            'No lyrics available',
            style: TextStyle(
              color: Colors.white.withOpacity(0.5),
              fontSize: 14,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // 获取当前和下一句歌词
    final currentIndex = _lyric!.getCurrentLineIndex(_position);
    final currentLine = currentIndex >= 0 && currentIndex < _lyric!.lines.length
        ? _lyric!.lines[currentIndex].text
        : '';
    final nextLine = currentIndex + 1 < _lyric!.lines.length
        ? _lyric!.lines[currentIndex + 1].text
        : '';

    return GestureDetector(
      onTap: () {
        setState(() {
          _showLyrics = true;
        });
        // 同步更新PageController到歌词页面
        _pageController.animateToPage(
          1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 当前歌词 - 使用走马灯效果
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: ClipRect(
                child: MarqueeText(
                  text: currentLine.isNotEmpty ? currentLine : 'Instrumental',
                  key: ValueKey(currentLine),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                  duration: const Duration(seconds: 8),
                  pauseDuration: const Duration(seconds: 2),
                  maxLines: 1,
                ),
              ),
            ),
            const SizedBox(height: 8),
            // 下一句歌词（预览）- 使用走马灯效果
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: ClipRect(
                child: MarqueeText(
                  text: nextLine,
                  key: ValueKey(nextLine),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                  duration: const Duration(seconds: 8),
                  pauseDuration: const Duration(seconds: 2),
                  maxLines: 1,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 获取歌词文本 - 按照设计图显示
  String _getLyricsText() {
    if (_lyric != null && _lyric!.lines.isNotEmpty) {
      // 如果有歌词，显示歌词
      return _lyric!.lines.map((line) => line.text).join('\n');
    } else {
      // 如果没有歌词，显示默认文本（类似设计图中的英文歌词）
      return '''I got my head out the sunroof

I'm blasting our favorite tunes

I only got one thing on my mind

You got me stuck on the thought of you

You're making me feel brand new''';
    }
  }


}