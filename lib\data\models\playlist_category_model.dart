import 'dart:convert';
import 'song_model.dart';

/// 歌单分类响应模型
class PlaylistCategoryResponse {
  final int code;
  final bool success;
  final String message;
  final PlaylistCategoryData data;

  PlaylistCategoryResponse({
    required this.code,
    required this.success,
    required this.message,
    required this.data,
  });

  factory PlaylistCategoryResponse.fromJson(Map<String, dynamic> json) {
    return PlaylistCategoryResponse(
      code: json['code'] ?? 200,
      success: json['success'] ?? true,
      message: json['message'] ?? '',
      data: PlaylistCategoryData.fromJson(json['data'] ?? {}),
    );
  }
}

/// 歌单分类数据
class PlaylistCategoryData {
  final int total;
  final Map<String, List<PlaylistCategorySong>> categories;

  PlaylistCategoryData({
    required this.total,
    required this.categories,
  });

  factory PlaylistCategoryData.fromJson(Map<String, dynamic> json) {
    final categories = <String, List<PlaylistCategorySong>>{};
    
    final categoriesJson = json['categories'] as Map<String, dynamic>? ?? {};
    
    for (final entry in categoriesJson.entries) {
      final categoryName = entry.key;
      final songsJson = entry.value as List<dynamic>? ?? [];
      
      final songs = songsJson.map((songJson) {
        return PlaylistCategorySong.fromJson(songJson as Map<String, dynamic>);
      }).toList();
      
      categories[categoryName] = songs;
    }

    return PlaylistCategoryData(
      total: json['total'] ?? 0,
      categories: categories,
    );
  }

  /// 获取所有分类名称
  List<String> get categoryNames => categories.keys.toList();

  /// 获取指定分类的歌曲列表
  List<PlaylistCategorySong> getSongsForCategory(String categoryName) {
    return categories[categoryName] ?? [];
  }
}

/// 歌单分类中的歌曲模型
class PlaylistCategorySong {
  final String name;
  final String artist;
  final String rid;
  final String artistid;
  final String album;
  final String albumid;
  final String img;
  final String duration;
  final String durationFormatted;
  final String vid;
  final bool hasMv;
  final String mvquality;
  final String minfo;
  final String mvflag;
  final bool free;
  final String payFlag;
  final String format;
  final String contentType;
  final String kmark;
  final String track;
  final String? recordtime;
  final String? tagName;
  final String? tagType;
  final String? tagId;
  final String? tagNameBase;
  final String? tagNameRank;

  PlaylistCategorySong({
    required this.name,
    required this.artist,
    required this.rid,
    required this.artistid,
    required this.album,
    required this.albumid,
    required this.img,
    required this.duration,
    required this.durationFormatted,
    required this.vid,
    required this.hasMv,
    required this.mvquality,
    required this.minfo,
    required this.mvflag,
    required this.free,
    required this.payFlag,
    required this.format,
    required this.contentType,
    required this.kmark,
    required this.track,
    this.recordtime,
    this.tagName,
    this.tagType,
    this.tagId,
    this.tagNameBase,
    this.tagNameRank,
  });

  factory PlaylistCategorySong.fromJson(Map<String, dynamic> json) {
    return PlaylistCategorySong(
      name: _decodeHtmlEntities(json['name'] ?? ''),
      artist: _decodeHtmlEntities(json['artist'] ?? ''),
      rid: json['rid']?.toString() ?? '',
      artistid: json['artistid']?.toString() ?? '',
      album: _decodeHtmlEntities(json['album'] ?? ''),
      albumid: json['albumid']?.toString() ?? '',
      img: json['img']?.toString() ?? '',
      duration: json['duration']?.toString() ?? '0',
      durationFormatted: json['duration_formatted']?.toString() ?? '0:00',
      vid: json['vid']?.toString() ?? '0',
      hasMv: json['has_mv'] == true,
      mvquality: json['mvquality']?.toString() ?? '',
      minfo: json['minfo']?.toString() ?? '',
      mvflag: json['mvflag']?.toString() ?? '0',
      free: json['free'] == true,
      payFlag: json['pay_flag']?.toString() ?? '',
      format: json['format']?.toString() ?? '',
      contentType: json['content_type']?.toString() ?? '',
      kmark: json['kmark']?.toString() ?? '',
      track: json['track']?.toString() ?? '',
      recordtime: json['recordtime']?.toString(),
      tagName: json['tag_name']?.toString(),
      tagType: json['tag_type']?.toString(),
      tagId: json['tag_id']?.toString(),
      tagNameBase: json['tag_name_base']?.toString(),
      tagNameRank: json['tag_name_rank']?.toString(),
    );
  }

  /// 转换为统一的Song模型
  Song toSong() {
    return Song(
      id: rid,
      title: name,
      artistName: artist,
      albumName: album,
      coverUrl: img.isNotEmpty ? img : null,
      highQualityCoverUrl: img.isNotEmpty ? img : null,
      audioUrl: null, // 播放时动态获取
      duration: int.tryParse(duration) ?? 0,
      quality: _parseQuality(minfo),
      isLiked: false,
      artistId: artistid,
      albumId: albumid,
      trackNumber: int.tryParse(track) ?? 0,
      releaseDate: recordtime,
      hasMv: hasMv,
      isFree: free,
    );
  }

  /// 解析音质等级
  static SongQuality _parseQuality(String minfo) {
    if (minfo.contains('level:ff') || minfo.contains('flac')) {
      return SongQuality.hires; // 无损
    } else if (minfo.contains('level:p') || minfo.contains('320')) {
      return SongQuality.high; // 高品质
    } else {
      return SongQuality.standard; // 标准
    }
  }
}

/// HTML实体解码
String _decodeHtmlEntities(String text) {
  return text
      .replaceAll('&#39;', "'")
      .replaceAll('&quot;', '"')
      .replaceAll('&amp;', '&')
      .replaceAll('&lt;', '<')
      .replaceAll('&gt;', '>');
}
