import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTextStyles {
  // 大标题样式：36pt Medium
  static const TextStyle headingLarge = TextStyle(
    fontFamily: 'SourceHanSans',
    fontSize: 36,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.2,
  );
  
  // 卡片标题样式：28pt Medium
  static const TextStyle headingMedium = TextStyle(
    fontFamily: 'SourceHanSans',
    fontSize: 28,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.2,
  );
  
  // 内容文字样式：24pt Medium
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'SourceHanSans',
    fontSize: 24,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.4,
  );
  
  // 标签样式：22pt Medium
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'SourceHanSans',
    fontSize: 22,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.4,
  );
  
  // 次要文本样式
  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'SourceHanSans',
    fontSize: 18,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.4,
  );
  
  // 按钮文本样式
  static const TextStyle buttonText = TextStyle(
    fontFamily: 'SourceHanSans',
    fontSize: 20,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.2,
  );
  
  // 导航栏文本样式
  static const TextStyle navBarText = TextStyle(
    fontFamily: 'SourceHanSans',
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    height: 1.2,
  );
  
  // 标签页文本样式
  static const TextStyle tabText = TextStyle(
    fontFamily: 'SourceHanSans',
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.2,
  );
} 