import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:math' show cos, sin;
import '../data/models/song_model.dart';
import '../data/models/kuwo_ranking_model.dart';
import '../data/services/kuwo_ranking_service.dart';
import '../data/services/kuwo_music_service.dart';
import '../data/providers/mini_player_provider.dart';
import '../data/providers/enhanced_mini_player_provider.dart';
import '../data/services/audio_player_service.dart';
import 'package:provider/provider.dart';
import '../data/services/player_state_sync_service.dart';
import '../data/services/playlist_manager.dart';
import '../data/services/enhanced_playlist_manager.dart';
import 'player_page.dart';

class HotHitsPage extends StatefulWidget {
  const HotHitsPage({super.key});

  @override
  State<HotHitsPage> createState() => _HotHitsPageState();
}

class _HotHitsPageState extends State<HotHitsPage>
    with TickerProviderStateMixin {
  int _selectedTabIndex = 0;
  final List<String> _tabs = ['热歌榜', '新歌榜', '抖音榜'];
  
  late AnimationController _sunController;
  late AnimationController _stairsController;
  late Animation<double> _sunRotation;
  late Animation<double> _stairsSlide;

  // API服务
  final KuwoRankingService _rankingService = KuwoRankingService();
  final KuwoMusicService _musicService = KuwoMusicService();
  
  // 排行榜数据
  Map<KuwoRankingType, KuwoRankingResponse?> _rankingData = {};
  bool _isLoading = true;
  String? _errorMessage;
  
  // 当前歌曲列表
  List<Song> _currentSongs = [];
  
  // 歌曲播放加载状态
  bool _isSongLoading = false;

  // 获取当前选中的排行榜类型
  KuwoRankingType get _currentRankingType {
    switch (_selectedTabIndex) {
      case 0:
        return KuwoRankingType.hotHits;
      case 1:
        return KuwoRankingType.newSongs;
      case 2:
        return KuwoRankingType.shortVideo;
      default:
        return KuwoRankingType.hotHits;
    }
  }

  // 获取当前榜单信息
  Map<String, dynamic> get _currentChartInfo {
    final rankingType = _currentRankingType;
    final rankingResponse = _rankingData[rankingType];
    
        return {
      'title': rankingType.title,
      'subtitle': rankingType.subtitle,
      'colors': rankingType.gradientColors,
      'count': rankingResponse?.data.rankInfo.num ?? 0,
      'info': rankingResponse?.data.rankInfo.info ?? '',
    };
  }

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _sunController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );
    
    _stairsController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    // 太阳旋转动画
    _sunRotation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _sunController,
      curve: Curves.linear,
    ));

    // 楼梯滑动动画
    _stairsSlide = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _stairsController,
      curve: Curves.easeInOut,
    ));

    // 启动动画
    _sunController.repeat();
    _stairsController.repeat(reverse: true);

    // 加载排行榜数据
    _loadRankingData();
  }

  @override
  void dispose() {
    _sunController.dispose();
    _stairsController.dispose();
    super.dispose();
  }

  /// 加载排行榜数据
  Future<void> _loadRankingData() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final results = await _rankingService.getAllRankings(pageSize: 50);
      
      if (mounted) {
        setState(() {
          _rankingData = results;
          _updateCurrentSongs();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '加载排行榜数据失败: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// 刷新当前排行榜数据
  Future<void> _refreshCurrentRanking() async {
    if (!mounted) return;

    try {
      final response = await _rankingService.getRankingData(
        rankingType: _currentRankingType,
        pageSize: 50,
      );
      
      if (mounted && response != null) {
        setState(() {
          _rankingData[_currentRankingType] = response;
          _updateCurrentSongs();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刷新失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final chartInfo = _currentChartInfo;
    
    return Scaffold(
      body: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: chartInfo['colors'],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部导航栏
              _buildTopNavigation(),
              
              // 主要内容
              Expanded(
                child: _isLoading
                  ? _buildLoadingView()
                  : _errorMessage != null
                    ? _buildErrorView()
                    : Column(
                  children: [
                    // 榜单标题区域
                    _buildTitleSection(),
                    
                    // 播放控制区域
                    _buildPlayControlSection(),
                    
                    // 歌曲列表
                    Expanded(
                            child: Stack(
                              children: [
                                _buildSongsList(),
                                
                                // 歌曲加载覆盖层
                                if (_isSongLoading)
                                  Container(
                                    color: Colors.black54,
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          CircularProgressIndicator(
                                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                          ),
                                          SizedBox(height: 16),
                                          Text(
                                            'Loading song...',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ],
                                      ),
                ),
              ),
            ],
          ),
        ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            '正在加载排行榜数据...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.white,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage ?? '加载失败',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadRankingData,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.2),
              foregroundColor: Colors.white,
            ),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(18),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
          
          // 标签页
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_tabs.length, (index) {
                final isSelected = index == _selectedTabIndex;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedTabIndex = index;
                      _updateCurrentSongs();
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _tabs[index],
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                        if (isSelected)
                          Container(
                            margin: const EdgeInsets.only(top: 4),
                            width: 20,
                            height: 2,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(1),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              }),
            ),
          ),
          
          // 占位，保持对称
          const SizedBox(width: 36),
        ],
      ),
    );
  }

  Widget _buildTitleSection() {
    final chartInfo = _currentChartInfo;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      child: Row(
        children: [
          // 左侧标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 副标题
                Text(
                  chartInfo['subtitle'],
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 14,
                    fontWeight: FontWeight.w300,
                    letterSpacing: 2,
                  ),
                ),
                const SizedBox(height: 4),
                
                // 主标题
                Text(
                  chartInfo['title'],
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                // 榜单描述
                if (chartInfo['info'] != null && chartInfo['info'].isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    chartInfo['info'],
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // 右侧动画区域
          _buildAnimationArea(),
        ],
      ),
    );
  }

  Widget _buildAnimationArea() {
    return SizedBox(
      width: 120,
      height: 80,
      child: Stack(
        children: [
          // 根据不同榜单显示不同动画
          if (_selectedTabIndex == 0) ...[
            // 热歌榜：楼梯动画
            AnimatedBuilder(
              animation: _stairsSlide,
              builder: (context, child) {
                return Positioned(
                  right: 10 + (_stairsSlide.value * 20),
                  bottom: 0,
                  child: CustomPaint(
                    size: const Size(80, 60),
                    painter: StairsPainter(animationValue: _stairsSlide.value),
                  ),
                );
              },
            ),
            // 热歌榜：太阳动画
            AnimatedBuilder(
              animation: _sunRotation,
              builder: (context, child) {
                return Positioned(
                  right: 20,
                  top: 10,
                  child: Transform.rotate(
                    angle: _sunRotation.value * 2 * 3.14159,
                    child: CustomPaint(
                      size: const Size(40, 40),
                      painter: SunPainter(),
                    ),
                  ),
                );
              },
            ),
          ] else if (_selectedTabIndex == 1) ...[
            // 新歌榜：星星和音符浮动动画
            AnimatedBuilder(
              animation: _sunRotation,
              builder: (context, child) {
                return Positioned.fill(
                  child: CustomPaint(
                    size: const Size(120, 80),
                    painter: StarMusicNotePainter(animationValue: _sunRotation.value),
                  ),
                );
              },
            ),
          ] else if (_selectedTabIndex == 2) ...[
            // 抖音榜：TikTok风格动画
            AnimatedBuilder(
              animation: _stairsSlide,
              builder: (context, child) {
                return Positioned(
                  right: 20,
                  bottom: 10,
                  child: CustomPaint(
                    size: const Size(80, 50),
                    painter: TikTokStagePainter(animationValue: _stairsSlide.value),
                  ),
                );
              },
            ),
            // 抖音榜：跳动音符
            AnimatedBuilder(
              animation: _sunRotation,
              builder: (context, child) {
                return Positioned(
                  right: 40,
                  top: 0,
                  child: Transform.translate(
                    offset: Offset(0, sin(_sunRotation.value * 2 * 3.14159) * 5),
                    child: CustomPaint(
                      size: const Size(40, 40),
                      painter: TikTokNotePainter(),
                    ),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlayControlSection() {
    final chartInfo = _currentChartInfo;
    final rankingResponse = _rankingData[_currentRankingType];
    final vipCount = rankingResponse?.data.songs.where((song) => song.isVip).length ?? 0;
    
    String vipMessage = '';
    switch (_selectedTabIndex) {
      case 0:
        vipMessage = '53:11 | ${vipCount}首会员歌曲免费畅听中';
        break;
      case 1:
        vipMessage = '53:07 | ${vipCount}首会员歌曲免费畅听中';
        break;
      case 2:
        vipMessage = '45:23 | ${vipCount}首会员歌曲免费畅听中';
        break;
    }
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          // 免费播放信息
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    Icons.music_note,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  vipMessage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '获取更多时长',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    decoration: TextDecoration.underline,
                  ),
                ),
                const Icon(
                  Icons.chevron_right,
                  color: Colors.white,
                  size: 16,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 播放全部按钮
          Row(
            children: [
              GestureDetector(
                onTap: _playAllSongs,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '播放全部',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${chartInfo['count']}',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const Spacer(),
              
              // 下载和列表按钮
              Row(
                children: [
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: const Icon(
                      Icons.download,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: const Icon(
                      Icons.format_list_bulleted,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSongsList() {
    if (_currentSongs.isEmpty) {
      return const Center(
        child: Text(
          '暂无歌曲数据',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: RefreshIndicator(
        onRefresh: _refreshCurrentRanking,
      child: ListView.builder(
        padding: const EdgeInsets.only(top: 16),
        itemCount: _currentSongs.length,
        itemBuilder: (context, index) {
          final song = _currentSongs[index];
            final kuwoSong = _rankingData[_currentRankingType]!.data.songs[index];
            return _buildSongItem(song, kuwoSong, index + 1);
        },
        ),
      ),
    );
  }

  Widget _buildSongItem(Song song, KuwoRankingSong kuwoSong, int rank) {
    return GestureDetector(
      onTap: () => _onSongTap(rank - 1),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // 排名
            SizedBox(
              width: 30,
              child: Text(
                rank.toString(),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: rank <= 3 ? const Color(0xFFFF6B6B) : Colors.grey[600],
                ),
              ),
            ),
            
            // 排名趋势图标
            SizedBox(
              width: 20,
              child: Icon(
                kuwoSong.rankChangeIcon,
                color: kuwoSong.rankChangeColor,
                size: 16,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // 歌曲信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          song.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (kuwoSong.isVip) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: const Color(0xFF00C853),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'VIP',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${song.artistName} · ${song.albumName}',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            
            // 更多按钮
            GestureDetector(
              onTap: () => _showSongOptions(song),
              child: Container(
                width: 32,
                height: 32,
                child: Icon(
                  Icons.more_vert,
                  color: Colors.grey[400],
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _playAllSongs() {
    if (_currentSongs.isNotEmpty) {
      _onSongTap(0);
    }
  }

  void _onSongTap(int index) async {
    if (index < 0 || index >= _currentSongs.length) return;

    setState(() {
      _isSongLoading = true;
    });

    try {
      final Song song = _currentSongs[index];
      final kuwoSong = _rankingData[_currentRankingType]!.data.songs[index];

      // 1. 并行获取音频URL和高清封面
      String? audioUrl = song.audioUrl;
      String? highQualityCoverUrl = song.highQualityCoverUrl;
      
      final futures = <Future>[];
      
      // 如果没有音频URL，获取音频URL
      if (audioUrl == null || audioUrl.isEmpty) {
        futures.add(_musicService.getSongPlayUrl(kuwoSong.id));
    } else {
        futures.add(Future.value(audioUrl));
      }
      
      // 不再预加载高清封面，让CachedAlbumImage组件按需处理
      // 这样避免重复的网络请求
      futures.add(Future.value(highQualityCoverUrl ?? song.coverUrl));
      
      final results = await Future.wait(futures);
      audioUrl = results[0] as String?;
      highQualityCoverUrl = results[1] as String?;

      if (audioUrl == null || audioUrl.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('无法获取音频链接')),
          );
          setState(() {
            _isSongLoading = false;
          });
        }
        return;
      }

      // 2. 更新歌曲对象
      final updatedSong = song.copyWith(
        audioUrl: audioUrl,
        highQualityCoverUrl: highQualityCoverUrl,
      );
      _currentSongs[index] = updatedSong;

      // 3. 设置播放列表（歌单模式：替换整个播放列表）
      final enhancedPlaylistManager = Provider.of<EnhancedPlaylistManager>(context, listen: false);
      enhancedPlaylistManager.setPlaylistAndPlay(_currentSongs, index);

      // 4. 立即结束加载状态并导航到播放页面
      if (mounted) {
        setState(() {
          _isSongLoading = false;
        });

        // 5. 导航到播放页面
        // 注意：不需要手动调用 syncService.playSong()，因为 setPlaylistAndPlay() 已经处理了播放
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayerPage(
              song: _currentSongs[index],
              autoPlay: false,  // 播放已经由 setPlaylistAndPlay() 处理
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSongLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('播放失败: $e')),
        );
      }
    }
  }

  void _showSongOptions(Song song) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
            ListTile(
              leading: const Icon(Icons.play_arrow),
              title: const Text('播放'),
              onTap: () {
                Navigator.pop(context);
                // 找到歌曲在列表中的索引
                final index = _currentSongs.indexWhere((s) => s.id == song.id);
                if (index != -1) {
                  _onSongTap(index);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.queue_music),
              title: const Text('添加到播放列表'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现添加到播放列表功能
              },
            ),
            ListTile(
              leading: const Icon(Icons.favorite_border),
              title: const Text('收藏'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现收藏功能
              },
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('下载'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现下载功能
              },
            ),
          ],
        ),
      ),
    );
  }

  // 更新当前歌曲列表
  void _updateCurrentSongs() {
    final rankingResponse = _rankingData[_currentRankingType];
    if (rankingResponse == null) {
      _currentSongs = [];
      return;
    }
    
    _currentSongs = rankingResponse.data.songs.map((kuwoSong) {
      return kuwoSong.toSong(
        coverUrl: rankingResponse.data.rankInfo.pic.isNotEmpty 
          ? rankingResponse.data.rankInfo.pic 
          : 'https://picsum.photos/200/200?random=${kuwoSong.id.hashCode}',
        // 移除硬编码的测试音频URL，让音频URL为空，在点击时动态获取
        audioUrl: null,
      );
    }).toList();
  }
}

// 太阳画笔
class SunPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFFFD700)
      ..style = PaintingStyle.fill;
    
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 4;
    
    // 绘制太阳圆形
    canvas.drawCircle(center, radius, paint);
    
    // 绘制太阳光线
    paint.strokeWidth = 2;
    paint.style = PaintingStyle.stroke;
    
    for (int i = 0; i < 8; i++) {
      final angle = (i * 45) * (3.14159 / 180);
      final start = Offset(
        center.dx + (radius + 4) * cos(angle),
        center.dy + (radius + 4) * sin(angle),
      );
      final end = Offset(
        center.dx + (radius + 10) * cos(angle),
        center.dy + (radius + 10) * sin(angle),
      );
      canvas.drawLine(start, end, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// 楼梯画笔
class StairsPainter extends CustomPainter {
  final double animationValue;
  
  StairsPainter({required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.fill;
    
    final stepWidth = size.width / 4;
    final stepHeight = size.height / 4;
    
    // 绘制4层楼梯，带动画效果
    for (int i = 0; i < 4; i++) {
      final opacity = (animationValue + i * 0.2) % 1.0;
      paint.color = Colors.white.withOpacity(0.3 + opacity * 0.5);
      
      final rect = Rect.fromLTWH(
        i * stepWidth,
        size.height - (i + 1) * stepHeight,
        stepWidth * (4 - i),
        stepHeight,
      );
      
      canvas.drawRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(StairsPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

// 新歌榜：星星和音符浮动动画画笔
class StarMusicNotePainter extends CustomPainter {
  final double animationValue;
  
  StarMusicNotePainter({required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;
    
    // 绘制多个飘动的音符
    for (int i = 0; i < 5; i++) {
      final noteOpacity = (animationValue + i * 0.3) % 1.0;
      paint.color = Colors.white.withOpacity(0.4 + noteOpacity * 0.6);
      
      final x = 10 + i * 15 + (animationValue * 20);
      final y = 20 + sin(animationValue * 3.14159 * 2 + i) * 15;
      
      // 绘制音符头部（圆形）
      canvas.drawCircle(Offset(x, y), 4, paint);
      
      // 绘制音符杆
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 2;
      canvas.drawLine(
        Offset(x + 3, y),
        Offset(x + 3, y - 12),
        paint,
      );
      
      // 绘制音符旗帜
      paint.style = PaintingStyle.fill;
      final flagPath = Path();
      flagPath.moveTo(x + 3, y - 12);
      flagPath.lineTo(x + 10, y - 8);
      flagPath.lineTo(x + 10, y - 4);
      flagPath.lineTo(x + 3, y - 8);
      flagPath.close();
      canvas.drawPath(flagPath, paint);
    }
    
    // 星星动画
    final center = Offset(size.width / 2, size.height / 2);
    
    // 主星星闪烁效果
    final opacity = (sin(animationValue * 3.14159 * 4) + 1) / 2;
    paint.color = Colors.white.withOpacity(0.6 + opacity * 0.4);
    paint.style = PaintingStyle.fill;
    
    // 绘制主星星
    _drawStar(canvas, paint, center, 12);
    
    // 绘制周围小星星
    for (int i = 0; i < 3; i++) {
      final angle = (i * 120 + animationValue * 180) * (3.14159 / 180);
      final smallStarCenter = Offset(
        center.dx + 18 * cos(angle),
        center.dy + 18 * sin(angle),
      );
      
      final smallOpacity = (sin(animationValue * 3.14159 * 3 + i) + 1) / 2;
      paint.color = Colors.white.withOpacity(0.3 + smallOpacity * 0.4);
      
      _drawStar(canvas, paint, smallStarCenter, 6);
    }
  }

  void _drawStar(Canvas canvas, Paint paint, Offset center, double radius) {
    final path = Path();
    for (int i = 0; i < 5; i++) {
      final angle = (i * 72 - 90) * (3.14159 / 180);
      final outerRadius = radius;
      final innerRadius = radius * 0.4;
      
      if (i == 0) {
        path.moveTo(
          center.dx + outerRadius * cos(angle),
          center.dy + outerRadius * sin(angle),
        );
      } else {
        path.lineTo(
          center.dx + outerRadius * cos(angle),
          center.dy + outerRadius * sin(angle),
        );
      }
      
      final innerAngle = ((i * 72 + 36) - 90) * (3.14159 / 180);
      path.lineTo(
        center.dx + innerRadius * cos(innerAngle),
        center.dy + innerRadius * sin(innerAngle),
      );
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(StarMusicNotePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

// 抖音榜：TikTok风格动画画笔
class TikTokStagePainter extends CustomPainter {
  final double animationValue;
  
  TikTokStagePainter({required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;
    
    // 圆柱体底部（椭圆）
    final bottomGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Colors.white.withOpacity(0.4),
        Colors.white.withOpacity(0.2),
      ],
    );
    
    paint.shader = bottomGradient.createShader(
      Rect.fromLTWH(0, size.height - 15, size.width, 15),
    );
    
    // 绘制圆柱底部
    canvas.drawOval(
      Rect.fromLTWH(0, size.height - 15, size.width, 15),
      paint,
    );
    
    // 圆柱体侧面
    final sideGradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        Colors.white.withOpacity(0.3),
        Colors.white.withOpacity(0.5),
        Colors.white.withOpacity(0.3),
      ],
    );
    
    paint.shader = sideGradient.createShader(
      Rect.fromLTWH(0, 10, size.width, size.height - 25),
    );
    
    // 绘制圆柱侧面
    canvas.drawRect(
      Rect.fromLTWH(0, 10, size.width, size.height - 25),
      paint,
    );
    
    // 圆柱体顶部（椭圆）
    final topGradient = RadialGradient(
      colors: [
        Colors.white.withOpacity(0.6),
        Colors.white.withOpacity(0.4),
      ],
    );
    
    paint.shader = topGradient.createShader(
      Rect.fromLTWH(0, 0, size.width, 20),
    );
    
    // 绘制圆柱顶部
    canvas.drawOval(
      Rect.fromLTWH(0, 0, size.width, 20),
      paint,
    );
    
    // 添加动画光效
    final lightOpacity = 0.3 + 0.2 * sin(animationValue * 2 * 3.14159);
    final lightPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.white.withOpacity(lightOpacity);
    
    canvas.drawOval(
      Rect.fromLTWH(size.width * 0.2, 5, size.width * 0.6, 10),
      lightPaint,
    );
  }

  @override
  bool shouldRepaint(TikTokStagePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

// 抖音榜：跳动音符画笔
class TikTokNotePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;
    
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 2;
    
    // 创建球体渐变效果
    final gradient = RadialGradient(
      center: const Alignment(-0.3, -0.3), // 光源位置
      colors: [
        const Color(0xFFFF6B9D), // 高光
        const Color(0xFFE91E63), // 主色
        const Color(0xFFC2185B), // 阴影
      ],
      stops: const [0.0, 0.6, 1.0],
    );
    
    paint.shader = gradient.createShader(
      Rect.fromCircle(center: center, radius: radius),
    );
    
    // 绘制主球体
    canvas.drawCircle(center, radius, paint);
    
    // 添加高光效果
    final highlightPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.white.withOpacity(0.4);
    
    canvas.drawCircle(
      Offset(center.dx - radius * 0.3, center.dy - radius * 0.3),
      radius * 0.3,
      highlightPaint,
    );
    
    // 添加小高光点
    final smallHighlightPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.white.withOpacity(0.8);
    
    canvas.drawCircle(
      Offset(center.dx - radius * 0.4, center.dy - radius * 0.4),
      radius * 0.1,
      smallHighlightPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 