import 'dart:io';
import 'dart:typed_data';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// 统一缓存管理器
/// 负责管理所有API数据的缓存，包括专辑封面、歌曲URL、歌词等
/// 支持内存缓存和本地存储，预留外部存储扩展接口
class UnifiedCacheManager {
  static final UnifiedCacheManager _instance = UnifiedCacheManager._internal();
  factory UnifiedCacheManager() => _instance;
  UnifiedCacheManager._internal();

  // 防止同一张图片的并发下载
  static final Map<String, Future<Uint8List?>> _pendingImageRequests = {};

  // 图片缓存更新事件流
  static final StreamController<String> _imageCacheUpdateController = StreamController<String>.broadcast();
  static Stream<String> get imageCacheUpdateStream => _imageCacheUpdateController.stream;

  // 歌词缓存更新事件流
  static final StreamController<String> _lyricsCacheUpdateController = StreamController<String>.broadcast();
  static Stream<String> get lyricsCacheUpdateStream => _lyricsCacheUpdateController.stream;

  // 防止重复触发缓存事件的标志
  static final Set<String> _eventTriggered = <String>{};

  /// 清除指定歌曲的事件触发标志，允许重新触发缓存事件
  static void clearEventFlag(String songId) {
    _eventTriggered.remove(songId);
    // 清除事件标志
  }

  final Dio _dio = Dio();
  
  // Hive存储盒子
  Box<Uint8List>? _audioFilesBox;  // 音乐文件缓存
  Box<String>? _lyricsBox;         // 歌词缓存
  Box<String>? _metadataBox;       // 元数据缓存
  Box<Uint8List>? _imageBox;       // 图片缓存
  
  // 内存缓存 - 移除大小限制，支持无限缓存
  final Map<String, String> _audioUrlMemoryCache = {};     // 音乐URL内存缓存
  final Map<String, String> _lyricsMemoryCache = {};       // 歌词内存缓存
  final Map<String, Uint8List> _imageMemoryCache = {};     // 图片内存缓存
  final Map<String, Uint8List> _audioFileMemoryCache = {}; // 音乐文件内存缓存
  final Map<String, Map<String, dynamic>> _metadataMemoryCache = {};

  // URL缓存 - 避免重复网络请求获取真实URL
  final Map<String, String> _imageUrlCache = {};           // 图片URL缓存
  final Map<String, String> _audioRealUrlCache = {};       // 音频真实URL缓存
  
  // 缓存配置 - 移除内存限制，支持无限存储
  static const Duration cacheExpireDuration = Duration(days: 30); // 缓存过期时间延长到30天

  // 音乐质量枚举
  static const String qualityStandard = 'mq';  // 标准音质
  static const String qualityHigh = 'hq';      // 高音质
  static const String qualityLossless = 'sq';  // 无损音质
  static const String qualityHiRes = 'hires';  // Hi-Res音质
  
  bool _isInitialized = false;

  /// 初始化缓存管理器
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await Hive.initFlutter();
      
      // 打开各种缓存盒子
      _audioFilesBox = await Hive.openBox<Uint8List>('audio_files');
      _lyricsBox = await Hive.openBox<String>('lyrics');
      _metadataBox = await Hive.openBox<String>('metadata');
      _imageBox = await Hive.openBox<Uint8List>('images');
      
      _isInitialized = true;
      
      // 缓存初始化完成
      
      // 临时禁用缓存清理，避免缓存被意外删除
      // await _cleanExpiredCache();
      
    } catch (e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Initialization failed: $e');
      }
    }
  }

  /// 获取音乐文件（带缓存和下载）
  /// [songId] 歌曲ID
  /// [quality] 音质类型 (mq, hq, sq, hires)
  /// [apiCall] API调用函数，返回真实的音乐URL
  Future<String?> getMusicFile(String songId, String quality, Future<String?> Function() apiCall) async {
    await _ensureInitialized();

    final cacheKey = '${songId}_$quality';

    // 1. 检查是否已有缓存文件
    final cachedFile = await _getCachedAudioFile(cacheKey);
    if (cachedFile != null) {
      // 音频缓存命中，不输出日志
      return cachedFile;
    }

    // 2. 调用API获取真实URL
    try {
      final realUrl = await apiCall();
      if (realUrl != null && realUrl.isNotEmpty && realUrl.startsWith('http')) {
        // 音乐URL获取成功

        // 3. 下载音乐文件并缓存
        final cachedFilePath = await _downloadAndCacheAudioFile(songId, quality, realUrl);
        if (cachedFilePath != null) {
          if (kDebugMode) {
            print('UnifiedCacheManager: Music file downloaded and cached: $cacheKey');
          }
          return cachedFilePath;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Failed to get music file for $cacheKey: $e');
      }
    }

    return null;
  }

  /// 检查是否有缓存的音乐文件
  Future<String?> _getCachedAudioFile(String cacheKey) async {
    // 1. 检查内存缓存
    if (_audioFileMemoryCache.containsKey(cacheKey)) {
      // 从内存缓存创建临时文件
      return await _createTempFileFromMemory(cacheKey);
    }

    // 2. 检查本地缓存
    final cachedData = _audioFilesBox?.get(cacheKey);
    if (cachedData != null) {
      // 检查是否过期
      final metadataKey = '${cacheKey}_meta';
      final metadataJson = _metadataBox?.get(metadataKey);
      if (metadataJson != null) {
        final metadata = json.decode(metadataJson);
        final timestamp = metadata['timestamp'] as int;
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);

        if (DateTime.now().difference(cacheTime) < cacheExpireDuration) {
          // 添加到内存缓存
          _audioFileMemoryCache[cacheKey] = cachedData;

          // 创建临时文件
          return await _createTempFileFromData(cacheKey, cachedData);
        } else {
          // 过期，删除缓存
          await _audioFilesBox?.delete(cacheKey);
          await _metadataBox?.delete(metadataKey);
        }
      }
    }

    return null;
  }

  /// 下载并缓存音乐文件
  Future<String?> _downloadAndCacheAudioFile(String songId, String quality, String realUrl) async {
    try {
      if (kDebugMode) {
        print('UnifiedCacheManager: Downloading music file: $songId ($quality)');
      }

      final response = await _dio.get(
        realUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final audioData = Uint8List.fromList(response.data);
        final cacheKey = '${songId}_$quality';

        // 缓存到本地
        await _audioFilesBox?.put(cacheKey, audioData);
        await _metadataBox?.put('${cacheKey}_meta', json.encode({
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'songId': songId,
          'quality': quality,
          'originalUrl': realUrl,
          'fileSize': audioData.length,
        }));

        // 缓存到内存
        _audioFileMemoryCache[cacheKey] = audioData;

        // 创建临时文件供播放使用
        return await _createTempFileFromData(cacheKey, audioData);
      }
    } catch (e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Failed to download music file: $e');
      }
    }

    return null;
  }

  /// 从内存缓存创建临时文件
  Future<String?> _createTempFileFromMemory(String cacheKey) async {
    final data = _audioFileMemoryCache[cacheKey];
    if (data != null) {
      return await _createTempFileFromData(cacheKey, data);
    }
    return null;
  }

  /// 从数据创建临时文件
  Future<String?> _createTempFileFromData(String cacheKey, Uint8List data) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/himusic_$cacheKey.mp3');

      await tempFile.writeAsBytes(data);

      // 临时文件创建成功

      return tempFile.path;
    } catch (e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Failed to create temp file: $e');
      }
      return null;
    }
  }

  /// 获取歌词（带缓存）
  Future<String?> getLyrics(String songId, Future<String?> Function() apiCall) async {
    await _ensureInitialized();

    final cacheKey = '${songId}_lyric';

    // 1. 检查内存缓存
    if (_lyricsMemoryCache.containsKey(cacheKey)) {
      return _lyricsMemoryCache[cacheKey];
    }

    // 2. 检查本地缓存
    final cachedData = await _getCachedData(_lyricsBox, cacheKey);
    if (cachedData != null) {
      final data = json.decode(cachedData);
      final lyrics = data['lyrics'] as String;

      // 添加到内存缓存
      _lyricsMemoryCache[cacheKey] = lyrics;
      return lyrics;
    }

    // 3. 调用API获取
    try {
      final lyrics = await apiCall();
      if (lyrics != null && lyrics.isNotEmpty) {
        // 缓存到本地和内存
        await _cacheData(_lyricsBox, cacheKey, json.encode({
          'lyrics': lyrics,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }));
        _lyricsMemoryCache[cacheKey] = lyrics;

        // 触发歌词缓存更新事件
        _lyricsCacheUpdateController.add(songId);

        return lyrics;
      }
    } catch (e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Failed to get lyrics: $e');
      }
    }

    return null;
  }

  /// 缓存图片URL，避免重复网络请求
  void cacheImageUrl(String songId, String imageUrl) {
    final cacheKey = '${songId}_image_url';
    _imageUrlCache[cacheKey] = imageUrl;

    if (kDebugMode) {
      print('UnifiedCacheManager: Cached image URL for $songId');
    }
  }

  /// 获取缓存的图片URL
  String? getCachedImageUrl(String songId) {
    final cacheKey = '${songId}_image_url';
    return _imageUrlCache[cacheKey];
  }

  /// 缓存音频真实URL，避免重复网络请求
  void cacheAudioRealUrl(String songId, String quality, String realUrl) {
    final cacheKey = '${songId}_${quality}_real_url';
    _audioRealUrlCache[cacheKey] = realUrl;

    if (kDebugMode) {
      print('UnifiedCacheManager: Cached audio real URL for $songId ($quality)');
    }
  }

  /// 获取缓存的音频真实URL
  String? getCachedAudioRealUrl(String songId, String quality) {
    final cacheKey = '${songId}_${quality}_real_url';
    return _audioRealUrlCache[cacheKey];
  }

  /// 检查是否有指定尺寸的图片缓存
  Future<bool> hasImageCache(String songId, int size) async {
    final cacheKey = '${songId}_pic_$size';

    // 检查内存缓存
    if (_imageMemoryCache.containsKey(cacheKey)) {
      return true;
    }

    // 检查磁盘缓存
    if (_imageBox != null) {
      return _imageBox!.containsKey(cacheKey);
    }

    return false;
  }

  /// 获取图片缓存的元数据
  Future<Map<String, dynamic>?> getImageMetadata(String songId, int size) async {
    final cacheKey = '${songId}_pic_$size';
    final metaKey = '${cacheKey}_meta';

    if (_metadataBox != null && _metadataBox!.containsKey(metaKey)) {
      try {
        final metadataJson = _metadataBox!.get(metaKey);
        if (metadataJson != null) {
          return json.decode(metadataJson) as Map<String, dynamic>;
        }
      } catch (e) {
        if (kDebugMode) {
          print('UnifiedCacheManager: Error parsing metadata for $cacheKey: $e');
        }
      }
    }

    return null;
  }

  /// 获取专辑封面图片数据（带智能尺寸缓存）
  /// [songId] 歌曲ID
  /// [imageUrl] 图片URL
  /// [requestedSize] 请求的图片尺寸
  /// [checkCacheOnly] 只检查缓存，不下载
  Future<Uint8List?> getAlbumCover(String songId, String? imageUrl, {int requestedSize = 100, bool checkCacheOnly = false}) async {
    // 获取专辑封面

    // 如果只是检查缓存，允许空URL
    if (!checkCacheOnly && (imageUrl == null || imageUrl.isEmpty)) {
      return null;
    }

    await _ensureInitialized();

    // 1. 优先查找完全匹配的缓存
    final exactCacheKey = '${songId}_pic_$requestedSize';
    final exactMatch = await _getCachedImage(exactCacheKey);
    if (exactMatch != null) {
      // 缓存命中，只在第一次时发出缓存更新事件以触发颜色提取
      if (!_eventTriggered.contains(songId)) {
        _eventTriggered.add(songId);
        // 触发颜色提取
        _imageCacheUpdateController.add(songId);
      }
      return exactMatch;
    }

    // 2. 查找该ID的所有图片缓存，寻找合适的尺寸
    final suitableImage = await _findSuitableImageCache(songId, requestedSize);
    if (suitableImage != null) {
      // 找到合适尺寸的缓存，只在第一次时发出缓存更新事件以触发颜色提取
      if (!_eventTriggered.contains(songId)) {
        _eventTriggered.add(songId);
        if (kDebugMode) {
          print('UnifiedCacheManager: Found cached image for $songId, triggering color extraction...');
        }
        _imageCacheUpdateController.add(songId);
      }
      return suitableImage;
    }

    // 如果只是检查缓存且没找到，返回null
    if (checkCacheOnly) {
      return null;
    }

    // 3. 检查是否已有正在进行的下载请求
    if (_pendingImageRequests.containsKey(exactCacheKey)) {
      return await _pendingImageRequests[exactCacheKey];
    }

    // 4. 下载新图片
    if (kDebugMode) {
      print('UnifiedCacheManager: Downloading album cover: $songId');
    }

    // 创建下载Future并缓存
    final downloadFuture = _performImageDownload(imageUrl!, exactCacheKey);
    _pendingImageRequests[exactCacheKey] = downloadFuture;

    try {
      final result = await downloadFuture;
      return result;
    } finally {
      // 下载完成后清理
      _pendingImageRequests.remove(exactCacheKey);
    }
  }

  /// 执行实际的图片下载
  Future<Uint8List?> _performImageDownload(String imageUrl, String cacheKey) async {
    try {
      if (kDebugMode) {
        print('UnifiedCacheManager: Downloading from URL: $imageUrl');
        print('UnifiedCacheManager: Cache key: $cacheKey');
      }

      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final imageData = Uint8List.fromList(response.data);

        // 缓存到本地和内存
        await _imageBox?.put(cacheKey, imageData);
        await _metadataBox?.put('${cacheKey}_meta', json.encode({
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'fileSize': imageData.length,
        }));

        // 添加到内存缓存
        _imageMemoryCache[cacheKey] = imageData;

        if (kDebugMode) {
          print('UnifiedCacheManager: Album cover downloaded and cached: $cacheKey');
        }

        // 发送缓存更新事件
        final songId = cacheKey.split('_pic_')[0];
        _imageCacheUpdateController.add(songId);

        return imageData;
      }
    } catch (e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Failed to download album cover: $e');
      }
    }

    return null;
  }

  /// 查找合适的图片缓存
  Future<Uint8List?> _findSuitableImageCache(String songId, int requestedSize) async {
    if (_imageBox == null || _metadataBox == null) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Image or metadata box is null for $songId');
      }
      return null;
    }

    if (kDebugMode) {
      print('UnifiedCacheManager: Searching cache for songId: $songId, requestedSize: $requestedSize');
    }

    final availableSizes = <int, String>{};

    // 遍历所有缓存，找到该songId的所有图片
    for (final key in _metadataBox!.keys) {
      if (key.endsWith('_meta')) {
        final metadataJson = _metadataBox!.get(key);
        if (metadataJson != null) {
          try {
            final metadata = json.decode(metadataJson);
            final cachedSongId = metadata['songId'] as String?;
            final size = metadata['size'] as int?;

            if (cachedSongId == songId && size != null) {
              // 检查是否过期
              final timestamp = metadata['timestamp'] as int;
              final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);

              if (DateTime.now().difference(cacheTime) < cacheExpireDuration) {
                final imageKey = key.replaceAll('_meta', '');
                availableSizes[size] = imageKey;
              }
            }
          } catch (e) {
            // 忽略解析错误的元数据
          }
        }
      }
    }

    if (kDebugMode) {
      print('UnifiedCacheManager: Found ${availableSizes.length} cached images for $songId: ${availableSizes.keys.toList()}');
    }

    if (availableSizes.isEmpty) return null;

    // 选择最合适的尺寸：
    // 1. 优先选择大于等于请求尺寸的最小图片
    // 2. 如果没有，选择最大的图片
    int? bestSize;

    final largerSizes = availableSizes.keys.where((size) => size >= requestedSize).toList();
    if (largerSizes.isNotEmpty) {
      largerSizes.sort();
      bestSize = largerSizes.first;
    } else {
      final allSizes = availableSizes.keys.toList();
      allSizes.sort((a, b) => b.compareTo(a)); // 降序
      bestSize = allSizes.first;
    }

    if (bestSize != null) {
      final imageKey = availableSizes[bestSize]!;
      return await _getCachedImage(imageKey);
    }

    return null;
  }

  /// 获取缓存的图片
  Future<Uint8List?> _getCachedImage(String cacheKey) async {
    // 1. 检查内存缓存
    if (_imageMemoryCache.containsKey(cacheKey)) {
      return _imageMemoryCache[cacheKey];
    }

    // 2. 检查本地缓存
    final cachedImage = _imageBox?.get(cacheKey);
    if (cachedImage != null) {
      // 添加到内存缓存
      _imageMemoryCache[cacheKey] = cachedImage;
      return cachedImage;
    }

    return null;
  }



  /// 清理过期缓存
  Future<void> _cleanExpiredCache() async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final expireTime = cacheExpireDuration.inMilliseconds;

      // 清理歌词缓存
      await _cleanExpiredBox(_lyricsBox, expireTime, now);

      // 清理音乐文件和图片缓存（通过metadata检查）
      if (_metadataBox != null) {
        final keysToDelete = <String>[];
        for (final key in _metadataBox!.keys) {
          if (key.endsWith('_meta')) {
            final metadataJson = _metadataBox!.get(key);
            if (metadataJson != null) {
              try {
                final metadata = json.decode(metadataJson);
                final timestamp = metadata['timestamp'] as int;
                if (now - timestamp > expireTime) {
                  keysToDelete.add(key);
                  final dataKey = key.replaceAll('_meta', '');

                  // 删除对应的数据
                  if (dataKey.contains('_pic_')) {
                    await _imageBox?.delete(dataKey);
                  } else if (dataKey.contains('_mq') || dataKey.contains('_hq') ||
                           dataKey.contains('_sq') || dataKey.contains('_hires')) {
                    await _audioFilesBox?.delete(dataKey);
                  }
                }
              } catch (e) {
                // 元数据格式错误，删除
                keysToDelete.add(key);
              }
            }
          }
        }

        for (final key in keysToDelete) {
          await _metadataBox!.delete(key);
        }
      }

      if (kDebugMode) {
        print('UnifiedCacheManager: Expired cache cleaned');
      }
    } catch (e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Failed to clean expired cache: $e');
      }
    }
  }

  /// 清理过期的盒子数据
  Future<void> _cleanExpiredBox(Box<String>? box, int expireTime, int now) async {
    if (box == null) return;
    
    final keysToDelete = <String>[];
    for (final key in box.keys) {
      final dataJson = box.get(key);
      if (dataJson != null) {
        try {
          final data = json.decode(dataJson);
          final timestamp = data['timestamp'] as int;
          if (now - timestamp > expireTime) {
            keysToDelete.add(key);
          }
        } catch (e) {
          // 数据格式错误，删除
          keysToDelete.add(key);
        }
      }
    }
    
    for (final key in keysToDelete) {
      await box.delete(key);
    }
  }

  /// 生成缓存键
  String _generateCacheKey(String input) {
    final bytes = utf8.encode(input);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 确保初始化
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// 获取缓存数据
  Future<String?> _getCachedData(Box<String>? box, String key) async {
    if (box == null) return null;
    
    final dataJson = box.get(key);
    if (dataJson != null) {
      try {
        final data = json.decode(dataJson);
        final timestamp = data['timestamp'] as int;
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        
        if (DateTime.now().difference(cacheTime) < cacheExpireDuration) {
          return dataJson;
        } else {
          // 过期，删除
          await box.delete(key);
        }
      } catch (e) {
        // 数据格式错误，删除
        await box.delete(key);
      }
    }
    
    return null;
  }

  /// 缓存数据
  Future<void> _cacheData(Box<String>? box, String key, String data) async {
    if (box != null) {
      await box.put(key, data);
    }
  }

  /// 预加载专辑封面（后台预加载）
  Future<void> preloadAlbumCover(String songId, String? imageUrl, {int size = 100}) async {
    if (imageUrl == null || imageUrl.isEmpty) return;

    // 异步预加载，不阻塞主线程
    getAlbumCover(songId, imageUrl, requestedSize: size).catchError((e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Preload failed for $songId: $e');
      }
    });
  }

  /// 预加载音乐文件
  Future<void> preloadMusicFile(String songId, String quality, Future<String?> Function() apiCall) async {
    // 异步预加载，不阻塞主线程
    getMusicFile(songId, quality, apiCall).catchError((e) {
      if (kDebugMode) {
        print('UnifiedCacheManager: Music preload failed for $songId ($quality): $e');
      }
    });
  }

  /// 清理所有缓存
  Future<void> clearAllCache() async {
    await _ensureInitialized();

    // 清理内存缓存
    _audioUrlMemoryCache.clear();
    _lyricsMemoryCache.clear();
    _imageMemoryCache.clear();
    _audioFileMemoryCache.clear();
    _metadataMemoryCache.clear();

    // 清理本地缓存
    await _audioFilesBox?.clear();
    await _lyricsBox?.clear();
    await _imageBox?.clear();
    await _metadataBox?.clear();

    if (kDebugMode) {
      print('UnifiedCacheManager: All cache cleared');
    }
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    return {
      'audioFiles': {
        'memory': _audioFileMemoryCache.length,
        'local': _audioFilesBox?.length ?? 0,
      },
      'lyrics': {
        'memory': _lyricsMemoryCache.length,
        'local': _lyricsBox?.length ?? 0,
      },
      'images': {
        'memory': _imageMemoryCache.length,
        'local': _imageBox?.length ?? 0,
      },
      'isInitialized': _isInitialized,
    };
  }

  /// 获取指定歌曲的所有缓存信息
  Future<Map<String, dynamic>> getSongCacheInfo(String songId) async {
    await _ensureInitialized();

    final info = <String, dynamic>{
      'songId': songId,
      'audioFiles': <String>[],
      'images': <Map<String, dynamic>>[],
      'hasLyrics': false,
    };

    // 检查元数据，找到该歌曲的所有缓存
    if (_metadataBox != null) {
      for (final key in _metadataBox!.keys) {
        if (key.endsWith('_meta')) {
          final metadataJson = _metadataBox!.get(key);
          if (metadataJson != null) {
            try {
              final metadata = json.decode(metadataJson);
              final cachedSongId = metadata['songId'] as String?;

              if (cachedSongId == songId) {
                final dataKey = key.replaceAll('_meta', '');

                if (dataKey.contains('_pic_')) {
                  final size = metadata['size'] as int?;
                  final fileSize = metadata['fileSize'] as int?;
                  info['images'].add({
                    'size': size,
                    'fileSize': fileSize,
                    'cacheKey': dataKey,
                  });
                } else if (dataKey.endsWith('_lyric')) {
                  info['hasLyrics'] = true;
                } else if (dataKey.contains('_mq') || dataKey.contains('_hq') ||
                         dataKey.contains('_sq') || dataKey.contains('_hires')) {
                  final quality = dataKey.split('_').last;
                  final fileSize = metadata['fileSize'] as int?;
                  info['audioFiles'].add({
                    'quality': quality,
                    'fileSize': fileSize,
                    'cacheKey': dataKey,
                  });
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    }

    return info;
  }

  /// 释放资源
  Future<void> dispose() async {
    await _audioFilesBox?.close();
    await _lyricsBox?.close();
    await _imageBox?.close();
    await _metadataBox?.close();

    _audioUrlMemoryCache.clear();
    _lyricsMemoryCache.clear();
    _imageMemoryCache.clear();
    _audioFileMemoryCache.clear();
    _metadataMemoryCache.clear();
  }

  // TODO: 预留外部存储扩展接口
  // 后期可以扩展为保存到用户存储卡
  
  /// 设置外部存储路径（预留接口）
  Future<void> setExternalStoragePath(String path) async {
    // TODO: 实现外部存储逻辑
    // 1. 检查存储权限
    // 2. 创建目录结构
    // 3. 迁移现有缓存
    if (kDebugMode) {
      print('UnifiedCacheManager: External storage path set to: $path');
      print('Note: External storage implementation is pending');
    }
  }
  
  /// 导出缓存到外部存储（预留接口）
  Future<bool> exportCacheToExternal() async {
    // TODO: 实现缓存导出逻辑
    if (kDebugMode) {
      print('UnifiedCacheManager: Cache export to external storage is pending');
    }
    return false;
  }
}
