<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:minHeight="120dp">

    <!-- 主要内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 专辑封面 -->
        <ImageView
            android:id="@+id/iv_album_art"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp"
            android:scaleType="centerCrop"
            android:background="@drawable/album_art_background"
            android:src="@drawable/default_album_art" />

        <!-- 歌曲信息区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 标题和关闭按钮行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- 歌曲标题 -->
                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="歌曲标题"
                    android:textSize="16sp"
                    android:textColor="#FF333333"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <!-- 关闭按钮 -->
                <ImageButton
                    android:id="@+id/btn_close"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="@drawable/notification_button_background"
                    android:src="@drawable/ic_close"
                    android:scaleType="centerInside"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- 艺术家 -->
            <TextView
                android:id="@+id/tv_artist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="艺术家"
                android:textSize="14sp"
                android:textColor="#FF666666"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 控制按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="12dp">

        <!-- 喜欢按钮 -->
        <ImageButton
            android:id="@+id/btn_like"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/notification_button_background"
            android:src="@drawable/ic_heart_outline"
            android:scaleType="centerInside"
            android:layout_marginEnd="20dp" />

        <!-- 上一首按钮 -->
        <ImageButton
            android:id="@+id/btn_previous"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="@drawable/notification_button_background"
            android:src="@drawable/ic_skip_previous"
            android:scaleType="centerInside"
            android:layout_marginEnd="16dp" />

        <!-- 播放/暂停按钮 -->
        <ImageButton
            android:id="@+id/btn_play_pause"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/notification_button_background"
            android:src="@drawable/ic_play"
            android:scaleType="centerInside"
            android:layout_marginEnd="16dp" />

        <!-- 下一首按钮 -->
        <ImageButton
            android:id="@+id/btn_next"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="@drawable/notification_button_background"
            android:src="@drawable/ic_skip_next"
            android:scaleType="centerInside"
            android:layout_marginEnd="20dp" />

        <!-- 歌词按钮 -->
        <TextView
            android:id="@+id/btn_lyrics"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="词"
            android:textSize="12sp"
            android:textColor="@android:color/white"
            android:background="@drawable/word_tag_background"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:paddingTop="3dp"
            android:paddingBottom="3dp"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</LinearLayout>
