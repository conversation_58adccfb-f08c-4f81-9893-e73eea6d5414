import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';

class PlayerControls extends StatelessWidget {
  final bool isPlaying;
  final bool isShuffle;
  final bool isRepeat;
  final VoidCallback? onPrevious;
  final VoidCallback? onPlayPause;
  final VoidCallback? onNext;
  final VoidCallback? onShuffle;
  final VoidCallback? onRepeat;
  
  const PlayerControls({
    super.key,
    this.isPlaying = false,
    this.isShuffle = false,
    this.isRepeat = false,
    this.onPrevious,
    this.onPlayPause,
    this.onNext,
    this.onShuffle,
    this.onRepeat,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 随机播放
        IconButton(
          onPressed: onShuffle,
          icon: Icon(
            Icons.shuffle,
            color: isShuffle ? AppColors.primaryColor : AppColors.textSecondary,
            size: 24,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 上一首
        IconButton(
          onPressed: onPrevious,
          icon: const Icon(
            Icons.skip_previous,
            color: AppColors.textPrimary,
            size: 40,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 播放/暂停
        Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            color: AppColors.primaryColor,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryColor.withOpacity(0.5),
                blurRadius: 16,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: IconButton(
            onPressed: onPlayPause,
            icon: Icon(
              isPlaying ? Icons.pause : Icons.play_arrow,
              color: AppColors.grey3,
              size: 40,
            ),
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 下一首
        IconButton(
          onPressed: onNext,
          icon: const Icon(
            Icons.skip_next,
            color: AppColors.textPrimary,
            size: 40,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 循环播放
        IconButton(
          onPressed: onRepeat,
          icon: Icon(
            isRepeat ? Icons.repeat_one : Icons.repeat,
            color: isRepeat ? AppColors.primaryColor : AppColors.textSecondary,
            size: 24,
          ),
        ),
      ],
    );
  }
} 