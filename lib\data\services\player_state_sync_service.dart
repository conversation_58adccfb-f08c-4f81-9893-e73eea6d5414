import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/song_model.dart';
import '../providers/mini_player_provider.dart';
import '../providers/enhanced_mini_player_provider.dart';
import '../providers/playback_state_provider.dart';
import 'audio_player_service.dart';
import 'kuwo_music_service.dart';

/// 播放器状态同步服务
/// 负责将AudioPlayerService的状态同步到各个Provider
class PlayerStateSyncService {
  static final PlayerStateSyncService _instance = PlayerStateSyncService._internal();
  
  factory PlayerStateSyncService() {
    return _instance;
  }
  
  PlayerStateSyncService._internal();

  final AudioPlayerService _audioPlayerService = AudioPlayerService();
  
  MiniPlayerProvider? _miniPlayerProvider;
  EnhancedMiniPlayerProvider? _enhancedMiniPlayerProvider;
  PlaybackStateProvider? _playbackStateProvider;
  
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;
  StreamSubscription? _playerStateSubscription;
  
  bool _isInitialized = false;

  /// 初始化同步服务
  void initialize({
    MiniPlayerProvider? miniPlayerProvider,
    EnhancedMiniPlayerProvider? enhancedMiniPlayerProvider,
    PlaybackStateProvider? playbackStateProvider,
  }) {
    if (_isInitialized) return;

    _miniPlayerProvider = miniPlayerProvider;
    _enhancedMiniPlayerProvider = enhancedMiniPlayerProvider;
    _playbackStateProvider = playbackStateProvider;

    _setupListeners();
    _isInitialized = true;

    if (kDebugMode) {
      print('PlayerStateSyncService: Initialized with providers');
      print('- MiniPlayerProvider: ${_miniPlayerProvider != null}');
      print('- EnhancedMiniPlayerProvider: ${_enhancedMiniPlayerProvider != null}');
      print('- PlaybackStateProvider: ${_playbackStateProvider != null}');
    }
  }

  /// 设置监听器
  void _setupListeners() {
    // 监听播放位置变化
    _positionSubscription = _audioPlayerService.audioPlayer.positionStream.listen((position) {
      final duration = _audioPlayerService.audioPlayer.duration ?? Duration.zero;
      _syncProgress(position, duration);
    });

    // 监听音频时长变化
    _durationSubscription = _audioPlayerService.audioPlayer.durationStream.listen((duration) {
      if (duration != null) {
        final position = _audioPlayerService.audioPlayer.position;
        _syncProgress(position, duration);
      }
    });

    // 监听播放状态变化
    _playerStateSubscription = _audioPlayerService.audioPlayer.playerStateStream.listen((state) {
      _syncPlayingState(state.playing);
    });
  }

  /// 同步当前歌曲
  void syncCurrentSong(Song song) {
    // 同步歌曲状态

    // 同步到原有的MiniPlayerProvider（使用setCurrentSong方法）
    _miniPlayerProvider?.setCurrentSong(song);

    // 同步到增强版Provider（使用updateCurrentSong方法）
    _enhancedMiniPlayerProvider?.updateCurrentSong(song);
    _enhancedMiniPlayerProvider?.show();

    // 同步到新的统一Provider（使用updateCurrentSong方法）
    _playbackStateProvider?.updateCurrentSong(song);
    _playbackStateProvider?.show();
  }

  /// 同步播放状态
  void _syncPlayingState(bool isPlaying) async {
    // 移除重复的播放状态同步日志

    // 同步到UI状态提供者
    if (isPlaying) {
      _miniPlayerProvider?.play();
      _enhancedMiniPlayerProvider?.play();
      _playbackStateProvider?.play();
    } else {
      _miniPlayerProvider?.pause();
      _enhancedMiniPlayerProvider?.pause();
      _playbackStateProvider?.pause();
    }

    // 处理前台服务状态变化（移到这里，避免重复调用）
    await _handlePlaybackStateChange(isPlaying);
  }

  /// 处理播放状态变化 - 管理前台服务
  Future<void> _handlePlaybackStateChange(bool isPlaying) async {
    try {
      // 通过AudioPlayerService的公共方法来处理前台服务状态
      await _audioPlayerService.handlePlaybackStateChange(isPlaying);
    } catch (e) {
      if (kDebugMode) {
        print('PlayerStateSyncService: Error handling playback state change: $e');
      }
    }
  }

  /// 同步播放进度
  void _syncProgress(Duration position, Duration duration) {
    _enhancedMiniPlayerProvider?.updateProgress(position, duration);
    _playbackStateProvider?.updateProgress(position, duration);

    // 原有的MiniPlayerProvider可能没有进度更新方法，这里可以扩展
    // _miniPlayerProvider?.updateProgress(position, duration);
  }

  /// 开始播放歌曲
  Future<void> playSong(Song song) async {
    try {
      // 确保歌曲有音频URL
      Song songToPlay = song;
      if (song.audioUrl == null || song.audioUrl!.isEmpty) {
        if (kDebugMode) {
          print('PlayerStateSyncService: No audio URL found, fetching from API for: ${song.title}');
        }

        try {
          final kuwoService = KuwoMusicService();
          final audioUrl = await kuwoService.getSongPlayUrl(song.id, quality: 'mq');

          if (audioUrl != null && audioUrl.isNotEmpty) {
            songToPlay = song.copyWith(audioUrl: audioUrl);
            if (kDebugMode) {
              print('PlayerStateSyncService: Got audio URL for: ${song.title}');
            }
          } else {
            if (kDebugMode) {
              print('PlayerStateSyncService: Failed to get audio URL for: ${song.title}');
            }
            return; // 无法获取URL，停止播放
          }
        } catch (e) {
          if (kDebugMode) {
            print('PlayerStateSyncService: Error fetching audio URL: $e');
          }
          return; // 获取URL失败，停止播放
        }
      }

      // 先同步状态
      syncCurrentSong(songToPlay);

      // 然后播放
      await _audioPlayerService.playSong(songToPlay);

      if (kDebugMode) {
        print('PlayerStateSyncService: Song playback started successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('PlayerStateSyncService: Error playing song: $e');
      }
      rethrow;
    }
  }

  /// 暂停播放
  Future<void> pause() async {
    await _audioPlayerService.pause();
  }

  /// 继续播放
  Future<void> resume() async {
    await _audioPlayerService.resume();
  }

  /// 停止播放
  Future<void> stop() async {
    await _audioPlayerService.stop();

    // 清除播放状态
    _miniPlayerProvider?.clearCurrentSong();
    _enhancedMiniPlayerProvider?.hide();
  }

  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    await _audioPlayerService.seekTo(position);
  }



  /// 获取当前歌曲
  Song? get currentSong => _audioPlayerService.currentSong;

  /// 获取播放器实例
  AudioPlayerService get audioPlayerService => _audioPlayerService;

  /// 清理资源
  void dispose() {
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _playerStateSubscription?.cancel();
    
    _miniPlayerProvider = null;
    _enhancedMiniPlayerProvider = null;
    _isInitialized = false;

    if (kDebugMode) {
      print('PlayerStateSyncService: Disposed');
    }
  }
}
