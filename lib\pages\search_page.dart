import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../data/models/kuwo_song_model.dart';
import '../data/models/song_model.dart';
import '../data/services/kuwo_music_service.dart';
import '../data/services/playlist_manager.dart';
import '../data/services/enhanced_playlist_manager.dart';
import '../core/theme/app_colors.dart';
import '../core/theme/app_text_styles.dart';
import '../widgets/common/custom_app_bar.dart';
import '../widgets/home/<USER>';
import '../widgets/original_music_bar.dart';
import '../data/providers/enhanced_mini_player_provider.dart';
import '../data/services/player_state_sync_service.dart';
import 'player_page.dart';
import 'package:flutter/foundation.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final KuwoMusicService _musicService = KuwoMusicService();
  final PlaylistManager _playlistManager = PlaylistManager();
  
  List<Song> _searchResults = [];
  bool _isLoading = false;
  String _errorMessage = '';
  int _currentPage = 0;
  final int _pageSize = 10;
  bool _hasMoreData = true;
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  Future<void> _searchSongs(String keyword, {bool refresh = true}) async {
    if (keyword.isEmpty) return;
    
    if (refresh) {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
        _currentPage = 0;
        if (refresh) {
          _searchResults = [];
        }
      });
    } else {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
    }
    
    try {
      final searchResponse = await _musicService.searchSongs(
        keyword,
        page: _currentPage,
        pageSize: _pageSize,
      );
      
      final songs = _musicService.convertToSongs(searchResponse.songList);
      
      setState(() {
        if (refresh) {
          _searchResults = songs;
        } else {
          _searchResults.addAll(songs);
        }
        _isLoading = false;
        _currentPage++;
        _hasMoreData = searchResponse.songList.length >= _pageSize;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to search songs: $e';
      });
    }
  }
  
  void _loadMoreSongs() {
    if (!_isLoading && _hasMoreData) {
      _searchSongs(_searchController.text, refresh: false);
    }
  }
  
  void _onSongTap(int index) async {
    if (index < 0 || index >= _searchResults.length) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final Song song = _searchResults[index];

      // 1. 如果需要，先获取音频URL
      String? audioUrl = song.audioUrl;
      if (audioUrl == null || audioUrl!.isEmpty) {
        audioUrl = await _musicService.getSongPlayUrl(song.id);

        if (audioUrl == null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to get audio URL')),
            );
            setState(() {
              _isLoading = false;
            });
          }
          return;
        }

        // 更新歌曲
        final updatedSong = song.copyWith(audioUrl: audioUrl);
        _searchResults[index] = updatedSong;
      }

      // 2. 添加歌曲到播放列表（搜索模式：添加单曲并播放）
      final enhancedPlaylistManager = Provider.of<EnhancedPlaylistManager>(context, listen: false);
      enhancedPlaylistManager.addSongAndPlay(_searchResults[index]);

      // 3. 立即结束加载状态并导航到播放页面
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // 4. 先导航到播放页面
        final navigationFuture = Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayerPage(
              song: _searchResults[index],
              autoPlay: true,  // 让播放页面自己处理播放
            ),
          ),
        );

        // 5. 同时启动播放（非阻塞）
        final syncService = PlayerStateSyncService();
        syncService.playSong(_searchResults[index]).catchError((e) {
          print('播放失败: $e');
        });

        // 等待导航完成
        await navigationFuture;
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // 主要内容区域
          SafeArea(
            child: Column(
              children: [
                // 自定义应用栏
                CustomAppBar(
                  title: 'Search',
                  showBackButton: true,
                ),

                // 搜索栏
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Song/Video/Playlist/Album/Lyrics/Radio/Novel',
                      prefixIcon: Icon(Icons.search, color: Colors.grey[400]),
                      suffixIcon: IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey[400]),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchResults = [];
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    ),
                    onSubmitted: (value) {
                      _searchSongs(value);
                    },
                  ),
                ),

                // 搜索结果 - 为播放栏预留底部空间
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 90), // 为播放栏预留空间
                    child: _buildSearchResults(),
                  ),
                ),
              ],
            ),
          ),

          // 原版E4A风格底部播放栏 - 稍微上移
          Positioned(
            left: 0,
            right: 0,
            bottom: 16, // 稍微上移，避免贴底
            child: const OriginalMusicBar(),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSearchResults() {
    if (_isLoading && _searchResults.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFF8BBE4)),
        ),
      );
    }
    
    if (_errorMessage.isNotEmpty && _searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 60,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 60,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 16),
            Text(
              '搜索你喜欢的音乐、歌手、专辑等',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    return NotificationListener<ScrollNotification>(
      onNotification: (scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          _loadMoreSongs();
          return true;
        }
        return false;
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _searchResults.length + (_isLoading && _hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _searchResults.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFF8BBE4)),
                ),
              ),
            );
          }
          
          final song = _searchResults[index];
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: SongItem(
              song: song,
              onTap: () => _onSongTap(index),
              onMoreTap: () {
                // Show more options
              },
            ),
          );
        },
      ),
    );
  }
} 