import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../data/services/enhanced_playlist_manager.dart';
import '../../data/services/player_state_sync_service.dart';
import '../../data/models/song_model.dart';
import '../common/cached_album_image.dart';

/// 播放列表底部弹窗
class PlaylistBottomSheet extends StatelessWidget {
  const PlaylistBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EnhancedPlaylistManager>(
      builder: (context, playlistManager, child) {
        final currentPlaylist = playlistManager.currentPlaylist;
        final playNextQueue = playlistManager.playNextQueue;
        final currentIndex = playlistManager.currentIndex;
        final playMode = playlistManager.playMode;

        return Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // 顶部标题栏
              _buildHeader(context, playlistManager),
              
              // 播放模式指示器
              _buildPlayModeIndicator(playMode, playlistManager),
              
              // 播放列表
              Expanded(
                child: _buildPlaylist(
                  context, 
                  currentPlaylist, 
                  playNextQueue, 
                  currentIndex, 
                  playlistManager
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建顶部标题栏
  Widget _buildHeader(BuildContext context, EnhancedPlaylistManager playlistManager) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          // 标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '播放列表',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${playlistManager.currentPlaylist.length}首歌曲',
                  style: TextStyle(
                    color: Colors.black.withOpacity(0.6),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          // 清空按钮
          TextButton(
            onPressed: () {
              _showClearPlaylistDialog(context, playlistManager);
            },
            child: Text(
              '清空',
              style: TextStyle(
                color: Colors.black.withOpacity(0.8),
                fontSize: 14,
              ),
            ),
          ),

          // 关闭按钮
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.close,
              color: Colors.black.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建播放模式指示器
  Widget _buildPlayModeIndicator(PlayMode playMode, EnhancedPlaylistManager playlistManager) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getPlayModeIcon(playMode),
            color: Colors.black.withOpacity(0.8),
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            playlistManager.getPlayModeDisplayName(),
            style: TextStyle(
              color: Colors.black.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建播放列表
  Widget _buildPlaylist(
    BuildContext context,
    List<Song> currentPlaylist,
    List<Song> playNextQueue,
    int currentIndex,
    EnhancedPlaylistManager playlistManager,
  ) {
    // 合并显示：下一首播放队列 + 当前播放列表
    final allSongs = <Song>[];
    final songTypes = <String>[]; // 'queue', 'current', 'normal'

    // 获取当前正在播放的歌曲（用于高亮显示）
    final currentSong = playlistManager.currentSong;

    // 添加下一首播放队列
    for (final song in playNextQueue) {
      allSongs.add(song);
      // 如果队列中的歌曲是当前播放的歌曲，标记为current
      if (currentSong != null && song.id == currentSong.id) {
        songTypes.add('current');
      } else {
        songTypes.add('queue');
      }
    }

    // 添加当前播放列表
    for (int i = 0; i < currentPlaylist.length; i++) {
      allSongs.add(currentPlaylist[i]);
      // 如果列表中的歌曲是当前播放的歌曲，标记为current
      if (currentSong != null && currentPlaylist[i].id == currentSong.id) {
        songTypes.add('current');
      } else {
        songTypes.add('normal');
      }
    }

    if (allSongs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_note_outlined,
              size: 64,
              color: Colors.black.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              '播放列表为空',
              style: TextStyle(
                color: Colors.black.withOpacity(0.6),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: allSongs.length,
      itemBuilder: (context, index) {
        final song = allSongs[index];
        final type = songTypes[index];
        
        return _buildSongItem(
          context,
          song,
          type,
          index,
          playlistManager,
        );
      },
    );
  }

  /// 构建歌曲项目
  Widget _buildSongItem(
    BuildContext context,
    Song song,
    String type,
    int index,
    EnhancedPlaylistManager playlistManager,
  ) {
    final isCurrentSong = type == 'current';
    final isQueueSong = type == 'queue';

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isCurrentSong 
            ? Colors.white.withOpacity(0.1) 
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 播放状态指示器
            if (isCurrentSong)
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 12,
                ),
              )
            else if (isQueueSong)
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.8),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.queue_music,
                  color: Colors.white,
                  size: 12,
                ),
              )
            else
              SizedBox(
                width: 20,
                child: Text(
                  '${index + 1}',
                  style: TextStyle(
                    color: Colors.black.withOpacity(0.6),
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            
            const SizedBox(width: 12),
            
            // 专辑封面
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: CachedAlbumImage(
                songId: song.id,
                width: 40,
                height: 40,
                fit: BoxFit.cover,
              ),
            ),
          ],
        ),
        
        title: Text(
          song.title,
          style: TextStyle(
            color: isCurrentSong ? Theme.of(context).primaryColor : Colors.black,
            fontSize: 14,
            fontWeight: isCurrentSong ? FontWeight.w600 : FontWeight.normal,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),

        subtitle: Text(
          song.artistName,
          style: TextStyle(
            color: Colors.black.withOpacity(0.6),
            fontSize: 12,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        
        trailing: PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            color: Colors.black.withOpacity(0.6),
          ),
          onSelected: (value) {
            switch (value) {
              case 'remove':
                if (isQueueSong) {
                  // TODO: 从队列中移除
                } else {
                  playlistManager.removeSong(index);
                }
                break;
              case 'play_next':
                playlistManager.addToPlayNext(song);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'play_next',
              child: Text('下一首播放'),
            ),
            const PopupMenuItem(
              value: 'remove',
              child: Text('从列表移除'),
            ),
          ],
        ),
        
        onTap: () {
          _onSongTap(context, song, type, index, playlistManager);
        },
      ),
    );
  }

  /// 处理歌曲点击事件
  void _onSongTap(
    BuildContext context,
    Song song,
    String type,
    int index,
    EnhancedPlaylistManager playlistManager,
  ) async {
    try {
      if (type == 'queue') {
        // 点击队列中的歌曲，直接播放
        final syncService = PlayerStateSyncService();
        await syncService.playSong(song);

        // 从队列中移除这首歌曲（因为已经开始播放了）
        playlistManager.playNextQueue.remove(song);
        playlistManager.notifyListeners();
      } else {
        // 点击播放列表中的歌曲，更新索引并播放
        final realIndex = index - playlistManager.playNextQueue.length; // 减去队列长度
        if (realIndex >= 0 && realIndex < playlistManager.currentPlaylist.length) {
          // 找到歌曲在原始播放列表中的索引
          final songIndex = playlistManager.currentPlaylist.indexWhere((s) => s.id == song.id);
          if (songIndex != -1) {
            // 更新当前索引
            playlistManager.setCurrentIndex(songIndex);

            // 播放歌曲
            final syncService = PlayerStateSyncService();
            await syncService.playSong(song);
          }
        }
      }

      // 关闭播放列表
      Navigator.pop(context);
    } catch (e) {
      if (kDebugMode) {
        print('PlaylistBottomSheet: Error playing song: $e');
      }
    }
  }

  /// 获取播放模式图标
  IconData _getPlayModeIcon(PlayMode mode) {
    switch (mode) {
      case PlayMode.listRepeat:
        return Icons.repeat;
      case PlayMode.singleRepeat:
        return Icons.repeat_one;
      case PlayMode.shuffle:
        return Icons.shuffle;
    }
  }

  /// 显示清空播放列表确认对话框
  void _showClearPlaylistDialog(BuildContext context, EnhancedPlaylistManager playlistManager) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: const Text(
          '清空播放列表',
          style: TextStyle(color: Colors.black),
        ),
        content: const Text(
          '确定要清空当前播放列表吗？',
          style: TextStyle(color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              playlistManager.clearPlaylist();
              Navigator.pop(context);
              Navigator.pop(context); // 关闭播放列表
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

/// 显示播放列表底部弹窗
void showPlaylistBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (context) => const PlaylistBottomSheet(),
  );
}
