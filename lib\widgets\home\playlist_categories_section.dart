import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../data/models/playlist_category_model.dart';
import '../../data/models/song_model.dart';
import '../../data/services/playlist_api_service.dart';
import '../../data/services/enhanced_playlist_manager.dart';
import '../../pages/player_page.dart';
import '../common/cached_album_image.dart';

/// 歌单分类区域组件
class PlaylistCategoriesSection extends StatefulWidget {
  const PlaylistCategoriesSection({super.key});

  @override
  State<PlaylistCategoriesSection> createState() => _PlaylistCategoriesSectionState();
}

class _PlaylistCategoriesSectionState extends State<PlaylistCategoriesSection> {
  final PlaylistApiService _apiService = PlaylistApiService();
  PlaylistCategoryData? _categoryData;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _apiService.initialize();
    _loadPlaylistCategories();
  }

  /// 加载歌单分类数据
  Future<void> _loadPlaylistCategories() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await _apiService.getPlaylistCategories();
      
      if (response.success) {
        setState(() {
          _categoryData = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response.message;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load playlist categories: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_errorMessage != null) {
      return _buildErrorWidget();
    }

    if (_categoryData == null || _categoryData!.categories.isEmpty) {
      return _buildEmptyWidget();
    }

    return _buildCategoriesWidget();
  }

  /// 构建加载状态Widget
  Widget _buildLoadingWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '歌单分类',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态Widget
  Widget _buildErrorWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '歌单分类',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red[200]!),
            ),
            child: Column(
              children: [
                Icon(Icons.error_outline, color: Colors.red[400], size: 48),
                const SizedBox(height: 8),
                Text(
                  _errorMessage ?? 'Unknown error',
                  style: TextStyle(color: Colors.red[700]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: _loadPlaylistCategories,
                  child: const Text('重试'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态Widget
  Widget _buildEmptyWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '歌单分类',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(Icons.music_note_outlined, 
                       color: Colors.grey[400], size: 48),
                  const SizedBox(height: 8),
                  Text(
                    '暂无歌单分类',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分类列表Widget
  Widget _buildCategoriesWidget() {
    final categories = _categoryData!.categoryNames;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '歌单分类',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Text(
                '${_categoryData!.total}首歌曲',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 分类列表
          ...categories.map((categoryName) => _buildCategorySection(categoryName)),
        ],
      ),
    );
  }

  /// 构建单个分类区域
  Widget _buildCategorySection(String categoryName) {
    final songs = _categoryData!.getSongsForCategory(categoryName);
    if (songs.isEmpty) return const SizedBox.shrink();

    // 根据分类类型选择不同的显示样式
    if (_isRankingCategory(categoryName)) {
      return _buildRankingCategorySection(categoryName, songs);
    } else {
      return _buildRegularCategorySection(categoryName, songs);
    }
  }

  /// 判断是否为榜单类型分类
  bool _isRankingCategory(String categoryName) {
    return categoryName.contains('榜') || 
           categoryName == '榜单歌曲' ||
           categoryName.contains('排行');
  }

  /// 构建榜单类型分类区域（纵向列表）
  Widget _buildRankingCategorySection(String categoryName, List<PlaylistCategorySong> songs) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分类标题
          _buildCategoryHeader(categoryName, songs.length),
          const SizedBox(height: 12),

          // 歌曲列表（纵向）
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: songs.take(6).map((song) => _buildSongListItem(song, songs)).toList(),
            ),
          ),

          // 查看更多按钮
          if (songs.length > 6) _buildViewMoreButton(categoryName, songs),
        ],
      ),
    );
  }

  /// 构建常规分类区域（横向滚动）
  Widget _buildRegularCategorySection(String categoryName, List<PlaylistCategorySong> songs) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分类标题
          _buildCategoryHeader(categoryName, songs.length),
          const SizedBox(height: 12),

          // 横向滚动歌曲卡片
          SizedBox(
            height: 180,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: songs.length,
              itemBuilder: (context, index) {
                return _buildSongCard(songs[index], songs);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分类标题
  Widget _buildCategoryHeader(String categoryName, int songCount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          categoryName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        Text(
          '$songCount首',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建歌曲卡片（横向滚动）
  Widget _buildSongCard(PlaylistCategorySong song, List<PlaylistCategorySong> allSongs) {
    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: 12),
      child: GestureDetector(
        onTap: () => _onSongTap(song, allSongs),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 专辑封面
            Container(
              width: 140,
              height: 140,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedAlbumImage(
                  imageUrl: song.img,
                  size: 140,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 8),

            // 歌曲信息
            Text(
              song.name,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              song.artist,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建歌曲列表项（纵向列表）
  Widget _buildSongListItem(PlaylistCategorySong song, List<PlaylistCategorySong> allSongs) {
    return InkWell(
      onTap: () => _onSongTap(song, allSongs),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[100]!,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            // 专辑封面
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: CachedAlbumImage(
                  imageUrl: song.img,
                  size: 50,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 12),

            // 歌曲信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    song.name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${song.artist} · ${song.album}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // 时长和更多按钮
            Row(
              children: [
                Text(
                  song.durationFormatted,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[500],
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.more_vert,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建查看更多按钮
  Widget _buildViewMoreButton(String categoryName, List<PlaylistCategorySong> songs) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: InkWell(
        onTap: () => _showAllSongs(categoryName, songs),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '查看全部 ${songs.length} 首',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.blue[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                Icons.keyboard_arrow_down,
                size: 16,
                color: Colors.blue[600],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理歌曲点击事件
  Future<void> _onSongTap(PlaylistCategorySong categorySong, List<PlaylistCategorySong> allSongs) async {
    try {
      // 转换为Song模型
      final song = categorySong.toSong();
      final songList = allSongs.map((s) => s.toSong()).toList();

      // 找到当前歌曲在列表中的索引
      final currentIndex = songList.indexWhere((s) => s.id == song.id);

      if (currentIndex == -1) {
        if (kDebugMode) {
          print('PlaylistCategoriesSection: Song not found in list');
        }
        return;
      }

      // 使用增强播放列表管理器设置播放列表并播放
      final enhancedPlaylistManager = Provider.of<EnhancedPlaylistManager>(context, listen: false);
      enhancedPlaylistManager.setPlaylistAndPlay(songList, currentIndex);

      // 导航到播放页面
      if (mounted) {
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayerPage(
              song: song,
              autoPlay: false, // 播放已经由 setPlaylistAndPlay() 处理
            ),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('PlaylistCategoriesSection: Error playing song: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('播放失败: $e')),
        );
      }
    }
  }

  /// 显示分类的所有歌曲
  void _showAllSongs(String categoryName, List<PlaylistCategorySong> songs) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildAllSongsBottomSheet(categoryName, songs),
    );
  }

  /// 构建显示所有歌曲的底部弹窗
  Widget _buildAllSongsBottomSheet(String categoryName, List<PlaylistCategorySong> songs) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    categoryName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // 歌曲列表
          Expanded(
            child: ListView.builder(
              itemCount: songs.length,
              itemBuilder: (context, index) {
                return _buildSongListItem(songs[index], songs);
              },
            ),
          ),
        ],
      ),
    );
  }
}
