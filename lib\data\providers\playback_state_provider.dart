import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/song_model.dart';
import '../../utils/color_extractor.dart';
import '../services/kuwo_music_service.dart';
import '../services/unified_cache_manager.dart';

/// 统一的播放状态管理器
/// 合并了原来的 MiniPlayerProvider 和 EnhancedMiniPlayerProvider 的功能
/// 负责管理全局播放状态、当前歌曲、UI状态等
class PlaybackStateProvider extends ChangeNotifier {
  StreamSubscription<String>? _imageCacheSubscription;

  PlaybackStateProvider() {
    // 监听图片缓存更新事件
    _imageCacheSubscription = UnifiedCacheManager.imageCacheUpdateStream.listen((songId) {
      _onImageCacheUpdated(songId);
    });
  }
  // === 基础播放状态 ===
  bool _isVisible = false;
  bool _isPlaying = false;
  Song? _currentSong;
  double _progress = 0.0;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;

  // === UI状态 ===
  bool _isExpanded = false;
  bool _isLoadingColors = false;
  
  // === 颜色主题 ===
  ExtractedColors _currentColors = ExtractedColors.defaultColors();
  
  // === Getters ===
  bool get isVisible => _isVisible;
  bool get isPlaying => _isPlaying;
  Song? get currentSong => _currentSong;
  double get progress => _progress;
  Duration get position => _position;
  Duration get duration => _duration;
  bool get isExpanded => _isExpanded;
  bool get isLoadingColors => _isLoadingColors;
  ExtractedColors get currentColors => _currentColors;

  // === 基础播放控制 ===
  
  /// 显示播放栏
  void show() {
    if (!_isVisible) {
      _isVisible = true;
      notifyListeners();
    }
  }

  /// 隐藏播放栏
  void hide() {
    if (_isVisible) {
      _isVisible = false;
      notifyListeners();
    }
  }

  /// 开始播放
  void play() {
    if (!_isPlaying) {
      _isPlaying = true;
      notifyListeners();
    }
  }

  /// 暂停播放
  void pause() {
    if (_isPlaying) {
      _isPlaying = false;
      notifyListeners();
    }
  }

  /// 设置播放状态
  void setPlaying(bool playing) {
    if (_isPlaying != playing) {
      _isPlaying = playing;
      notifyListeners();
    }
  }

  // === 歌曲管理 ===
  
  /// 更新当前歌曲
  void updateCurrentSong(Song song) {
    if (_currentSong?.id != song.id) {
      _currentSong = song;
      // 播放时不立即提取颜色，使用默认颜色
      _currentColors = ExtractedColors.defaultColors();
      notifyListeners();
    }
  }

  /// 清除当前歌曲
  void clearCurrentSong() {
    _currentSong = null;
    _progress = 0.0;
    _position = Duration.zero;
    _duration = Duration.zero;
    _isPlaying = false;
    resetColors();
    notifyListeners();
  }

  // === 进度管理 ===
  
  /// 更新播放进度
  void updateProgress(Duration position, Duration duration) {
    _position = position;
    _duration = duration;
    
    if (duration.inMilliseconds > 0) {
      _progress = position.inMilliseconds / duration.inMilliseconds;
    } else {
      _progress = 0.0;
    }
    
    notifyListeners();
  }

  /// 设置播放进度
  void setProgress(double progress) {
    _progress = progress.clamp(0.0, 1.0);
    notifyListeners();
  }

  // === UI状态管理 ===
  
  /// 设置展开状态
  void setExpanded(bool expanded) {
    if (_isExpanded != expanded) {
      _isExpanded = expanded;
      notifyListeners();
    }
  }

  /// 切换展开状态
  void toggleExpanded() {
    _isExpanded = !_isExpanded;
    notifyListeners();
  }

  // === 颜色主题管理 ===
  
  /// 从歌曲提取颜色主题
  Future<void> _extractColorsFromSong(Song song) async {
    _isLoadingColors = true;
    notifyListeners();

    try {
      // 只使用高质量URL，不使用默认的coverUrl（可能是默认图标）
      String? imageUrl = song.highQualityCoverUrl;

      // 如果没有高质量URL，尝试获取
      if (imageUrl == null || imageUrl.isEmpty) {
        final kuwoService = KuwoMusicService();
        imageUrl = await kuwoService.getHighQualityAlbumCover(song.id);
      }

      // 如果还是没有URL，使用默认颜色
      if (imageUrl == null || imageUrl.isEmpty) {
        _currentColors = ExtractedColors.defaultColors();
        _isLoadingColors = false;
        notifyListeners();
        return;
      }

      final colors = await ColorExtractor.extractColorsFromSong(song.id, imageUrl);
      
      _currentColors = colors;
      _isLoadingColors = false;
      
      // 颜色提取完成
      
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('PlaybackStateProvider: Failed to extract colors: $e');
      }
      
      _currentColors = ExtractedColors.defaultColors();
      _isLoadingColors = false;
      notifyListeners();
    }
  }

  /// 手动设置颜色主题
  void setColors(ExtractedColors colors) {
    _currentColors = colors;
    notifyListeners();
  }

  /// 重置到默认颜色
  void resetColors() {
    _currentColors = ExtractedColors.defaultColors();
    notifyListeners();
  }

  // === 工具方法 ===
  
  /// 获取格式化的时间字符串
  String get formattedPosition {
    return _formatDuration(_position);
  }

  String get formattedDuration {
    return _formatDuration(_duration);
  }

  String get formattedProgress {
    return '${formattedPosition} / ${formattedDuration}';
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // === 兼容性方法 ===
  // 为了保持与现有代码的兼容性，提供原有Provider的所有方法
  
  /// 兼容 MiniPlayerProvider 的方法
  void showMiniPlayer() => show();
  void hideMiniPlayer() => hide();
  
  /// 兼容 EnhancedMiniPlayerProvider 的方法
  void updateSong(Song song) => updateCurrentSong(song);

  /// 处理图片缓存更新事件
  void _onImageCacheUpdated(String songId) {
    // 如果当前歌曲的图片缓存完成，开始提取颜色
    if (_currentSong?.id == songId) {
      // 提取颜色
      _extractColorsFromSong(_currentSong!);
    }
  }

  @override
  void dispose() {
    _imageCacheSubscription?.cancel();
    super.dispose();
  }
}
