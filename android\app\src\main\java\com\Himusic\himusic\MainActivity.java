package com.Himusic.himusic;

import android.Manifest;
import android.app.AppOpsManager;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.content.ComponentName;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;

public class MainActivity extends FlutterActivity {
    private static final String TAG = "MainActivity";
    private static final String CHANNEL = "music_notification";
    private static final String SERVICE_CHANNEL = "music_service";
    private static final int REQUEST_CODE_NOTIFICATION_PERMISSION = 100;

    private MusicNotificationManager notificationManager;
    private MusicForegroundService musicService;
    private boolean isServiceBound = false;
    
    // 服务连接器
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            Log.d(TAG, "Service connected");
            MusicForegroundService.MusicServiceBinder binder = (MusicForegroundService.MusicServiceBinder) service;
            musicService = binder.getService();
            isServiceBound = true;
            
            // 延迟设置MethodChannel，确保服务完全初始化
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (musicService != null && notificationManager != null) {
                    try {
                        musicService.setMethodChannel(new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL));
                        Log.d(TAG, "MethodChannel set successfully for music service");
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to set MethodChannel for music service", e);
                    }
                }
            }, 1000); // 延迟1秒设置MethodChannel
        }

        @Override
        public void onServiceDisconnected(ComponentName arg0) {
            Log.d(TAG, "Service disconnected");
            musicService = null;
            isServiceBound = false;
        }
    };

    @Override
    public void configureFlutterEngine(FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);

        notificationManager = new MusicNotificationManager(this);

        // 通知管理通道
        MethodChannel methodChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL);
        notificationManager.setMethodChannel(methodChannel);

        methodChannel.setMethodCallHandler(
                (call, result) -> {
                    switch (call.method) {
                        case "initializeNotification":
                            notificationManager.initialize();
                            result.success(null);
                            break;
                        case "showNotification":
                            notificationManager.showNotification(call.arguments);
                            result.success(null);
                            break;
                        case "updatePlaybackState":
                            notificationManager.updatePlaybackState(call.arguments);
                            result.success(null);
                            break;
                        case "updateProgress":
                            notificationManager.updateProgress(call.arguments);
                            result.success(null);
                            break;
                        case "updateLikeState":
                            notificationManager.updateLikeState(call.arguments);
                            result.success(null);
                            break;
                        case "hideNotification":
                            notificationManager.hideNotification();
                            result.success(null);
                            break;
                        default:
                            result.notImplemented();
                            break;
                    }
                }
        );
        
        // 音乐服务控制通道
        MethodChannel serviceChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), SERVICE_CHANNEL);
        serviceChannel.setMethodCallHandler(
                (call, result) -> {
                    switch (call.method) {
                        case "startMusicService":
                            boolean started = startMusicService();
                            result.success(started);
                            break;
                        case "stopMusicService":
                            boolean stopped = stopMusicService();
                            result.success(stopped);
                            break;
                        case "acquireWakeLock":
                            boolean acquired = acquireWakeLock();
                            result.success(acquired);
                            break;
                        case "releaseWakeLock":
                            boolean released = releaseWakeLock();
                            result.success(released);
                            break;
                        case "isWakeLockHeld":
                            boolean held = isWakeLockHeld();
                            result.success(held);
                            break;
                        case "ensureServicePersistence":
                            ensureServicePersistence();
                            result.success(true);
                            break;
                        case "allowServiceManagement":
                            allowServiceManagement();
                            result.success(true);
                            break;
                        case "isServiceRunning":
                            boolean running = isServiceRunning();
                            result.success(running);
                            break;
                        case "updateForegroundNotification":
                            updateForegroundNotification(call.arguments);
                            result.success(true);
                            break;
                        default:
                            result.notImplemented();
                            break;
                    }
                }
        );
    }
    
    /**
     * 启动音乐前台服务
     */
    private boolean startMusicService() {
        // 检查服务是否已经绑定
        if (isServiceBound && musicService != null) {
            Log.d(TAG, "Music service already running and bound, skipping start");
            return true;
        }

        try {
            Intent serviceIntent = new Intent(this, MusicForegroundService.class);
            serviceIntent.setAction("START_MUSIC_SERVICE");
            
            // 启动前台服务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent);
            } else {
                startService(serviceIntent);
            }
            
            // 绑定服务 - 延迟绑定，确保服务已启动
            if (!isServiceBound) {
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    try {
                        boolean bindResult = bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE);
                        Log.d(TAG, "Service binding initiated, result: " + bindResult);
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to bind service", e);
                    }
                }, 1000); // 延迟1秒绑定
            }
            
            Log.d(TAG, "Music service start initiated successfully");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to start music service", e);
            return false;
        }
    }
    
    /**
     * 停止音乐前台服务
     */
    private boolean stopMusicService() {
        try {
            // 解绑服务
            if (isServiceBound) {
                unbindService(serviceConnection);
                isServiceBound = false;
            }
            
            // 停止服务
            Intent serviceIntent = new Intent(this, MusicForegroundService.class);
            serviceIntent.setAction("STOP_MUSIC_SERVICE");
            stopService(serviceIntent);
            
            Log.d(TAG, "Music service stopped successfully");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop music service", e);
            return false;
        }
    }
    
    /**
     * 获取WakeLock
     */
    private boolean acquireWakeLock() {
        if (musicService != null) {
            musicService.acquireWakeLock();
            Log.d(TAG, "WakeLock acquire requested");
            return true;
        } else {
            Log.w(TAG, "Music service not available for WakeLock acquire, will retry after service connection");
            // 尝试延迟执行，等待服务连接
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (musicService != null) {
                    musicService.acquireWakeLock();
                    Log.d(TAG, "WakeLock acquire retried successfully");
                }
            }, 1000);
            return false;
        }
    }
    
    /**
     * 释放WakeLock
     */
    private boolean releaseWakeLock() {
        if (musicService != null) {
            musicService.releaseWakeLock();
            Log.d(TAG, "WakeLock release requested");
            return true;
        }
        Log.w(TAG, "Music service not available for WakeLock release");
        return false;
    }
    
    /**
     * 检查WakeLock状态
     */
    private boolean isWakeLockHeld() {
        if (musicService != null) {
            boolean held = musicService.isWakeLockHeld();
            Log.d(TAG, "WakeLock status checked: " + held);
            return held;
        }
        Log.w(TAG, "Music service not available for WakeLock status check");
        return false;
    }
    
    /**
     * 确保服务持久运行
     */
    private void ensureServicePersistence() {
        if (musicService != null) {
            musicService.ensureServicePersistence();
            Log.d(TAG, "Service persistence ensured");
        } else {
            Log.w(TAG, "Music service not available for persistence ensure");
        }
    }
    
    /**
     * 允许系统管理服务
     */
    private void allowServiceManagement() {
        if (musicService != null) {
            musicService.allowServiceManagement();
            Log.d(TAG, "Service management allowed");
        } else {
            Log.w(TAG, "Music service not available for management allow");
        }
    }
    
    /**
     * 检查服务是否运行
     */
    private boolean isServiceRunning() {
        if (musicService != null) {
            boolean running = musicService.isServiceRunning();
            Log.d(TAG, "Service running status: " + running);
            return running;
        }
        Log.w(TAG, "Music service not available for running status check");
        return false;
    }
    
    /**
     * 更新前台服务通知
     */
    private void updateForegroundNotification(Object arguments) {
        if (musicService != null && arguments instanceof Map) {
            Map<String, Object> args = (Map<String, Object>) arguments;
            Boolean isMusicPlaying = (Boolean) args.get("isMusicPlaying");
            if (isMusicPlaying != null) {
                musicService.updateForegroundNotification(isMusicPlaying);
                Log.d(TAG, "Foreground notification update requested, music playing: " + isMusicPlaying);
            }
        } else {
            Log.w(TAG, "Music service not available for foreground notification update");
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 检查并申请通知权限
        checkAndRequestNotificationPermission();
        
        // 移除自动启动音乐服务，由Flutter端控制
        // startMusicService();
    }

    private void checkAndRequestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 需要动态申请POST_NOTIFICATIONS权限
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {

                // 动态请求权限
                ActivityCompat.requestPermissions(
                    this,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS},
                    REQUEST_CODE_NOTIFICATION_PERMISSION
                );
            }
        } else {
            // Android 13以下检查通知开关是否开启
            if (!isNotificationEnabled(this)) {
                // 可以选择提示用户或直接跳转到设置页
                // 这里暂时不强制跳转，让用户自己决定
                System.out.println("通知权限未开启，建议用户手动开启");
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CODE_NOTIFICATION_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限已授予，可发送通知
                System.out.println("通知权限已授予");
            } else {
                // 权限被拒绝，可以提示用户或引导至设置页
                System.out.println("通知权限被拒绝");
                // 可选：引导用户到设置页
                // openNotificationSettings(this);
            }
        }
    }

    /**
     * 检测通知权限是否开启（兼容Android 4.4+）
     */
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static boolean isNotificationEnabled(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Android 8.0+ 使用官方API
            NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            return manager.areNotificationsEnabled();
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            // Android 4.4~7.0 通过反射检测
            AppOpsManager appOps = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
            ApplicationInfo appInfo = context.getApplicationInfo();
            String pkg = context.getPackageName();
            int uid = appInfo.uid;
            try {
                Class<?> appOpsClass = Class.forName(AppOpsManager.class.getName());
                Method checkOp = appOpsClass.getMethod("checkOpNoThrow", Integer.TYPE, Integer.TYPE, String.class);
                Field opField = appOpsClass.getDeclaredField("OP_POST_NOTIFICATION");
                int opValue = (Integer) opField.get(Integer.class);
                return (Integer) checkOp.invoke(appOps, opValue, uid, pkg) == AppOpsManager.MODE_ALLOWED;
            } catch (Exception e) {
                return true; // 反射失败时默认返回true
            }
        }
        return true; // Android 4.4以下默认返回true
    }

    /**
     * 引导用户跳转到通知设置页
     */
    public static void openNotificationSettings(Context context) {
        Intent intent = new Intent();
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ 跳转到应用专属通知设置
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.getPackageName());
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // Android 5.0~7.0 跳转到应用通知设置
                intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
                intent.putExtra("app_package", context.getPackageName());
                intent.putExtra("app_uid", context.getApplicationInfo().uid);
            } else {
                // 低版本跳转到应用详情页
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
            }
            context.startActivity(intent);
        } catch (Exception e) {
            // 备用方案：通用应用详情页
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.setData(Uri.parse("package:" + context.getPackageName()));
            context.startActivity(intent);
        }
    }

    @Override
    protected void onDestroy() {
        // 清理服务连接
        if (isServiceBound) {
            try {
                unbindService(serviceConnection);
                isServiceBound = false;
                Log.d(TAG, "Service unbound successfully");
            } catch (Exception e) {
                Log.e(TAG, "Error unbinding service", e);
            }
        }
        
        // 清理通知管理器
        if (notificationManager != null) {
            notificationManager.cleanup();
        }
        
        super.onDestroy();
        Log.d(TAG, "MainActivity destroyed");
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 确保服务连接正常
        if (!isServiceBound) {
            startMusicService();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 在暂停时确保服务持续运行
        if (musicService != null) {
            musicService.ensureServicePersistence();
        }
    }
}
