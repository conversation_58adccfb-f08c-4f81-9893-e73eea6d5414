import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';

import '../core/theme/app_colors.dart';
import '../core/theme/app_text_styles.dart';
import '../data/models/playlist_model.dart';
import '../data/models/song_model.dart';
import '../widgets/common/custom_app_bar.dart';
import '../widgets/home/<USER>';
import '../data/providers/mock_data_provider.dart';
import '../data/services/playlist_manager.dart';
import '../data/services/kuwo_music_service.dart';
import 'player_page.dart';

class PlaylistDetailPage extends StatefulWidget {
  final String? playlistId;
  final Playlist? playlist;
  
  const PlaylistDetailPage({
    super.key,
    this.playlistId,
    this.playlist,
  }) : assert(playlistId != null || playlist != null, 
             'Either playlistId or playlist must be provided');

  @override
  State<PlaylistDetailPage> createState() => _PlaylistDetailPageState();
}

class _PlaylistDetailPageState extends State<PlaylistDetailPage> {
  late Playlist _playlist;
  late ScrollController _scrollController;
  bool _showTitle = false;
  final PlaylistManager _playlistManager = PlaylistManager();
  final KuwoMusicService _musicService = KuwoMusicService();
  List<Song> _songsWithUrl = [];
  bool _isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    _loadPlaylist();
  }
  
  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }
  
  void _onScroll() {
    final showTitle = _scrollController.offset > 220;
    if (showTitle != _showTitle) {
      setState(() {
        _showTitle = showTitle;
      });
    }
  }
  
  void _loadPlaylist() {
    if (widget.playlist != null) {
      _playlist = widget.playlist!;
    } else {
      // TODO: 从数据库中加载歌单数据
      final dataProvider = Provider.of<MockDataProvider>(context, listen: false);
      _playlist = dataProvider.playlists.firstWhere(
        (playlist) => playlist.id == widget.playlistId,
        orElse: () => dataProvider.playlists.first,
      );
    }
    // 初始化歌曲列表
    _songsWithUrl = List.from(_playlist.songs);
  }
  
  void _onSongTap(int index) async {
    if (index < 0 || index >= _playlist.songs.length) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final Song song = _playlist.songs[index];
      
      // 1. 如果需要，先获取音频URL
      String? audioUrl = song.audioUrl;
      if (audioUrl == null || audioUrl!.isEmpty) {
        audioUrl = await _musicService.getSongPlayUrl(song.id);
        
        if (audioUrl == null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to get audio URL')),
            );
            setState(() {
              _isLoading = false;
            });
          }
          return;
        }
        
        // 更新歌曲
        final updatedSong = song.copyWith(audioUrl: audioUrl);
        _songsWithUrl[index] = updatedSong;
      }
      
      // 2. 设置播放列表但不立即播放
      await _playlistManager.setPlaylist(_songsWithUrl, index);
      
      if (mounted) {
        // 3. 结束加载状态
        setState(() {
          _isLoading = false;
        });
        
        // 4. 先导航到播放页面
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayerPage(
              song: _songsWithUrl[index],
              autoPlay: true,  // 标记需要自动播放
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }
  
  Future<void> _playAll() async {
    if (_playlist.songs.isEmpty) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // 获取第一首歌
      final Song firstSong = _playlist.songs.first;
      
      // 1. 如果需要，先获取音频URL
      String? audioUrl = firstSong.audioUrl;
      if (audioUrl == null || audioUrl!.isEmpty) {
        audioUrl = await _musicService.getSongPlayUrl(firstSong.id);
        
        if (audioUrl == null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to get audio URL')),
            );
            setState(() {
              _isLoading = false;
            });
          }
          return;
        }
        
        // 更新歌曲
        final updatedSong = firstSong.copyWith(audioUrl: audioUrl);
        _songsWithUrl[0] = updatedSong;
      }
      
      // 2. 设置播放列表但不立即播放
      await _playlistManager.setPlaylist(_songsWithUrl, 0);
      
      if (mounted) {
        // 3. 结束加载状态
        setState(() {
          _isLoading = false;
        });
        
        // 4. 先导航到播放页面
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayerPage(
              song: _songsWithUrl[0],
              autoPlay: true,  // 标记需要自动播放
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }
  
  Future<void> _playSong(Song song, int index) async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // 1. 如果需要，先获取音频URL
      String? audioUrl = song.audioUrl;
      if (audioUrl == null || audioUrl!.isEmpty) {
        audioUrl = await _musicService.getSongPlayUrl(song.id);
        
        if (audioUrl == null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to get audio URL')),
            );
            setState(() {
              _isLoading = false;
            });
          }
          return;
        }
        
        // 更新歌曲
        final updatedSong = song.copyWith(audioUrl: audioUrl);
        _songsWithUrl[index] = updatedSong;
      }
      
      // 2. 设置播放列表但不立即播放
      await _playlistManager.setPlaylist(_songsWithUrl, index);
      
      if (mounted) {
        // 3. 结束加载状态
        setState(() {
          _isLoading = false;
        });
        
        // 4. 先导航到播放页面
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayerPage(
              song: _songsWithUrl[index],
              autoPlay: true,  // 标记需要自动播放
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // 自定义应用栏
            CustomAppBar(
              title: _showTitle ? _playlist.name : '',
              showBackButton: true,
              actions: [
                IconButton(
                  icon: const Icon(
                    Icons.search,
                    color: AppColors.textPrimary,
                  ),
                  onPressed: () {
                    // TODO: 搜索歌单内歌曲
                  },
                ),
                IconButton(
                  icon: const Icon(
                    Icons.more_vert,
                    color: AppColors.textPrimary,
                  ),
                  onPressed: () {
                    // TODO: 显示更多选项
                  },
                ),
              ],
            ),
            
            // 主要内容
            Expanded(
              child: Stack(
                children: [
                  CustomScrollView(
                    controller: _scrollController,
                    slivers: [
                      // 歌单头部
                      SliverToBoxAdapter(
                        child: _buildPlaylistHeader(),
                      ),
                      
                      // 播放控制条
                      SliverToBoxAdapter(
                        child: _buildPlayControls(),
                      ),
                      
                      // 歌曲列表
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final song = _playlist.songs[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 4,
                              ),
                              child: SongItem(
                                song: song,
                                onTap: () => _onSongTap(index),
                                onMoreTap: () {
                                  // TODO: 显示更多选项
                                },
                              ),
                            );
                          },
                          childCount: _playlist.songs.length,
                        ),
                      ),
                      
                      // 底部空间
                      const SliverToBoxAdapter(
                        child: SizedBox(height: 80),
                      ),
                    ],
                  ),
                  
                  // 加载指示器
                  if (_isLoading)
                    Container(
                      color: Colors.black.withOpacity(0.3),
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildPlaylistHeader() {
    return Container(
      height: 240,
      width: double.infinity,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 背景图片
          CachedNetworkImage(
            imageUrl: _playlist.coverUrl ?? 'https://picsum.photos/400/200',
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: AppColors.cardBackground,
            ),
            errorWidget: (context, url, error) => Container(
              color: AppColors.cardBackground,
              child: const Icon(Icons.broken_image, size: 50),
            ),
          ),
          
          // 毛玻璃效果
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              color: Colors.black.withOpacity(0.5),
            ),
          ),
          
          // 内容
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 封面
                Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: CachedNetworkImage(
                    imageUrl: _playlist.coverUrl ?? 'https://picsum.photos/300',
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppColors.cardBackground,
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppColors.cardBackground,
                      child: const Icon(Icons.music_note, size: 50),
                    ),
                  ),
                ),
                
                const SizedBox(width: 20),
                
                // 歌单信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        _playlist.name,
                        style: AppTextStyles.headingMedium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'By ${_playlist.creatorName}',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_playlist.songs.length} songs • ${_playlist.totalDurationFormatted}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPlayControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          // 播放全部按钮
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _playAll,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Play All'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: AppColors.grey3,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 收藏按钮
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppColors.cardBackground,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                _playlist.isLiked ? Icons.favorite : Icons.favorite_border,
                color: _playlist.isLiked ? Colors.red : AppColors.textPrimary,
              ),
              onPressed: () {
                // TODO: 收藏歌单
              },
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 下载按钮
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppColors.cardBackground,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(
                Icons.download,
                color: AppColors.textPrimary,
              ),
              onPressed: () {
                // TODO: 下载歌单
              },
            ),
          ),
        ],
      ),
    );
  }
} 