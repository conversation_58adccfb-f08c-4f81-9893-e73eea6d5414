import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;

import '../data/providers/enhanced_mini_player_provider.dart';
import '../data/providers/playback_state_provider.dart';
import '../data/models/song_model.dart';
import '../data/services/audio_player_service.dart';
import '../data/services/player_state_sync_service.dart';
import '../data/services/global_color_service.dart';
import '../pages/player_page.dart';
import 'common/cached_album_image.dart';
import 'player/playlist_bottom_sheet.dart';

/// 完全复刻E4A木子音乐播放栏的设计
/// 严格按照 an9_muzi.xml 布局文件实现
class OriginalMusicBar extends StatefulWidget {
  const OriginalMusicBar({super.key});

  @override
  State<OriginalMusicBar> createState() => _OriginalMusicBarState();
}

class _OriginalMusicBarState extends State<OriginalMusicBar>
    with TickerProviderStateMixin {

  late AnimationController _slideController;
  final AudioPlayerService _audioPlayerService = AudioPlayerService();
  final GlobalColorService _globalColorService = GlobalColorService();

  @override
  void initState() {
    super.initState();

    // 初始化滑入动画控制器
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }
  
  /// 使用全局颜色服务提取颜色主题
  Future<void> _extractColorsFromCurrentSong(Song song) async {
    await _globalColorService.extractColorsForSong(song);
  }
  
  /// 导航到播放页面
  void _navigateToPlayerPage(Song song) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlayerPage(song: song),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    // 使用新的统一Provider，同时保持对旧Provider的兼容性
    return Consumer2<PlaybackStateProvider, EnhancedMiniPlayerProvider>(
      builder: (context, playbackProvider, enhancedProvider, child) {
        // 优先使用新Provider，如果没有数据则回退到旧Provider
        final song = playbackProvider.currentSong ?? enhancedProvider.currentSong;
        final isPlaying = playbackProvider.isPlaying || enhancedProvider.isPlaying;

        // 当歌曲改变时重新提取颜色
        if (song != null && song.id != _globalColorService.currentSongId) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _extractColorsFromCurrentSong(song);
          });
        }

        return ListenableBuilder(
          listenable: _globalColorService,
          builder: (context, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 1),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: _slideController,
                curve: Curves.easeOutCubic,
              )),
              child: _buildOriginalDesignPlayer(playbackProvider, song, isPlaying),
            );
          },
        );
      },
    );
  }

  /// 构建完全复刻原设计的播放器
  /// 严格按照 an9_muzi.xml 布局文件实现
  /// 尺寸参考: a9o=67.5dp, a9n=45dp, a9e=50dp, a9h=17.5dp, ars=30dp
  Widget _buildOriginalDesignPlayer(PlaybackStateProvider provider, Song? song, bool isPlaying) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 18),
      height: 67.5, // a9o
      color: Colors.transparent, // 明确设置透明背景，避免半透明图层
      child: Stack(
        children: [
          // 背景容器 - 对应 ArcRelativeLayout (du6)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0, // 改为0，让播放栏紧贴底部
            child: Container(
              height: 50, // 增加高度，让播放栏更贴近底部
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(22.5), // 更圆润的边角
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    _globalColorService.currentColors.gradientStart,
                    _globalColorService.currentColors.gradientEnd,
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: _globalColorService.currentColors.primary.withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
            ),
          ),

          // 专辑封面 - 悬浮效果，挡住左边圆角，对应 AlbImageView (pic)
          Positioned(
            left: 0, // 调整位置，避免被截取但仍挡住部分圆角
            bottom: 0, // 改为0，与背景容器对齐
            child: _buildAlbumCover(song),
          ),

          // 专辑封面右侧光碟边缘装饰 - 对应 e0n (minibar_pic_right_album)
          Positioned(
            left: 55, // 专辑封面右边缘外侧 (50+2=52)
            bottom: 0, // 改为0，与背景容器对齐
            child: Image.asset(
              'assets/images/minibar_pic_right_album.webp',
              width: 10,
              height: 54,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // 如果图片加载失败，使用渐变替代
                return Container(
                  width: 8,
                  height: 50,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        Color(0x33FFFFFF),
                        Colors.transparent,
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

          // 歌曲信息区域 - 对应 BottomViewPager (e0a) - 单行显示
          Positioned(
            left: 66, // 调整左边距，适应专辑封面新位置 (52+8+6=66)
            right: 110, // 为右侧控制区域预留空间
            top: 16, // 调整顶部位置
            bottom: 0, // 改为0，与背景容器对齐
            child: _buildSongInfoArea(song),
          ),

          // 右侧控制区域 - 对应 e08
          Positioned(
            right: 10, // aro
            top: 16, // 调整顶部位置
            bottom: 0, // 改为0，与背景容器对齐
            child: Center(
              child: _buildControlArea(provider, isPlaying),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建专辑封面 - 对应 AlbImageView (pic)
  /// 严格按照原设计：50x50dp，圆角8，默认显示fad.png，无旋转
  Widget _buildAlbumCover(Song? song) {
    return GestureDetector(
      onTap: song != null ? () => _navigateToPlayerPage(song) : null,
      child: Container(
        width: 55, // a9e
        height: 55, // a9e
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: song != null
            ? RoundedAlbumImage(
                songId: song.id,
                width: 67.5,
                height: 67.5,
                borderRadius: 8, // 统一使用500px高质量图片
                errorWidget: Image.asset(
                  'assets/images/fad.png',
                  fit: BoxFit.cover,
                ),
                placeholder: Image.asset(
                  'assets/images/fad.png',
                  fit: BoxFit.cover,
                ),
              )
            : ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.asset(
                  'assets/images/fad.png',
                  fit: BoxFit.cover,
                ),
              ),
      ),
    );
  }

  /// 构建歌曲信息区域 - 对应 BottomViewPager (e0a)
  /// 单行显示：歌曲名-歌手名，避免布局溢出
  Widget _buildSongInfoArea(Song? song) {
    final songTitle = song?.title ?? '未知歌曲';
    final artistName = song?.artistName ?? '未知歌手';
    final displayText = '$songTitle - $artistName';

    return GestureDetector(
      onTap: song != null ? () => _navigateToPlayerPage(song) : null,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Center(
          child: Text(
            displayText,
            style: TextStyle(
              color: _globalColorService.currentColors.textColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  /// 构建右侧控制区域 - 对应 e08
  Widget _buildControlArea(PlaybackStateProvider provider, bool isPlaying) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 圆形进度条和播放按钮 - 对应 FrameLayout (f9r)
        _buildCircularProgressPlayButton(provider, isPlaying),
        const SizedBox(width: 10),
        // 播放列表按钮 - 对应 e0h
        _buildPlaylistButton(),
      ],
    );
  }

  /// 构建圆形进度条和播放按钮 - 对应原设计的 FrameLayout (f9r)
  /// 包含 ProgressBar (e0i), ArcImageView (e0s), ImageView (e0o)
  Widget _buildCircularProgressPlayButton(PlaybackStateProvider provider, bool isPlaying) {
    return StreamBuilder<Duration>(
      stream: _audioPlayerService.audioPlayer.positionStream,
      builder: (context, positionSnapshot) {
        final position = positionSnapshot.data ?? Duration.zero;
        final duration = _audioPlayerService.audioPlayer.duration ?? Duration.zero;
        final progress = duration.inMilliseconds > 0
            ? position.inMilliseconds / duration.inMilliseconds
            : 0.0;

        return SizedBox(
          width: 34, // 缩小播放按钮区域
          height: 34, // 缩小播放按钮区域
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 圆形进度条背景 - 对应 ArcImageView (e0s)
              CustomPaint(
                size: const Size(34, 34), // 缩小进度条
                painter: _CircularProgressPainter(
                  progress: progress,
                  progressColor: Colors.white.withOpacity(0.8),
                  backgroundColor: Colors.white.withOpacity(0.2),
                  strokeWidth: 2.0, // 调整线宽
                ),
              ),
              // 播放/暂停按钮 - 对应 ImageView (e0o)
              GestureDetector(
                onTap: () async {
                  final syncService = PlayerStateSyncService();
                  if (isPlaying) {
                    await syncService.pause();
                  } else {
                    await syncService.resume();
                  }
                },
                child: Container(
                  width: 28, // 缩小按钮
                  height: 28,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.9),
                  ),
                  child: Icon(
                    isPlaying ? Icons.pause : Icons.play_arrow,
                    color: _globalColorService.currentColors.primary,
                    size: 18, // 缩小图标
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建播放列表按钮 - 对应 ImageView (e0h)
  Widget _buildPlaylistButton() {
    return GestureDetector(
      onTap: () {
        showPlaylistBottomSheet(context);
      },
      child: Container(
        width: 40, // 缩小按钮
        height: 40, // 缩小按钮
        alignment: Alignment.center,
        child: Icon(
          Icons.queue_music,
          color: Colors.white.withOpacity(0.8),
          size: 28, // 缩小图标
        ),
      ),
    );
  }
}

/// 圆形进度条绘制器 - 对应原设计的 ArcImageView
/// 完全复刻E4A中的圆形进度条效果
class _CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color progressColor;
  final Color backgroundColor;
  final double strokeWidth;

  _CircularProgressPainter({
    required this.progress,
    required this.progressColor,
    required this.backgroundColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 绘制背景圆环
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // 绘制进度圆弧
    if (progress > 0) {
      final progressPaint = Paint()
        ..color = progressColor
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      const startAngle = -math.pi / 2; // 从顶部开始
      final sweepAngle = 2 * math.pi * progress;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(_CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.progressColor != progressColor ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
