import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../data/models/user_model.dart';

class ProfileStats extends StatelessWidget {
  final User user;
  final VoidCallback? onFollowingTap;
  final VoidCallback? onFollowersTap;
  
  const ProfileStats({
    super.key,
    required this.user,
    this.onFollowingTap,
    this.onFollowersTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      transform: Matrix4.translationValues(0, -20, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatItem(
            icon: Icons.music_note,
            value: user.favoriteSongIds.length.toString(),
            label: 'Favorites',
          ),
          _buildVerticalDivider(),
          GestureDetector(
            onTap: onFollowingTap,
            child: _buildStatItem(
              icon: Icons.people_outline,
              value: user.followingIds.length.toString(),
              label: 'Following',
            ),
          ),
          _buildVerticalDivider(),
          GestureDetector(
            onTap: onFollowersTap,
            child: _buildStatItem(
              icon: Icons.person_outline,
              value: user.followerIds.length.toString(),
              label: 'Followers',
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColors.primaryColor,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
  
  Widget _buildVerticalDivider() {
    return Container(
      height: 40,
      width: 1,
      color: AppColors.grey1.withOpacity(0.3),
    );
  }
} 