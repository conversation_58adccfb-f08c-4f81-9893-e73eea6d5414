import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'dart:async';
import '../models/song_model.dart';
import 'notification_service.dart';
import 'permission_service.dart';
import 'playlist_manager.dart';
import 'enhanced_playlist_manager.dart';
import '../../services/music_service_manager.dart';
import 'unified_cache_manager.dart';
import 'kuwo_music_service.dart';

class AudioPlayerService {
  // 单例模式
  static final AudioPlayerService _instance = AudioPlayerService._internal();
  
  factory AudioPlayerService() {
    return _instance;
  }
  
  final AudioPlayer _audioPlayer = AudioPlayer();
  Song? _currentSong;
  final NotificationService _notificationService = NotificationService();
  final PermissionService _permissionService = PermissionService();
  final MusicServiceManager _serviceManager = MusicServiceManager();
  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<ProcessingState>? _processingStateSubscription;
  bool _isInitialized = false;
  bool _isHandlingCompletion = false; // 防止重复处理播放完成事件
  bool _isHandlingNotificationClick = false; // 防止通知栏点击时的重复状态处理

  // 播放列表管理器引用（通过Provider设置）
  EnhancedPlaylistManager? _enhancedPlaylistManager;

  // 当前歌曲变化的Stream
  final StreamController<Song?> _currentSongController = StreamController<Song?>.broadcast();

  AudioPlayerService._internal() {
    _initializeService();
  }
  
  AudioPlayer get audioPlayer => _audioPlayer;
  Song? get currentSong => _currentSong;
  Stream<Song?> get currentSongStream => _currentSongController.stream;

  /// 设置增强播放列表管理器
  void setEnhancedPlaylistManager(EnhancedPlaylistManager manager) {
    _enhancedPlaylistManager = manager;
    if (kDebugMode) {
      print('AudioPlayerService: EnhancedPlaylistManager set successfully');
    }
  }

  /// 初始化服务
  Future<void> _initializeService() async {
    if (_isInitialized) return;

    try {
      // 首先初始化通知服务
      await _notificationService.initialize();

      // 设置通知服务的事件监听器
      _notificationService.setEventListeners(
        onPlayPause: _handleNotificationPlayPause,
        onNext: _handleNotificationNext,
        onPrevious: _handleNotificationPrevious,
        onLike: _handleNotificationLike,
        onClose: _handleNotificationClose,
      );

      // 设置音频播放器监听器
      _setupAudioPlayerListeners();

      _isInitialized = true;

      print('AudioPlayerService: Service initialized successfully');
    } catch (e) {
      print('AudioPlayerService: Initialization error: $e');
    }
  }
  
  /// 处理播放状态变化 - 公共方法，供PlayerStateSyncService调用
  Future<void> handlePlaybackStateChange(bool isPlaying) async {
    try {
      if (isPlaying) {
        // 开始播放时的优化
        await _serviceManager.onMusicPlayStarted();
      } else {
        // 暂停播放时的优化
        await _serviceManager.onMusicPlayPaused();
      }

      // 通知前台服务更新通知状态
      await _updateForegroundServiceNotification(isPlaying);
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Error handling playback state change: $e');
      }
    }
  }
  
  /// 更新前台服务通知
  Future<void> _updateForegroundServiceNotification(bool isPlaying) async {
    try {
      await _serviceManager.updateForegroundNotification(isPlaying);
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Failed to update foreground service notification: $e');
      }
    }
  }
  
  /// 播放歌曲
  Future<void> playSong(Song song) async {
    if (song.audioUrl == null || song.audioUrl!.isEmpty) {
      if (kDebugMode) {
        print('AudioPlayerService: Cannot play song: audio URL is null or empty');
      }
      return;
    }

    try {
      _currentSong = song;
      // 发出当前歌曲变化事件
      _currentSongController.add(song);

    // 异步预加载所有资源（不阻塞播放）
    _preloadAllResources(song);

      // 立即显示通知栏（使用当前歌曲信息）
      await _showNotification();

      // 处理URL中的特殊字符，对于特殊字符$，我们需要将其替换为%24
      String processedUrl = song.audioUrl!.replaceAll('\$', '%24');

      // 开始播放

      // 设置音频源并播放
      await _audioPlayer.setUrl(processedUrl);
      await _audioPlayer.play();

      // 再次更新通知栏（确保播放状态正确）
      await _showNotification();
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Error playing song: $e');
      }
    }
  }
  
  /// 暂停播放
  Future<void> pause() async {
    await _audioPlayer.pause();
    // 暂停时的服务优化已在 _handlePlaybackStateChange 中处理
  }
  
  /// 继续播放
  Future<void> resume() async {
    await _audioPlayer.play();
    // 播放时的服务优化已在 _handlePlaybackStateChange 中处理
  }
  
  /// 停止播放
  Future<void> stop() async {
    await _audioPlayer.stop();
    
    // 停止播放时的清理
    await _serviceManager.onMusicPlayStopped();
    
    if (kDebugMode) {
      print('AudioPlayerService: Playback stopped and resources cleaned up');
    }
  }
  
  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    await _audioPlayer.seek(position);
  }
  
  /// 设置音量
  Future<void> setVolume(double volume) async {
    await _audioPlayer.setVolume(volume);
  }
  
  /// 异步预加载所有资源（并发但不阻塞）
  void _preloadAllResources(Song song) {
    // 并发启动所有预加载，但不等待完成
    // 各个UI组件会独立检查缓存状态

    // 1. 图片预加载（优先级最高，UI立即需要）
    _preloadAlbumCover(song).catchError((e) {
      if (kDebugMode) {
        print('AudioPlayerService: Album cover preload failed: $e');
      }
    });

    // 2. 歌词预加载（优先级中等）
    _preloadLyrics(song).catchError((e) {
      if (kDebugMode) {
        print('AudioPlayerService: Lyrics preload failed: $e');
      }
    });

    // 3. 音频文件预加载（如果需要的话）
    // 注意：音频文件通常在播放时才下载，因为文件较大
  }

  /// 预加载专辑封面（检查缓存，不存在则下载）
  Future<void> _preloadAlbumCover(Song song) async {
    try {
      // 预加载专辑封面

      // 1. 先检查是否已有缓存（任意尺寸）
      final cacheManager = UnifiedCacheManager();
      final existingImage = await cacheManager.getAlbumCover(
        song.id,
        '', // 空URL，只检查缓存
        requestedSize: 500,
        checkCacheOnly: true, // 只检查缓存，不下载
      );

      if (existingImage != null) {
        // 专辑封面已缓存
        return; // 已有缓存，无需下载
      }

      // 2. 没有缓存，获取高质量URL并下载
      if (kDebugMode) {
        print('AudioPlayerService: Preloading album cover for ${song.id}');
      }

      final kuwoService = KuwoMusicService();
      final highQualityUrl = await kuwoService.getHighQualityAlbumCover(song.id);

      if (highQualityUrl != null && highQualityUrl.isNotEmpty) {
        // 下载并缓存图片
        await cacheManager.getAlbumCover(
          song.id,
          highQualityUrl,
          requestedSize: 500,
        );

        // 专辑封面预加载成功
      }
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Error preloading album cover: $e');
      }
    }
  }

  /// 预加载歌词（检查缓存，不存在则下载）
  Future<void> _preloadLyrics(Song song) async {
    try {
      final cacheManager = UnifiedCacheManager();

      // 1. 检查歌词缓存并下载（如果需要）
      // 预加载歌词

      final kuwoService = KuwoMusicService();
      final lyrics = await kuwoService.getLyrics(song.id);

      if (lyrics != null) {
        // 歌词会在KuwoMusicService.getLyrics中自动缓存
        // 歌词预加载成功
      } else {
        if (kDebugMode) {
          print('AudioPlayerService: No lyrics found for ${song.id}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Error preloading lyrics: $e');
      }
    }
  }

  /// 显示通知栏
  Future<void> _showNotification() async {
    if (_currentSong == null) return;

    try {
      // 确保有通知权限
      final hasPermission = await _ensureNotificationPermission();
      if (!hasPermission) {
        if (kDebugMode) {
          print('AudioPlayerService: Notification permission denied, skipping notification');
        }
        return;
      }

      // 直接让NotificationService使用缓存系统获取最佳图片
      final albumArt = await _notificationService.loadAlbumArtFromSong(_currentSong!);

      await _notificationService.showNotification(
        song: _currentSong!,
        isPlaying: _audioPlayer.playing,
        position: _audioPlayer.position.inMilliseconds,
        duration: _audioPlayer.duration?.inMilliseconds ?? 0,
        albumArt: albumArt,
      );
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Failed to show notification: $e');
      }
    }
  }

  /// 更新通知栏播放状态
  Future<void> _updateNotificationPlayState(bool isPlaying) async {
    if (_currentSong == null) return;

    try {
      await _notificationService.updatePlaybackState(
        isPlaying: isPlaying,
        position: _audioPlayer.position.inMilliseconds,
      );
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Failed to update notification play state: $e');
      }
    }
  }

  /// 更新通知栏播放进度
  Future<void> _updateNotificationProgress(Duration position) async {
    if (_currentSong == null) return;

    try {
      await _notificationService.updateProgress(
        position: position.inMilliseconds,
        duration: _audioPlayer.duration?.inMilliseconds ?? 0,
      );
    } catch (e) {
      // 忽略进度更新错误，避免日志过多
    }
  }

  /// 处理通知栏播放/暂停按钮点击
  Future<void> _handleNotificationPlayPause() async {
    // 防止重复处理
    if (_isHandlingNotificationClick) {
      if (kDebugMode) {
        print('AudioPlayerService: Notification click already being handled, ignoring');
      }
      return;
    }

    _isHandlingNotificationClick = true;

    try {
      if (kDebugMode) {
        print('AudioPlayerService: Handling notification play/pause, current playing: ${_audioPlayer.playing}');
      }

      if (_audioPlayer.playing) {
        if (kDebugMode) {
          print('AudioPlayerService: Pausing playback from notification');
        }
        await pause();
      } else {
        if (kDebugMode) {
          print('AudioPlayerService: Resuming playback from notification');
        }
        await resume();
      }
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Error handling notification play/pause: $e');
      }
    } finally {
      // 延迟重置标志，防止过快的重复点击
      Future.delayed(const Duration(milliseconds: 300), () {
        _isHandlingNotificationClick = false;
        if (kDebugMode) {
          print('AudioPlayerService: Notification click handling reset');
        }
      });
    }
  }

  /// 处理通知栏下一首按钮点击
  Future<void> _handleNotificationNext() async {
    if (_enhancedPlaylistManager != null) {
      await _enhancedPlaylistManager!.playNext();
    } else {
      // 回退到旧的播放列表管理器
      final playlistManager = PlaylistManager();
      await playlistManager.playNext();
    }
  }

  /// 处理通知栏上一首按钮点击
  Future<void> _handleNotificationPrevious() async {
    if (_enhancedPlaylistManager != null) {
      await _enhancedPlaylistManager!.playPrevious();
    } else {
      // 回退到旧的播放列表管理器
      final playlistManager = PlaylistManager();
      await playlistManager.playPrevious();
    }
  }

  /// 处理通知栏喜欢按钮点击
  Future<void> _handleNotificationLike() async {
    if (_currentSong == null) return;

    // 切换喜欢状态
    final newLikedState = !_currentSong!.isLiked;
    _currentSong = _currentSong!.copyWith(isLiked: newLikedState);
    // 发出当前歌曲变化事件
    _currentSongController.add(_currentSong);

    // 更新通知栏喜欢状态
    await _notificationService.updateLikeState(newLikedState);

    if (kDebugMode) {
      print('AudioPlayerService: Song ${_currentSong!.title} liked state changed to $newLikedState');
    }
  }

  /// 处理通知栏关闭按钮点击
  Future<void> _handleNotificationClose() async {
    await stop();
    await _notificationService.hideNotification();
  }

  /// 确保有通知权限
  Future<bool> _ensureNotificationPermission() async {
    try {
      // 检查是否已有权限
      final hasPermission = await _permissionService.checkNotificationPermission();

      if (hasPermission) {
        return true;
      }

      if (kDebugMode) {
        print('AudioPlayerService: Requesting notification permission...');
      }

      // 请求权限
      final granted = await _permissionService.requestNotificationPermission();

      if (kDebugMode) {
        print('AudioPlayerService: Notification permission granted: $granted');
      }

      return granted;
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Error ensuring notification permission: $e');
      }
      return false;
    }
  }

  /// 设置音频播放器监听器
  void _setupAudioPlayerListeners() {
    // 监听播放状态变化 - 只处理通知栏更新，状态同步交给PlayerStateSyncService
    _playerStateSubscription = _audioPlayer.playerStateStream.listen((state) {
      _updateNotificationPlayState(state.playing);
      // 移除重复的 _handlePlaybackStateChange 调用，避免重复的前台服务更新
    });

    // 监听播放进度变化
    _positionSubscription = _audioPlayer.positionStream.listen((position) {
      _updateNotificationProgress(position);
    });

    // 监听播放完成事件，自动播放下一首
    _processingStateSubscription = _audioPlayer.processingStateStream.listen((state) {
      if (kDebugMode) {
        print('AudioPlayerService: Processing state changed to: $state');
      }

      if (state == ProcessingState.completed && !_isHandlingCompletion) {
        if (kDebugMode) {
          print('AudioPlayerService: Handling playback completion...');
        }
        _isHandlingCompletion = true;
        _handlePlaybackCompletion();
      } else if (state == ProcessingState.completed && _isHandlingCompletion) {
        if (kDebugMode) {
          print('AudioPlayerService: Playback completion already being handled, skipping...');
        }
      }
    });
  }

  /// 处理播放完成事件
  Future<void> _handlePlaybackCompletion() async {
    try {
      if (kDebugMode) {
        print('AudioPlayerService: Song playback completed, getting next song...');
      }

      // 使用增强播放列表管理器播放下一首
      if (_enhancedPlaylistManager != null) {
        if (kDebugMode) {
          print('AudioPlayerService: EnhancedPlaylistManager is available');
          print('AudioPlayerService: Current playlist size: ${_enhancedPlaylistManager!.currentPlaylist.length}');
          print('AudioPlayerService: Play next queue size: ${_enhancedPlaylistManager!.playNextQueue.length}');
          print('AudioPlayerService: Current index: ${_enhancedPlaylistManager!.currentIndex}');
          print('AudioPlayerService: Play mode: ${_enhancedPlaylistManager!.playMode}');
        }

        // 直接调用播放下一首，它会处理所有逻辑
        await _enhancedPlaylistManager!.playNext();
      } else {
        if (kDebugMode) {
          print('AudioPlayerService: EnhancedPlaylistManager is NULL! Using fallback...');
        }
        // 回退到旧的播放列表管理器
        final playlistManager = PlaylistManager();
        await playlistManager.playNext();
      }

      if (kDebugMode) {
        print('AudioPlayerService: Successfully handled playback completion');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AudioPlayerService: Error handling playback completion: $e');
      }
    } finally {
      // 重置标志，允许下次触发
      _isHandlingCompletion = false;
    }
  }

  /// 处理资源释放
  Future<void> dispose() async {
    _playerStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _processingStateSubscription?.cancel();
    await _currentSongController.close();
    await _notificationService.dispose();
    await _audioPlayer.dispose();
  }
} 