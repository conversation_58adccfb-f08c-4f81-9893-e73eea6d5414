package com.Himusic.himusic;

import android.app.Notification;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.widget.RemoteViews;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import io.flutter.plugin.common.MethodChannel;

import java.util.Map;
import java.util.HashMap;

public class MusicNotificationManager {
    private static final String CHANNEL_ID = "music_channel";
    private static final int NOTIFICATION_ID = 1000; // 统一使用1000作为前台服务和音乐播放通知ID
    
    // 广播动作
    private static final String ACTION_PLAY_PAUSE = "com.himusic.PLAY_PAUSE";
    private static final String ACTION_NEXT = "com.himusic.NEXT";
    private static final String ACTION_PREVIOUS = "com.himusic.PREVIOUS";
    private static final String ACTION_LIKE = "com.himusic.LIKE";
    private static final String ACTION_CLOSE = "com.himusic.CLOSE";

    
    private Context context;
    private NotificationManagerCompat notificationManager;
    private MusicNotificationReceiver receiver;
    private MethodChannel methodChannel;
    
    // 当前播放状态
    private String currentTitle = "";
    private String currentArtist = "";
    private String currentAlbum = "";
    private boolean isPlaying = false;
    private boolean isLiked = false;
    private Bitmap albumArt;
    private Handler refreshHandler;
    private Runnable refreshRunnable;

    // 进度相关
    private long currentPosition = 0; // 当前播放位置（毫秒）
    private long duration = 0; // 总时长（毫秒）

    public MusicNotificationManager(Context context) {
        this.context = context;
        this.notificationManager = NotificationManagerCompat.from(context);
        this.receiver = new MusicNotificationReceiver();
        this.refreshHandler = new Handler(Looper.getMainLooper());
    }

    public void setMethodChannel(MethodChannel channel) {
        this.methodChannel = channel;
    }

    public void initialize() {
        createNotificationChannel();
        registerReceiver();
        loadDefaultAlbumArt();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 创建统一的音乐播放和前台服务通知渠道
            String musicChannelId = CHANNEL_ID;
            CharSequence musicChannelName = "音乐播放控制";
            String musicChannelDescription = "音乐播放控制通知，包含播放控制按钮和后台服务运行状态";
            int musicImportance = NotificationManager.IMPORTANCE_HIGH; // 高重要性确保显示
            
            NotificationChannel musicChannel = new NotificationChannel(musicChannelId, musicChannelName, musicImportance);
            musicChannel.setDescription(musicChannelDescription);
            musicChannel.setShowBadge(false); // 不显示应用角标
            musicChannel.setSound(null, null); // 静音通知
            musicChannel.enableLights(true); // 启用指示灯
            musicChannel.setLightColor(0xFF1976D2); // 蓝色指示灯
            musicChannel.enableVibration(false); // 禁用振动
            musicChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC); // 锁屏可见

            NotificationManager manager = context.getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(musicChannel);
            }
        }
    }

    private void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_PLAY_PAUSE);
        filter.addAction(ACTION_NEXT);
        filter.addAction(ACTION_PREVIOUS);
        filter.addAction(ACTION_LIKE);
        filter.addAction(ACTION_CLOSE);

        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(receiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            context.registerReceiver(receiver, filter);
        }
    }

    private void loadDefaultAlbumArt() {
        try {
            albumArt = BitmapFactory.decodeResource(context.getResources(), R.drawable.default_album_art);
        } catch (Exception e) {
            // 如果加载失败，创建一个默认的bitmap
            albumArt = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888);
        }
    }

    public void showNotification(Object arguments) {
        if (!(arguments instanceof Map)) return;
        
        Map<String, Object> args = (Map<String, Object>) arguments;
        
        currentTitle = (String) args.get("title");
        currentArtist = (String) args.get("artist");
        currentAlbum = (String) args.get("album");
        isPlaying = (Boolean) args.get("isPlaying");
        isLiked = (Boolean) args.get("isLiked");

        // 获取进度信息
        Object positionObj = args.get("position");
        Object durationObj = args.get("duration");
        if (positionObj instanceof Number) {
            currentPosition = ((Number) positionObj).longValue();
        }
        if (durationObj instanceof Number) {
            duration = ((Number) durationObj).longValue();
        }
        
        // 处理专辑封面
        byte[] albumArtBytes = (byte[]) args.get("albumArt");
        if (albumArtBytes != null && albumArtBytes.length > 0) {
            try {
                albumArt = BitmapFactory.decodeByteArray(albumArtBytes, 0, albumArtBytes.length);
            } catch (Exception e) {
                // 使用默认封面
            }
        }
        
        buildAndShowNotification();
    }

    public void updatePlaybackState(Object arguments) {
        if (!(arguments instanceof Map)) return;
        
        Map<String, Object> args = (Map<String, Object>) arguments;
        isPlaying = (Boolean) args.get("isPlaying");
        
        buildAndShowNotification();
    }

    public void updateProgress(Object arguments) {
        if (!(arguments instanceof Map)) return;

        Map<String, Object> args = (Map<String, Object>) arguments;
        Object positionObj = args.get("position");
        Object durationObj = args.get("duration");

        if (positionObj instanceof Number) {
            currentPosition = ((Number) positionObj).longValue();
        }
        if (durationObj instanceof Number) {
            duration = ((Number) durationObj).longValue();
        }

        // 只更新进度，不重新构建整个通知
        updateProgressOnly();
    }

    public void updateLikeState(Object arguments) {
        if (!(arguments instanceof Map)) return;
        
        Map<String, Object> args = (Map<String, Object>) arguments;
        isLiked = (Boolean) args.get("isLiked");
        
        buildAndShowNotification();
    }

    private void buildAndShowNotification() {
        // 创建自定义布局 - 使用统一的布局文件
        RemoteViews notificationLayout = new RemoteViews(context.getPackageName(), R.layout.notification_music);

        // 设置歌曲信息
        notificationLayout.setTextViewText(R.id.tv_title, currentTitle != null ? currentTitle : "未知歌曲");
        notificationLayout.setTextViewText(R.id.tv_artist, currentArtist != null ? currentArtist : "未知艺术家");

        // 设置专辑封面（添加圆角效果）
        if (albumArt != null) {
            Bitmap roundedAlbumArt = getRoundedCornerBitmap(albumArt, 24); // 12dp * 2 for density
            notificationLayout.setImageViewBitmap(R.id.iv_album_art, roundedAlbumArt);
        }

        // 设置播放/暂停按钮
        notificationLayout.setImageViewResource(R.id.btn_play_pause,
            isPlaying ? R.drawable.ic_pause : R.drawable.ic_play);

        // 设置喜欢按钮
        notificationLayout.setImageViewResource(R.id.btn_like,
            isLiked ? R.drawable.ic_heart_filled : R.drawable.ic_heart_outline);

        // 设置进度条和时间（仅在支持的版本上）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            updateProgressViews(notificationLayout);
        }

        // 设置按钮点击事件
        setButtonClickListeners(notificationLayout);

        // 创建通知 - 同时满足音乐播放和前台服务的需求
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_music_note)
                .setContentTitle(currentTitle != null ? currentTitle : "HiMusic")
                .setContentText(currentArtist != null ? currentArtist : "Music service is running")
                .setCustomContentView(notificationLayout)
                .setCustomBigContentView(notificationLayout) // 展开视图也使用自定义布局
                .setCustomHeadsUpContentView(notificationLayout) // 悬浮通知也使用自定义布局
                .setPriority(NotificationCompat.PRIORITY_HIGH) // 高优先级，确保显示
                .setOngoing(true) // 设置为持续通知，支持前台服务
                .setShowWhen(false)
                .setSound(null)
                .setAutoCancel(false) // 不允许滑动删除
                .setCategory(NotificationCompat.CATEGORY_TRANSPORT) // 媒体传输类别
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setOnlyAlertOnce(true) // 只提醒一次
                .setSilent(true) // 静默通知
                .setDefaults(0) // 不使用默认设置
                .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE); // 前台服务行为

        // 设置点击通知打开应用
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        builder.setContentIntent(pendingIntent);

        // 构建通知
        Notification notification = builder.build();
        
        // 设置前台服务标志
        notification.flags |= Notification.FLAG_NO_CLEAR;
        notification.flags |= Notification.FLAG_ONGOING_EVENT;
        notification.flags |= Notification.FLAG_FOREGROUND_SERVICE;

        // 发送通知
        notificationManager.notify(NOTIFICATION_ID, notification);

        // 启动定期刷新机制，防止通知被压缩
        startRefreshTimer();
    }

    private void updateProgressViews(RemoteViews views) {
        // 设置当前时间
        views.setTextViewText(R.id.tv_current_time, formatTime(currentPosition));

        // 设置总时长
        views.setTextViewText(R.id.tv_total_time, formatTime(duration));

        // 设置进度条 - 检查Android版本兼容性
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            if (duration > 0) {
                int progress = (int) ((currentPosition * 100) / duration);
                views.setProgressBar(R.id.progress_bar, 100, progress, false);
            } else {
                views.setProgressBar(R.id.progress_bar, 100, 0, false);
            }
        }
    }

    private void updateProgressOnly() {
        // 简化：直接重新构建完整通知，确保稳定性
        buildAndShowNotification();
    }

    private String formatTime(long timeMs) {
        if (timeMs <= 0) return "00:00";

        long seconds = timeMs / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;

        return String.format("%02d:%02d", minutes, seconds);
    }

    private void startRefreshTimer() {
        // 停止之前的定时器
        stopRefreshTimer();

        // 创建新的定时器，每5秒刷新一次通知
        refreshRunnable = new Runnable() {
            @Override
            public void run() {
                // 重新发送通知以保持展开状态
                if (currentTitle != null && !currentTitle.isEmpty()) {
                    buildAndShowNotification();
                }
                // 继续下一次刷新
                refreshHandler.postDelayed(this, 2000); // 5秒后再次执行
            }
        };

        // 5秒后开始第一次刷新
        refreshHandler.postDelayed(refreshRunnable, 2000);
    }

    private void stopRefreshTimer() {
        if (refreshHandler != null && refreshRunnable != null) {
            refreshHandler.removeCallbacks(refreshRunnable);
        }
    }

    private void setButtonClickListeners(RemoteViews views) {
        // 上一首按钮
        Intent previousIntent = new Intent(ACTION_PREVIOUS);
        PendingIntent previousPendingIntent = PendingIntent.getBroadcast(context, 0, previousIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        views.setOnClickPendingIntent(R.id.btn_previous, previousPendingIntent);
        
        // 播放/暂停按钮
        Intent playPauseIntent = new Intent(ACTION_PLAY_PAUSE);
        PendingIntent playPausePendingIntent = PendingIntent.getBroadcast(context, 1, playPauseIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        views.setOnClickPendingIntent(R.id.btn_play_pause, playPausePendingIntent);
        
        // 下一首按钮
        Intent nextIntent = new Intent(ACTION_NEXT);
        PendingIntent nextPendingIntent = PendingIntent.getBroadcast(context, 2, nextIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        views.setOnClickPendingIntent(R.id.btn_next, nextPendingIntent);
        
        // 喜欢按钮
        Intent likeIntent = new Intent(ACTION_LIKE);
        PendingIntent likePendingIntent = PendingIntent.getBroadcast(context, 3, likeIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        views.setOnClickPendingIntent(R.id.btn_like, likePendingIntent);
        
        // 关闭按钮
        Intent closeIntent = new Intent(ACTION_CLOSE);
        PendingIntent closePendingIntent = PendingIntent.getBroadcast(context, 4, closeIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        views.setOnClickPendingIntent(R.id.btn_close, closePendingIntent);

        // 进度条点击事件 - 打开应用
        Intent openAppIntent = new Intent(context, MainActivity.class);
        openAppIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        PendingIntent openAppPendingIntent = PendingIntent.getActivity(context, 6, openAppIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        views.setOnClickPendingIntent(R.id.progress_bar, openAppPendingIntent);
    }

    public void hideNotification() {
        notificationManager.cancel(NOTIFICATION_ID);
    }

    /**
     * 创建默认通知用于前台服务
     * 这个通知同时承担音乐播放控制和前台服务的职责
     */
    public Notification createDefaultNotification() {
        // 如果有当前播放的歌曲，创建完整的音乐播放通知
        if (!currentTitle.isEmpty()) {
            return createMusicPlaybackNotification();
        }
        
        // 如果没有播放歌曲，创建基础的服务通知
        return createBasicServiceNotification();
    }
    
    /**
     * 创建完整的音乐播放通知（用于前台服务）
     */
    private Notification createMusicPlaybackNotification() {
        // 创建自定义布局 - 使用统一的布局文件
        RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.notification_music);
        
        // 设置歌曲信息
        views.setTextViewText(R.id.tv_title, currentTitle != null ? currentTitle : "未知歌曲");
        views.setTextViewText(R.id.tv_artist, currentArtist != null ? currentArtist : "未知艺术家");
        
        // 设置专辑封面
        if (albumArt != null) {
            Bitmap roundedAlbumArt = getRoundedCornerBitmap(albumArt, 24f);
            views.setImageViewBitmap(R.id.iv_album_art, roundedAlbumArt);
        }
        
        // 设置播放按钮状态
        views.setImageViewResource(R.id.btn_play_pause, 
            isPlaying ? R.drawable.ic_pause : R.drawable.ic_play);
        
        // 设置喜欢按钮状态
        views.setImageViewResource(R.id.btn_like, 
            isLiked ? R.drawable.ic_heart_filled : R.drawable.ic_heart_outline);
        
        // 设置进度条和时间
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            updateProgressViews(views);
        }
        
        // 设置按钮点击事件
        setButtonClickListeners(views);
        
        // 创建点击通知打开应用的Intent
        Intent openAppIntent = new Intent(context, MainActivity.class);
        openAppIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        PendingIntent openAppPendingIntent = PendingIntent.getActivity(context, 0, openAppIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_music_note)
                .setContentTitle(currentTitle != null ? currentTitle : "HiMusic")
                .setContentText(currentArtist != null ? currentArtist : "Music service is running")
                .setContentIntent(openAppPendingIntent)
                .setCustomContentView(views)
                .setCustomBigContentView(views)
                .setCustomHeadsUpContentView(views)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setOngoing(true)
                .setShowWhen(false)
                .setSound(null)
                .setSilent(true)
                .setAutoCancel(false)
                .setOnlyAlertOnce(true)
                .setCategory(NotificationCompat.CATEGORY_TRANSPORT)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setDefaults(0)
                .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE);
        
        Notification notification = builder.build();
        
        // 设置前台服务标志
        notification.flags |= Notification.FLAG_NO_CLEAR;
        notification.flags |= Notification.FLAG_ONGOING_EVENT;
        notification.flags |= Notification.FLAG_FOREGROUND_SERVICE;
        
        return notification;
    }
    
    /**
     * 创建基础的服务通知（当没有播放歌曲时）
     */
    private Notification createBasicServiceNotification() {
        // 创建点击通知打开应用的Intent
        Intent openAppIntent = new Intent(context, MainActivity.class);
        openAppIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        PendingIntent openAppPendingIntent = PendingIntent.getActivity(context, 0, openAppIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_music_note)
                .setContentTitle("HiMusic")
                .setContentText("Music service is ready")
                .setContentIntent(openAppPendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MIN)
                .setOngoing(true)
                .setShowWhen(false)
                .setSound(null)
                .setSilent(true)
                .setAutoCancel(false)
                .setOnlyAlertOnce(true)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setVisibility(NotificationCompat.VISIBILITY_SECRET); // 在锁屏时隐藏
        
        Notification notification = builder.build();
        
        // 设置前台服务标志
        notification.flags |= Notification.FLAG_NO_CLEAR;
        notification.flags |= Notification.FLAG_ONGOING_EVENT;
        notification.flags |= Notification.FLAG_FOREGROUND_SERVICE;
        
        return notification;
    }

    public void cleanup() {
        try {
            context.unregisterReceiver(receiver);
        } catch (Exception e) {
            // 忽略异常
        }
        stopRefreshTimer();
        hideNotification();
    }

    // 广播接收器
    private class MusicNotificationReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action == null) return;
            
            switch (action) {
                case ACTION_PLAY_PAUSE:
                    if (methodChannel != null) {
                        methodChannel.invokeMethod("onPlayPause", null);
                    }
                    break;
                case ACTION_NEXT:
                    if (methodChannel != null) {
                        methodChannel.invokeMethod("onNext", null);
                    }
                    break;
                case ACTION_PREVIOUS:
                    if (methodChannel != null) {
                        methodChannel.invokeMethod("onPrevious", null);
                    }
                    break;
                case ACTION_LIKE:
                    if (methodChannel != null) {
                        methodChannel.invokeMethod("onLike", null);
                    }
                    break;
                case ACTION_CLOSE:
                    if (methodChannel != null) {
                        methodChannel.invokeMethod("onClose", null);
                    }
                    break;

            }
        }
    }

    /**
     * 创建圆角Bitmap
     */
    private Bitmap getRoundedCornerBitmap(Bitmap bitmap, float cornerRadius) {
        if (bitmap == null) return null;

        try {
            Bitmap output = Bitmap.createBitmap(bitmap.getWidth(), bitmap.getHeight(), Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(output);

            final int color = 0xff424242;
            final Paint paint = new Paint();
            final Rect rect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
            final RectF rectF = new RectF(rect);

            paint.setAntiAlias(true);
            canvas.drawARGB(0, 0, 0, 0);
            paint.setColor(color);
            canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint);

            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
            canvas.drawBitmap(bitmap, rect, rect, paint);

            return output;
        } catch (Exception e) {
            return bitmap; // 如果处理失败，返回原图
        }
    }
}
