import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../data/models/lyric_model.dart';
import '../../core/theme/app_colors.dart';
import '../marquee_text.dart';

class LyricsView extends StatefulWidget {
  final Lyric? lyric;
  final Duration position;
  
  const LyricsView({
    super.key,
    this.lyric,
    required this.position,
  });

  @override
  State<LyricsView> createState() => _LyricsViewState();
}

class _LyricsViewState extends State<LyricsView> with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  int _currentLineIndex = 0;
  bool _userScrolling = false;
  Timer? _scrollResumeTimer;
  double _viewportHeight = 0;
  late AnimationController _animationController;
  
  // 固定行高
  static const double kLineHeight = 50.0;
  
  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      print('LyricsView: initState called');
    }
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    
    // 添加滚动监听
    _scrollController.addListener(_onScrollChanged);
    
    // 等视图完全构建后再尝试更新和滚动
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateCurrentLine(true);
      }
    });
  }
  
  @override
  void didUpdateWidget(LyricsView oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 当位置变化时，更新当前行
    if (widget.position != oldWidget.position) {
      _updateCurrentLine(false);
    }
    
    // 当歌词变化时，重置状态
    if (widget.lyric != oldWidget.lyric) {
      setState(() {
        _currentLineIndex = 0;
      });
      
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _updateCurrentLine(true);
        }
      });
    }
  }
  
  void _onScrollChanged() {
    // 检测用户是否在滚动
    if (_scrollController.hasClients && _scrollController.position.isScrollingNotifier.value) {
      setState(() {
        _userScrolling = true;
      });
      
      // 取消任何已存在的定时器
      _scrollResumeTimer?.cancel();
      
      // 创建新的定时器，3秒后恢复自动滚动
      _scrollResumeTimer = Timer(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _userScrolling = false;
          });
          _updateCurrentLine(true);
        }
      });
    }
  }
  
  void _updateCurrentLine(bool forceScroll) {
    if (!mounted || widget.lyric == null || widget.lyric!.lines.isEmpty) return;
    
    // 获取当前时间对应的歌词行索引
    final newIndex = widget.lyric!.getCurrentLineIndex(widget.position);
    
    if (newIndex >= 0 && (newIndex != _currentLineIndex || forceScroll)) {
      if (kDebugMode) {
        print('LyricsView: Updating to line $newIndex (from $_currentLineIndex)');
      }
      
      setState(() {
        _currentLineIndex = newIndex;
      });
      
      // 如果不是用户滚动状态，滚动到当前行
      if (!_userScrolling || forceScroll) {
        _scrollToCurrentLine();
      }
    }
  }
  
  void _scrollToCurrentLine() {
    if (!mounted || !_scrollController.hasClients) return;
    
    // 计算使当前行滚动到中心位置所需的偏移量
    final targetOffset = _calculateCenterOffset(_currentLineIndex);
    
    if (kDebugMode) {
      print('LyricsView: Scrolling to line $_currentLineIndex, offset $targetOffset, viewportHeight: $_viewportHeight');
    }
    
    // 平滑滚动到目标位置
    _animationController.reset();
    
    // 创建动画，从当前位置到目标位置
    final Animation<double> animation = Tween<double>(
      begin: _scrollController.offset,
      end: targetOffset,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    // 监听动画并更新滚动位置
    animation.addListener(() {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(animation.value);
      }
    });
    
    // 开始动画
    _animationController.forward();
  }
  
  // 计算使指定行居中的偏移量
  double _calculateCenterOffset(int lineIndex) {
    // 歌词行的起始位置 = 索引 * 行高
    final linePosition = lineIndex * kLineHeight;
    
    // 目标偏移量 = 歌词行位置 - 视口高度的一半 + 行高的一半
    final targetOffset = linePosition - _viewportHeight / 2 + kLineHeight / 2;
    
    // 限制偏移量在可滚动范围内
    final maxOffset = _scrollController.position.maxScrollExtent;
    return targetOffset.clamp(0.0, maxOffset);
  }
  
  @override
  void dispose() {
    _scrollController.removeListener(_onScrollChanged);
    _scrollController.dispose();
    _scrollResumeTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    if (widget.lyric == null || widget.lyric!.lines.isEmpty) {
      return const Center(
        child: Text(
          'No lyrics available',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 16,
          ),
        ),
      );
    }
    
    return LayoutBuilder(
      builder: (context, constraints) {
        _viewportHeight = constraints.maxHeight;
        
        // 计算顶部和底部的填充，确保第一行和最后一行也能滚动到中心
        final verticalPadding = _viewportHeight / 2 - kLineHeight / 2;
        
        return ListView.builder(
          controller: _scrollController,
          itemCount: widget.lyric!.lines.length,
          itemExtent: kLineHeight,
          padding: EdgeInsets.only(
            top: verticalPadding,
            bottom: verticalPadding,
          ),
          physics: const BouncingScrollPhysics(),
          itemBuilder: (context, index) {
            final line = widget.lyric!.lines[index];
            final bool isCurrentLine = index == _currentLineIndex;
            
            // 计算不透明度 - 当前行全不透明，其他行根据距离渐变
            final distance = (index - _currentLineIndex).abs();
            final opacity = 1.0 - (distance * 0.15).clamp(0.0, 0.7);
            
            return Container(
              height: kLineHeight,
              alignment: Alignment.center,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                child: MarqueeText(
                  text: line.text,
                  style: TextStyle(
                    fontSize: isCurrentLine ? 18 : 16,
                    color: isCurrentLine
                        ? AppColors.primaryColor
                        : Colors.grey.withOpacity(opacity),
                    fontWeight: isCurrentLine ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  duration: const Duration(seconds: 8),
                  pauseDuration: const Duration(seconds: 2),
                ),
              ),
            );
          },
        );
      },
    );
  }
}