import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/song_model.dart';
import 'player_state_sync_service.dart';

/// 播放模式枚举
enum PlayMode {
  listRepeat,    // 列表循环（默认）
  singleRepeat,  // 单曲循环
  shuffle,       // 随机播放
}

/// 增强的播放列表管理器
/// 支持播放模式、下一首播放队列等功能
class EnhancedPlaylistManager extends ChangeNotifier {
  // 原始播放列表
  List<Song> _originalPlaylist = [];
  
  // 当前播放列表（可能被随机打乱）
  List<Song> _currentPlaylist = [];
  
  // 下一首播放队列（优先播放）
  List<Song> _playNextQueue = [];
  
  // 当前播放索引
  int _currentIndex = 0;
  
  // 播放模式
  PlayMode _playMode = PlayMode.listRepeat;
  
  // 随机播放的历史记录
  List<int> _shuffleHistory = [];

  // 当前正在播放的歌曲（可能来自队列或列表）
  Song? _currentPlayingSong;

  // Getters
  List<Song> get originalPlaylist => List.unmodifiable(_originalPlaylist);
  List<Song> get currentPlaylist => List.unmodifiable(_currentPlaylist);
  List<Song> get playNextQueue => List.unmodifiable(_playNextQueue);
  int get currentIndex => _currentIndex;
  PlayMode get playMode => _playMode;
  Song? get currentSong => _currentPlayingSong ?? (_currentPlaylist.isNotEmpty && _currentIndex >= 0 && _currentIndex < _currentPlaylist.length
      ? _currentPlaylist[_currentIndex] : null);
  
  /// 设置播放模式
  void setPlayMode(PlayMode mode) {
    if (_playMode == mode) return;
    
    final currentSong = this.currentSong;
    _playMode = mode;
    
    if (kDebugMode) {
      print('EnhancedPlaylistManager: Play mode changed to $mode');
    }
    
    // 重新构建播放列表
    _rebuildCurrentPlaylist();
    
    // 保持当前播放歌曲的位置
    if (currentSong != null) {
      _currentIndex = _currentPlaylist.indexWhere((song) => song.id == currentSong.id);
      if (_currentIndex == -1) _currentIndex = 0;
    }
    
    notifyListeners();
  }
  
  /// 切换到下一个播放模式
  void togglePlayMode() {
    final modes = PlayMode.values;
    final currentModeIndex = modes.indexOf(_playMode);
    final nextModeIndex = (currentModeIndex + 1) % modes.length;
    setPlayMode(modes[nextModeIndex]);
  }
  
  /// 获取播放模式的显示名称
  String getPlayModeDisplayName() {
    switch (_playMode) {
      case PlayMode.listRepeat:
        return '列表循环';
      case PlayMode.singleRepeat:
        return '单曲循环';
      case PlayMode.shuffle:
        return '随机播放';
    }
  }
  
  /// 获取播放模式图标
  String getPlayModeIcon() {
    switch (_playMode) {
      case PlayMode.listRepeat:
        return 'repeat';
      case PlayMode.singleRepeat:
        return 'repeat_one';
      case PlayMode.shuffle:
        return 'shuffle';
    }
  }
  
  /// 设置播放列表并开始播放（歌单模式）
  void setPlaylistAndPlay(List<Song> songs, int startIndex) {
    if (songs.isEmpty) return;

    _originalPlaylist = List.from(songs);
    _playNextQueue.clear();
    _shuffleHistory.clear();

    _rebuildCurrentPlaylist();

    _currentIndex = startIndex.clamp(0, _currentPlaylist.length - 1);

    if (kDebugMode) {
      print('EnhancedPlaylistManager: Set playlist with ${songs.length} songs, playing index $startIndex');
    }

    // 立即播放当前歌曲
    _playCurrent();

    notifyListeners();
  }

  /// 播放当前歌曲
  Future<void> _playCurrent() async {
    if (_currentIndex < 0 || _currentIndex >= _currentPlaylist.length) {
      if (kDebugMode) {
        print('EnhancedPlaylistManager: Invalid current index: $_currentIndex');
      }
      return;
    }

    final song = _currentPlaylist[_currentIndex];
    if (kDebugMode) {
      print('EnhancedPlaylistManager: Playing current song: ${song.title}');
    }

    // 更新当前播放的歌曲
    _currentPlayingSong = song;

    // 使用同步服务播放歌曲，它会处理URL获取
    final syncService = PlayerStateSyncService();
    await syncService.playSong(song);
  }
  
  /// 添加歌曲并立即播放（搜索模式）
  void addSongAndPlay(Song song) {
    if (_originalPlaylist.isEmpty) {
      // 如果播放列表为空，直接设置为新列表
      setPlaylistAndPlay([song], 0);
      return;
    }

    // 添加到原始列表
    _originalPlaylist.add(song);

    // 重建当前列表
    _rebuildCurrentPlaylist();

    // 找到新添加歌曲的位置并播放
    final newSongIndex = _currentPlaylist.indexWhere((s) => s.id == song.id);
    if (newSongIndex != -1) {
      _currentIndex = newSongIndex;
    }

    if (kDebugMode) {
      print('EnhancedPlaylistManager: Added song and playing: ${song.title}');
    }

    // 立即播放新添加的歌曲
    _playCurrent();

    notifyListeners();
  }
  
  /// 添加到下一首播放队列
  void addToPlayNext(Song song) {
    _playNextQueue.add(song);
    
    if (kDebugMode) {
      print('EnhancedPlaylistManager: Added to play next: ${song.title}');
    }
    
    notifyListeners();
  }
  
  /// 添加到播放列表末尾
  void addToPlaylist(Song song) {
    _originalPlaylist.add(song);
    _rebuildCurrentPlaylist();
    
    if (kDebugMode) {
      print('EnhancedPlaylistManager: Added to playlist: ${song.title}');
    }
    
    notifyListeners();
  }
  
  /// 获取下一首歌曲
  Song? getNextSong() {
    // 1. 优先播放"下一首播放"队列
    if (_playNextQueue.isNotEmpty) {
      return _playNextQueue.removeAt(0);
    }
    
    // 2. 根据播放模式决定下一首
    switch (_playMode) {
      case PlayMode.singleRepeat:
        return currentSong;
        
      case PlayMode.listRepeat:
        if (_currentIndex + 1 < _currentPlaylist.length) {
          return _currentPlaylist[_currentIndex + 1];
        } else {
          return _currentPlaylist.isNotEmpty ? _currentPlaylist[0] : null;
        }
        
      case PlayMode.shuffle:
        return _getNextShuffleSong();
    }
  }
  
  /// 播放下一首
  Future<void> playNext() async {
    if (_currentPlaylist.isEmpty && _playNextQueue.isEmpty) return;

    // 1. 优先处理"下一首播放"队列
    if (_playNextQueue.isNotEmpty) {
      final nextSong = _playNextQueue.removeAt(0);
      if (kDebugMode) {
        print('EnhancedPlaylistManager: Playing from queue: ${nextSong.title}');
      }

      // 更新当前播放的歌曲
      _currentPlayingSong = nextSong;

      // 使用同步服务播放歌曲
      final syncService = PlayerStateSyncService();
      await syncService.playSong(nextSong);
      notifyListeners();
      return;
    }

    // 2. 根据播放模式更新索引并播放
    switch (_playMode) {
      case PlayMode.singleRepeat:
        // 单曲循环，重新播放当前歌曲
        await _playCurrent();
        break;

      case PlayMode.listRepeat:
        if (_currentIndex + 1 < _currentPlaylist.length) {
          _currentIndex++;
        } else {
          _currentIndex = 0; // 回到第一首
        }
        await _playCurrent();
        break;

      case PlayMode.shuffle:
        // 随机模式，更新到随机选择的索引
        final nextSong = _getNextShuffleSong();
        if (nextSong != null) {
          _currentIndex = _currentPlaylist.indexWhere((song) => song.id == nextSong.id);
          await _playCurrent();
        }
        break;
    }

    notifyListeners();
  }
  
  /// 播放上一首
  Future<void> playPrevious() async {
    if (_currentPlaylist.isEmpty) return;

    switch (_playMode) {
      case PlayMode.singleRepeat:
        // 单曲循环模式下，重新播放当前歌曲
        await _playCurrent();
        break;

      case PlayMode.shuffle:
        // 随机模式下，从历史记录中获取
        if (_shuffleHistory.length > 1) {
          _shuffleHistory.removeLast(); // 移除当前歌曲
          _currentIndex = _shuffleHistory.last;
          await _playCurrent();
        }
        break;

      default:
        // 列表模式
        if (_currentIndex > 0) {
          _currentIndex--;
        } else {
          _currentIndex = _currentPlaylist.length - 1;
        }
        await _playCurrent();
    }

    notifyListeners();
  }
  
  /// 重建当前播放列表
  void _rebuildCurrentPlaylist() {
    if (_playMode == PlayMode.shuffle) {
      _currentPlaylist = List.from(_originalPlaylist);
      _currentPlaylist.shuffle(Random());
    } else {
      _currentPlaylist = List.from(_originalPlaylist);
    }
  }
  
  /// 获取随机播放的下一首歌曲
  Song? _getNextShuffleSong() {
    if (_currentPlaylist.isEmpty) return null;
    
    // 记录当前播放的歌曲到历史
    if (_currentIndex >= 0 && _currentIndex < _currentPlaylist.length) {
      _shuffleHistory.add(_currentIndex);
      
      // 限制历史记录长度
      if (_shuffleHistory.length > _currentPlaylist.length) {
        _shuffleHistory.removeAt(0);
      }
    }
    
    // 随机选择下一首（避免重复）
    final availableIndices = List.generate(_currentPlaylist.length, (index) => index)
        .where((index) => !_shuffleHistory.contains(index))
        .toList();
    
    if (availableIndices.isEmpty) {
      // 所有歌曲都播放过了，重新开始
      _shuffleHistory.clear();
      availableIndices.addAll(List.generate(_currentPlaylist.length, (index) => index));
    }
    
    final randomIndex = availableIndices[Random().nextInt(availableIndices.length)];
    return _currentPlaylist[randomIndex];
  }
  
  /// 移除播放列表中的歌曲
  void removeSong(int index) {
    if (index < 0 || index >= _originalPlaylist.length) return;
    
    final removedSong = _originalPlaylist.removeAt(index);
    _rebuildCurrentPlaylist();
    
    // 调整当前播放索引
    if (_currentIndex >= _currentPlaylist.length) {
      _currentIndex = _currentPlaylist.length - 1;
    }
    
    if (kDebugMode) {
      print('EnhancedPlaylistManager: Removed song: ${removedSong.title}');
    }
    
    notifyListeners();
  }
  
  /// 设置当前播放索引
  void setCurrentIndex(int index) {
    if (index >= 0 && index < _currentPlaylist.length) {
      _currentIndex = index;
      _currentPlayingSong = _currentPlaylist[index];
      notifyListeners();
    }
  }

  /// 清空播放列表
  void clearPlaylist() {
    _originalPlaylist.clear();
    _currentPlaylist.clear();
    _playNextQueue.clear();
    _shuffleHistory.clear();
    _currentIndex = 0;
    _currentPlayingSong = null;

    if (kDebugMode) {
      print('EnhancedPlaylistManager: Playlist cleared');
    }

    notifyListeners();
  }
}
