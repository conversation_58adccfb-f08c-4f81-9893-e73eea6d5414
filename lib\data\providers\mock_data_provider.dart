import 'dart:math';
import 'package:uuid/uuid.dart';
import '../models/models.dart';

/// 提供应用开发过程中使用的模拟数据
class MockDataProvider {
  static final MockDataProvider _instance = MockDataProvider._internal();
  
  factory MockDataProvider() {
    return _instance;
  }
  
  MockDataProvider._internal();
  
  final Uuid _uuid = const Uuid();
  final Random _random = Random();
  
  // 用户数据
  User get currentUser => User(
    id: 'user_001',
    username: 'music_lover',
    email: '<EMAIL>',
    nickname: 'Music Lover',
    avatar: 'https://picsum.photos/200',
    bio: 'Music is life, the rest is just details.',
    isPremium: true,
    phoneNumber: '123456789',
    gender: 'Male',
    birthday: DateTime(1995, 5, 15),
    createdAt: DateTime(2022, 1, 1),
    followingIds: List.generate(10, (index) => 'user_${100 + index}'),
    followerIds: List.generate(15, (index) => 'user_${200 + index}'),
    favoriteSongIds: List.generate(20, (index) => 'song_${index + 1}'),
    favoritePlaylistIds: List.generate(5, (index) => 'playlist_${index + 1}'),
  );
  
  // 歌曲数据
  List<Song> get songs => List.generate(
    50,
    (index) => Song(
      id: 'song_${index + 1}',
      title: _generateSongTitle(),
      artistId: 'artist_${_random.nextInt(20) + 1}',
      artistName: _generateArtistName(),
      albumId: 'album_${_random.nextInt(10) + 1}',
      albumName: _generateAlbumName(),
      coverUrl: 'https://picsum.photos/300?random=${_random.nextInt(100)}',
      audioUrl: 'https://example.com/audio/song_${index + 1}.mp3',
      duration: _random.nextInt(300) + 120, // 2-7 分钟
      releaseDate: DateTime.now().subtract(Duration(days: _random.nextInt(3650))), // 过去10年内
      playCount: _random.nextInt(10000000),
      likeCount: _random.nextInt(1000000),
      commentCount: _random.nextInt(50000),
      lyrics: _generateLyrics(),
      tags: _generateTags(_random.nextInt(5) + 1),
      genres: _generateGenres(_random.nextInt(3) + 1),
      isLiked: _random.nextBool(),
      quality: SongQuality.values[_random.nextInt(SongQuality.values.length)],
      isFeatured: _random.nextInt(10) > 7, // 30%概率是精选歌曲
      isExplicit: _random.nextInt(10) > 8, // 10%概率是限制级
    ),
  );
  
  // 获取随机歌曲
  Song getRandomSong() {
    return songs[_random.nextInt(songs.length)];
  }
  
  // 获取指定数量的随机歌曲
  List<Song> getRandomSongs(int count) {
    final allSongs = songs;
    allSongs.shuffle();
    return allSongs.take(count).toList();
  }
  
  // 歌单数据
  List<Playlist> get playlists => List.generate(
    20,
    (index) {
      final songCount = _random.nextInt(20) + 5; // 5-25首歌
      return Playlist(
        id: 'playlist_${index + 1}',
        name: _generatePlaylistName(),
        description: _generatePlaylistDescription(),
        coverUrl: 'https://picsum.photos/300?random=${100 + _random.nextInt(100)}',
        creatorId: _random.nextBool() ? 'user_001' : 'user_${_random.nextInt(50) + 100}',
        creatorName: _random.nextBool() ? 'Music Lover' : _generateUserName(),
        createdAt: DateTime.now().subtract(Duration(days: _random.nextInt(365))),
        updatedAt: DateTime.now().subtract(Duration(days: _random.nextInt(30))),
        songs: getRandomSongs(songCount),
        playCount: _random.nextInt(1000000),
        likeCount: _random.nextInt(100000),
        commentCount: _random.nextInt(10000),
        isPublic: _random.nextInt(10) > 2, // 80%概率是公开的
        isOfficial: _random.nextInt(10) > 7, // 30%概率是官方的
        isLiked: _random.nextBool(),
        tags: _generateTags(_random.nextInt(4) + 1),
      );
    },
  );
  
  // 获取推荐歌单
  List<Playlist> getRecommendedPlaylists() {
    final allPlaylists = playlists;
    allPlaylists.shuffle();
    return allPlaylists.take(6).toList();
  }
  
  // 获取每日推荐歌单
  List<Playlist> getDailyRecommendedPlaylists() {
    final allPlaylists = playlists;
    allPlaylists.shuffle();
    return allPlaylists.take(3).toList();
  }
  
  // 获取热门歌单
  List<Playlist> getHotPlaylists() {
    final allPlaylists = List<Playlist>.from(playlists);
    allPlaylists.sort((a, b) => b.playCount.compareTo(a.playCount));
    return allPlaylists.take(10).toList();
  }
  
  // 直播数据
  List<Live> get lives => List.generate(
    15,
    (index) {
      final status = LiveStatus.values[_random.nextInt(LiveStatus.values.length)];
      final scheduledStartTime = DateTime.now().add(Duration(hours: _random.nextInt(48) - 24));
      
      DateTime? actualStartTime;
      DateTime? endTime;
      
      if (status == LiveStatus.live || status == LiveStatus.ended) {
        actualStartTime = scheduledStartTime;
        if (status == LiveStatus.ended) {
          endTime = actualStartTime.add(Duration(minutes: 30 + _random.nextInt(90)));
        }
      }
      
      return Live(
        id: 'live_${index + 1}',
        title: _generateLiveTitle(),
        description: _generateLiveDescription(),
        hostId: 'user_${_random.nextInt(20) + 1}',
        hostName: _generateUserName(),
        hostAvatar: 'https://picsum.photos/200?random=${200 + _random.nextInt(100)}',
        coverUrl: 'https://picsum.photos/400/200?random=${300 + _random.nextInt(100)}',
        streamUrl: 'https://example.com/stream/live_${index + 1}',
        scheduledStartTime: scheduledStartTime,
        actualStartTime: actualStartTime,
        endTime: endTime,
        status: status,
        viewerCount: status == LiveStatus.live ? _random.nextInt(10000) : 0,
        likeCount: (status == LiveStatus.live || status == LiveStatus.ended) ? _random.nextInt(5000) : 0,
        commentCount: (status == LiveStatus.live || status == LiveStatus.ended) ? _random.nextInt(1000) : 0,
        tags: _generateTags(_random.nextInt(3) + 1),
        isFollowing: _random.nextBool(),
        isReminded: _random.nextBool(),
      );
    },
  );
  
  // 获取热门直播
  List<Live> getHotLives() {
    final liveLives = lives.where((live) => live.status == LiveStatus.live).toList();
    liveLives.sort((a, b) => b.viewerCount.compareTo(a.viewerCount));
    return liveLives.take(5).toList();
  }
  
  // 获取即将开始的直播
  List<Live> getUpcomingLives() {
    final upcomingLives = lives.where((live) => live.status == LiveStatus.upcoming).toList();
    upcomingLives.sort((a, b) => a.scheduledStartTime.compareTo(b.scheduledStartTime));
    return upcomingLives.take(5).toList();
  }
  
  // 生成数据的辅助方法
  String _generateSongTitle() {
    final titles = [
      'Lost in the Rhythm', 'Echoes of You', 'Midnight Memories', 'Electric Dreams',
      'Whispers in the Wind', 'Neon Lights', 'Starry Night', 'Forever Young',
      'Heartbeat', 'Ocean Waves', 'Summer Vibes', 'Winter Wonderland',
      'Spring Breeze', 'Autumn Leaves', 'City Lights', 'Mountain High',
      'Desert Rose', 'Jungle Fever', 'Rainbow Colors', 'Silver Lining',
    ];
    return titles[_random.nextInt(titles.length)];
  }
  
  String _generateArtistName() {
    final firstNames = [
      'Alex', 'Taylor', 'Jordan', 'Casey', 'Morgan', 'Riley', 'Avery',
      'Quinn', 'Blake', 'Cameron', 'Dakota', 'Reese', 'Skyler', 'Tatum',
    ];
    final lastNames = [
      'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis',
      'Garcia', 'Wilson', 'Martinez', 'Anderson', 'Taylor', 'Thomas', 'Lee',
    ];
    
    if (_random.nextBool()) {
      // Solo artist
      return '${firstNames[_random.nextInt(firstNames.length)]} ${lastNames[_random.nextInt(lastNames.length)]}';
    } else {
      // Band
      final bandNames = [
        'The ${firstNames[_random.nextInt(firstNames.length)]}s',
        '${firstNames[_random.nextInt(firstNames.length)]} & The ${lastNames[_random.nextInt(lastNames.length)]}',
        'Midnight ${lastNames[_random.nextInt(lastNames.length)]}',
        'Electric ${firstNames[_random.nextInt(firstNames.length)]}',
      ];
      return bandNames[_random.nextInt(bandNames.length)];
    }
  }
  
  String _generateAlbumName() {
    final albums = [
      'Reflections', 'Harmony', 'Spectrum', 'Odyssey', 'Genesis',
      'Evolution', 'Revolution', 'Seasons', 'Elements', 'Horizons',
      'Dimensions', 'Paradigm', 'Euphoria', 'Serenity', 'Infinity',
    ];
    return albums[_random.nextInt(albums.length)];
  }
  
  String _generatePlaylistName() {
    final prefixes = [
      'Best of', 'Top', 'Ultimate', 'Essential', 'Perfect',
      'Pure', 'Classic', 'Modern', 'Today\'s', 'Trending',
    ];
    final genres = [
      'Pop', 'Rock', 'Hip Hop', 'R&B', 'Jazz',
      'Classical', 'Electronic', 'Dance', 'Indie', 'Alternative',
    ];
    final suffixes = [
      'Hits', 'Anthems', 'Collection', 'Mix', 'Compilation',
      'Selection', 'Favorites', 'Essentials', 'Playlist', 'Chart',
    ];
    
    final nameType = _random.nextInt(4);
    switch (nameType) {
      case 0:
        return '${prefixes[_random.nextInt(prefixes.length)]} ${genres[_random.nextInt(genres.length)]}';
      case 1:
        return '${genres[_random.nextInt(genres.length)]} ${suffixes[_random.nextInt(suffixes.length)]}';
      case 2:
        return '${prefixes[_random.nextInt(prefixes.length)]} ${genres[_random.nextInt(genres.length)]} ${suffixes[_random.nextInt(suffixes.length)]}';
      case 3:
        final moods = [
          'Chill', 'Party', 'Focus', 'Workout', 'Relax',
          'Sleep', 'Study', 'Morning', 'Evening', 'Drive',
        ];
        return '${moods[_random.nextInt(moods.length)]} ${suffixes[_random.nextInt(suffixes.length)]}';
      default:
        return 'My Playlist';
    }
  }
  
  String _generatePlaylistDescription() {
    final descriptions = [
      'A collection of the best tracks to brighten your day.',
      'Perfect for your workout sessions or when you need motivation.',
      'Sit back, relax, and enjoy these soothing melodies.',
      'The ultimate party playlist to get the crowd moving.',
      'Discover new and trending songs from various artists.',
      'A journey through the classics that defined a generation.',
      'Handpicked tracks for your daily commute or road trip.',
      'Focus better with these instrumental and low-vocal tracks.',
      'The perfect soundtrack for your study or work sessions.',
      'Unwind and de-stress with these calming tunes.',
    ];
    return descriptions[_random.nextInt(descriptions.length)];
  }
  
  String _generateLiveTitle() {
    final prefixes = [
      'Live', 'Acoustic', 'Unplugged', 'Concert', 'Session',
      'Performance', 'Show', 'Gig', 'Tour', 'Festival',
    ];
    final locations = [
      'Studio', 'Home', 'Backyard', 'Basement', 'Rooftop',
      'Garden', 'Beach', 'Park', 'Cafe', 'Club',
    ];
    final suffixes = [
      'Experience', 'Special', 'Event', 'Night', 'Showcase',
      'Exclusive', 'Series', 'Edition', 'Jam', 'Party',
    ];
    
    final titleType = _random.nextInt(3);
    switch (titleType) {
      case 0:
        return '${prefixes[_random.nextInt(prefixes.length)]} from my ${locations[_random.nextInt(locations.length)]}';
      case 1:
        return '${prefixes[_random.nextInt(prefixes.length)]} ${suffixes[_random.nextInt(suffixes.length)]}';
      case 2:
        return 'Sunday ${prefixes[_random.nextInt(prefixes.length)]} ${suffixes[_random.nextInt(suffixes.length)]}';
      default:
        return 'Live Music Session';
    }
  }
  
  String _generateLiveDescription() {
    final descriptions = [
      'Join me for an evening of music and good vibes.',
      'Playing my latest tracks and taking requests!',
      'Acoustic session with some of my favorite covers.',
      'Special performance with surprise guests.',
      'Celebrating the release of my new single/album.',
      'Q&A and music session - ask me anything!',
      'Chill vibes and casual conversation while I play some tunes.',
      'Late night jam session - come hang out!',
      'Practicing some new material, would love your feedback!',
      'Fundraising concert for a cause close to my heart.',
    ];
    return descriptions[_random.nextInt(descriptions.length)];
  }
  
  String _generateLyrics() {
    final verses = [
      'Lost in the moment, found in your eyes\nTime stands still when you\'re by my side\nWords unspoken, feelings so clear\nIn this perfect moment, I\'m glad you\'re here',
      'City lights shine down on empty streets\nMemories echo with each heartbeat\nThinking of you with every breath\nHolding onto moments that have left',
      'Ocean waves crash upon the shore\nLike thoughts of you I can\'t ignore\nSalt in the air, sand between my toes\nWhere this feeling leads, nobody knows',
      'Stars in the sky, guide my way\nThrough the darkness, to a brighter day\nYour love is the compass that points me home\nWith you I\'ll never be alone',
      'Seasons change, people too\nBut my heart still belongs to you\nThrough winter cold and summer heat\nOur love remains, strong and sweet',
    ];
    
    final chorus = [
      'And I\'ll wait for you, until the end of time\nThrough storm and sunshine, you\'ll always be mine\nNo distance too far, no mountain too high\nFor a love like ours, that reaches the sky',
      'Take my hand, let\'s dance through the night\nUnder moonbeams and stars shining bright\nThis moment is ours, this feeling divine\nA perfect memory, frozen in time',
      'Hold on to me, like I hold on to you\nTogether we\'ll make it, whatever we go through\nYour heart next to mine, beating as one\nOur story continues, it\'s only begun',
    ];
    
    return '${verses[_random.nextInt(verses.length)]}\n\n${chorus[_random.nextInt(chorus.length)]}\n\n${verses[_random.nextInt(verses.length)]}';
  }
  
  List<String> _generateTags(int count) {
    final allTags = [
      'pop', 'rock', 'hip-hop', 'rnb', 'jazz', 'classical', 'electronic',
      'dance', 'indie', 'alternative', 'folk', 'country', 'metal', 'blues',
      'reggae', 'soul', 'funk', 'disco', 'house', 'techno', 'trance',
      'ambient', 'chill', 'party', 'workout', 'relax', 'focus', 'sleep',
      'morning', 'evening', 'drive', 'travel', 'beach', 'summer', 'winter',
      'autumn', 'spring', 'happy', 'sad', 'nostalgic', 'romantic', 'epic',
    ];
    
    allTags.shuffle();
    return allTags.take(count).toList();
  }
  
  List<String> _generateGenres(int count) {
    final allGenres = [
      'Pop', 'Rock', 'Hip Hop', 'R&B', 'Jazz', 'Classical', 'Electronic',
      'Dance', 'Indie', 'Alternative', 'Folk', 'Country', 'Metal', 'Blues',
      'Reggae', 'Soul', 'Funk', 'Disco', 'House', 'Techno', 'Trance',
    ];
    
    allGenres.shuffle();
    return allGenres.take(count).toList();
  }
  
  String _generateUserName() {
    final firstNames = [
      'Alex', 'Taylor', 'Jordan', 'Casey', 'Morgan', 'Riley', 'Avery',
      'Quinn', 'Blake', 'Cameron', 'Dakota', 'Reese', 'Skyler', 'Tatum',
    ];
    final lastNames = [
      'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis',
      'Garcia', 'Wilson', 'Martinez', 'Anderson', 'Taylor', 'Thomas', 'Lee',
    ];
    
    return '${firstNames[_random.nextInt(firstNames.length)]} ${lastNames[_random.nextInt(lastNames.length)]}';
  }
} 