package com.muzi.mzmusic.ui.minibar.video;
import java.io.File;
import android.widget.TextView;
import android.content.Context;
import android.util.AttributeSet;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.TypedValue;
import android.content.res.TypedArray;
import android.content.res.AssetManager;
import android.view.ViewDebug.ExportedProperty;
public class BottomTextView
extends TextView {
    private static final String fonts = "fonts";
    private static final String poppins = fonts
      + File.separator + "poppins.ttf";
	public BottomTextView(Context context) {
        super(context);
        initView(context);
    }

    public BottomTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public BottomTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context) {
        AssetManager assets = context.getAssets();
		final Typeface font = Typeface.createFromAsset(assets,poppins);
		this.setTypeface(font);
		this.setFocusable(true);
        this.setMarqueeRepeatLimit(-1);
		this.setPadding(0,dp2px(1.5f),0,0);
		this.setFocusableInTouchMode(true);
		this.setEllipsize(TextUtils.TruncateAt.MARQUEE);
		this.setSingleLine();
    }
	
	public int dp2px(float dpVal) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpVal,
        this.getResources().getDisplayMetrics());
    }
	
	@Override
    @ExportedProperty(category = "focus")
    public boolean isFocused() {
        return true;
    }
}