package com.muzi.mzmusic.ui.minibar;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.widget.RelativeLayout;
import com.muzi.mzmusic.ui.minibar.BottomDelegate;
public class ArcRelativeLayout
extends RelativeLayout {
    private BottomDelegate ArcDelegate;
	public ArcRelativeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        if (ArcDelegate == null) {
            ArcDelegate = new BottomDelegate(this, getContext());
        }
    }

    public ArcRelativeLayout(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        if (ArcDelegate == null) {
            ArcDelegate = new BottomDelegate(this, getContext());
        }
    }

    public ArcRelativeLayout(Context context) {
        super(context);
        if (ArcDelegate == null) {
            ArcDelegate = new BottomDelegate(this, getContext());
        }
    }
	
	public void setRectAdius(float adius) {
	   if (ArcDelegate == null) {
           ArcDelegate = new BottomDelegate(this, getContext());
		   ArcDelegate.setRectAdius(adius);
		   invalidate();
        }
	}

    @Override
    protected void onLayout(boolean changed, int left, int top, int right,int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        int w = getWidth();
        int h = getHeight();
        ArcDelegate.roundRectSet(w, h);
    }

    @Override
    public void draw(Canvas canvas) {
        ArcDelegate.canvasSetLayer(canvas);
        super.draw(canvas);
        canvas.restore();
    }

}