import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'home_page_v3.dart';
import 'search_page.dart';
import '../widgets/original_music_bar.dart';
import '../data/providers/enhanced_mini_player_provider.dart';

class MainPageV3 extends StatefulWidget {
  const MainPageV3({super.key});

  @override
  State<MainPageV3> createState() => _MainPageV3State();
}

class _MainPageV3State extends State<MainPageV3> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const HomePageV3(),
    const Placeholder(), // 直播页
    const Placeholder(), // 娱乐页
    const Placeholder(), // 我的页
  ];



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // 主要内容区域
          Positioned.fill(
            bottom: 56, // 为底部导航栏预留空间
            child: _pages[_currentIndex],
          ),

          // 原版E4A风格底部播放栏 - 悬浮在底部导航栏上方
          Positioned(
            left: 0,
            right: 0,
            bottom: 10, // 紧贴底部导航栏上方（导航栏高度56）
            child: const OriginalMusicBar(),
          ),
        ],
      ),
        bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(
              color: Colors.grey[200]!,
              width: 0.5,
            ),
          ),
        ),
        height: 56,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(
              index: 0,
              iconPath: 'assets/icons/nav_home.png',
              activeIconPath: 'assets/icons/nav_home_active.png',
              label: '首页',
              iconSize: 24,
            ),
            _buildNavItem(
              index: 1,
              iconPath: 'assets/icons/nav_live.png',
              activeIconPath: 'assets/icons/nav_live_active.png',
              label: '直播',
              iconSize: 24,
            ),
            _buildNavItem(
              index: 2,
              iconPath: 'assets/icons/nav_sing.png',
              activeIconPath: 'assets/icons/nav_sing_active.png',
              label: '唱歌',
              iconSize: 24,
            ),
            _buildNavItem(
              index: 3,
              iconPath: 'assets/icons/nav_my.png',
              activeIconPath: 'assets/icons/nav_my_active.png',
              label: '我的',
              iconSize: 24,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildNavItem({
    required int index,
    required String iconPath,
    required String activeIconPath,
    required String label,
    required double iconSize,
  }) {
    final isActive = _currentIndex == index;
    
    // 由于没有实际图标资源，使用系统图标替代
    final IconData icon = index == 0 
        ? Icons.home_outlined
        : index == 1 
            ? Icons.live_tv_outlined
            : index == 2 
                ? Icons.music_note_outlined
                : Icons.person_outline;
    
    final IconData activeIcon = index == 0 
        ? Icons.home
        : index == 1 
            ? Icons.live_tv
            : index == 2 
                ? Icons.music_note
                : Icons.person;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = index;
        });

        // 页面切换逻辑（已移除无效的播放栏状态管理）
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: 70,
        height: 56,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isActive ? activeIcon : icon,
              color: isActive ? const Color(0xFFF8BBE4) : Colors.grey[700],
              size: iconSize,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: isActive ? const Color(0xFFF8BBE4) : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 