import 'package:flutter/material.dart';
import '../models/song_model.dart';

class MiniPlayerProvider extends ChangeNotifier {
  Song? _currentSong;
  bool _isPlaying = false;
  bool _isExpanded = true;
  List<Song> _playlist = [];
  int _currentIndex = 0;

  // Getters
  Song? get currentSong => _currentSong;
  bool get isPlaying => _isPlaying;
  bool get isExpanded => _isExpanded;
  List<Song> get playlist => _playlist;
  int get currentIndex => _currentIndex;
  bool get canSwipe => _playlist.length > 1;
  bool get hasPrevious => _currentIndex > 0;
  bool get hasNext => _currentIndex < _playlist.length - 1;

  // 设置当前歌曲
  void setCurrentSong(Song song, {List<Song>? playlist, int? index}) {
    _currentSong = song;
    
    if (playlist != null) {
      _playlist = playlist;
      _currentIndex = index ?? 0;
    } else {
      // 如果没有提供播放列表，创建一个只包含当前歌曲的列表
      _playlist = [song];
      _currentIndex = 0;
    }
    
    notifyListeners();
  }

  // 播放/暂停
  void togglePlayPause() {
    _isPlaying = !_isPlaying;
    notifyListeners();
  }

  // 播放
  void play() {
    _isPlaying = true;
    notifyListeners();
  }

  // 暂停
  void pause() {
    _isPlaying = false;
    notifyListeners();
  }

  // 下一首
  void playNext() {
    if (hasNext) {
      _currentIndex++;
      _currentSong = _playlist[_currentIndex];
      notifyListeners();
    }
  }

  // 上一首
  void playPrevious() {
    if (hasPrevious) {
      _currentIndex--;
      _currentSong = _playlist[_currentIndex];
      notifyListeners();
    }
  }

  // 播放指定索引的歌曲
  void playAtIndex(int index) {
    if (index >= 0 && index < _playlist.length) {
      _currentIndex = index;
      _currentSong = _playlist[index];
      _isPlaying = true;
      notifyListeners();
    }
  }

  // 设置播放栏展开状态
  void setExpanded(bool expanded) {
    if (_isExpanded != expanded) {
      _isExpanded = expanded;
      notifyListeners();
    }
  }

  // 展开播放栏
  void expand() {
    setExpanded(true);
  }

  // 收缩播放栏
  void collapse() {
    setExpanded(false);
  }

  // 清除当前播放
  void clearCurrentSong() {
    _currentSong = null;
    _isPlaying = false;
    _playlist.clear();
    _currentIndex = 0;
    notifyListeners();
  }

  // 设置播放列表
  void setPlaylist(List<Song> playlist, {int startIndex = 0}) {
    _playlist = playlist;
    _currentIndex = startIndex;
    
    if (playlist.isNotEmpty && startIndex < playlist.length) {
      _currentSong = playlist[startIndex];
    } else {
      _currentSong = null;
    }
    
    notifyListeners();
  }

  // 添加歌曲到播放列表
  void addToPlaylist(Song song) {
    _playlist.add(song);
    
    // 如果当前没有歌曲在播放，设置这首歌为当前歌曲
    if (_currentSong == null) {
      _currentSong = song;
      _currentIndex = _playlist.length - 1;
    }
    
    notifyListeners();
  }

  // 从播放列表移除歌曲
  void removeFromPlaylist(int index) {
    if (index >= 0 && index < _playlist.length) {
      _playlist.removeAt(index);
      
      // 如果移除的是当前播放的歌曲
      if (index == _currentIndex) {
        if (_playlist.isEmpty) {
          _currentSong = null;
          _currentIndex = 0;
          _isPlaying = false;
        } else {
          // 调整当前索引
          if (_currentIndex >= _playlist.length) {
            _currentIndex = _playlist.length - 1;
          }
          _currentSong = _playlist[_currentIndex];
        }
      } else if (index < _currentIndex) {
        // 如果移除的歌曲在当前歌曲之前，调整索引
        _currentIndex--;
      }
      
      notifyListeners();
    }
  }

  // 随机播放
  void shuffle() {
    if (_playlist.length > 1) {
      final currentSong = _currentSong;
      _playlist.shuffle();
      
      // 重新找到当前歌曲的位置
      if (currentSong != null) {
        _currentIndex = _playlist.indexWhere((song) => song.id == currentSong.id);
        if (_currentIndex == -1) {
          _currentIndex = 0;
          _currentSong = _playlist[0];
        }
      }
      
      notifyListeners();
    }
  }

  // 获取歌曲在播放列表中的位置信息
  String getPositionInfo() {
    if (_playlist.isEmpty) return '';
    return '${_currentIndex + 1}/${_playlist.length}';
  }

  // 检查歌曲是否在播放列表中
  bool isInPlaylist(String songId) {
    return _playlist.any((song) => song.id == songId);
  }

  // 获取当前歌曲的播放进度信息（这里只是示例，实际需要与音频播放器集成）
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;
  double get progress => _totalDuration.inMilliseconds > 0 
      ? _currentPosition.inMilliseconds / _totalDuration.inMilliseconds 
      : 0.0;

  void updatePosition(Duration position) {
    _currentPosition = position;
    notifyListeners();
  }

  void updateDuration(Duration duration) {
    _totalDuration = duration;
    notifyListeners();
  }

  void seekTo(Duration position) {
    _currentPosition = position;
    // 这里应该调用实际的音频播放器的seek方法
    notifyListeners();
  }
}
