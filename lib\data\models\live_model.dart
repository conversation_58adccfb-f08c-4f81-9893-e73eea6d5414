import 'package:equatable/equatable.dart';
import 'user_model.dart';

enum LiveStatus {
  upcoming, // 即将开始
  live, // 直播中
  ended, // 已结束
  canceled, // 已取消
}

class Live extends Equatable {
  final String id;
  final String title;
  final String? description;
  final String hostId;
  final String hostName;
  final String? hostAvatar;
  final String? coverUrl;
  final String? streamUrl;
  final DateTime scheduledStartTime;
  final DateTime? actualStartTime;
  final DateTime? endTime;
  final LiveStatus status;
  final int viewerCount;
  final int likeCount;
  final int commentCount;
  final List<String> tags;
  final bool isFollowing;
  final bool isReminded;
  final List<User>? participants;
  
  const Live({
    required this.id,
    required this.title,
    this.description,
    required this.hostId,
    required this.hostName,
    this.hostAvatar,
    this.coverUrl,
    this.streamUrl,
    required this.scheduledStartTime,
    this.actualStartTime,
    this.endTime,
    this.status = LiveStatus.upcoming,
    this.viewerCount = 0,
    this.likeCount = 0,
    this.commentCount = 0,
    this.tags = const [],
    this.isFollowing = false,
    this.isReminded = false,
    this.participants,
  });
  
  Live copyWith({
    String? id,
    String? title,
    String? description,
    String? hostId,
    String? hostName,
    String? hostAvatar,
    String? coverUrl,
    String? streamUrl,
    DateTime? scheduledStartTime,
    DateTime? actualStartTime,
    DateTime? endTime,
    LiveStatus? status,
    int? viewerCount,
    int? likeCount,
    int? commentCount,
    List<String>? tags,
    bool? isFollowing,
    bool? isReminded,
    List<User>? participants,
  }) {
    return Live(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      hostId: hostId ?? this.hostId,
      hostName: hostName ?? this.hostName,
      hostAvatar: hostAvatar ?? this.hostAvatar,
      coverUrl: coverUrl ?? this.coverUrl,
      streamUrl: streamUrl ?? this.streamUrl,
      scheduledStartTime: scheduledStartTime ?? this.scheduledStartTime,
      actualStartTime: actualStartTime ?? this.actualStartTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      viewerCount: viewerCount ?? this.viewerCount,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      tags: tags ?? this.tags,
      isFollowing: isFollowing ?? this.isFollowing,
      isReminded: isReminded ?? this.isReminded,
      participants: participants ?? this.participants,
    );
  }
  
  // 从JSON映射到模型
  factory Live.fromJson(Map<String, dynamic> json) {
    return Live(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      hostId: json['host_id'] as String,
      hostName: json['host_name'] as String,
      hostAvatar: json['host_avatar'] as String?,
      coverUrl: json['cover_url'] as String?,
      streamUrl: json['stream_url'] as String?,
      scheduledStartTime: DateTime.parse(json['scheduled_start_time'] as String),
      actualStartTime: json['actual_start_time'] != null 
          ? DateTime.parse(json['actual_start_time'] as String) 
          : null,
      endTime: json['end_time'] != null 
          ? DateTime.parse(json['end_time'] as String) 
          : null,
      status: LiveStatus.values[json['status'] as int? ?? 0],
      viewerCount: json['viewer_count'] as int? ?? 0,
      likeCount: json['like_count'] as int? ?? 0,
      commentCount: json['comment_count'] as int? ?? 0,
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      isFollowing: json['is_following'] as bool? ?? false,
      isReminded: json['is_reminded'] as bool? ?? false,
      participants: json['participants'] != null
          ? (json['participants'] as List<dynamic>)
              .map((e) => User.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }
  
  // 从模型映射到JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'host_id': hostId,
      'host_name': hostName,
      'host_avatar': hostAvatar,
      'cover_url': coverUrl,
      'stream_url': streamUrl,
      'scheduled_start_time': scheduledStartTime.toIso8601String(),
      'actual_start_time': actualStartTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'status': status.index,
      'viewer_count': viewerCount,
      'like_count': likeCount,
      'comment_count': commentCount,
      'tags': tags,
      'is_following': isFollowing,
      'is_reminded': isReminded,
      'participants': participants?.map((user) => user.toJson()).toList(),
    };
  }
  
  // 直播时长（分钟）
  int? get durationMinutes {
    if (status != LiveStatus.ended || actualStartTime == null || endTime == null) {
      return null;
    }
    
    final difference = endTime!.difference(actualStartTime!);
    return (difference.inSeconds / 60).round();
  }
  
  // 格式化时长
  String? get formattedDuration {
    final minutes = durationMinutes;
    if (minutes == null) return null;
    
    if (minutes < 60) {
      return '$minutes分钟';
    } else {
      final hours = (minutes / 60).floor();
      final remainingMinutes = minutes % 60;
      return '$hours小时${remainingMinutes > 0 ? ' $remainingMinutes分钟' : ''}';
    }
  }
  
  // 格式化观看人数
  String get formattedViewerCount {
    if (viewerCount >= 10000) {
      return '${(viewerCount / 10000).toStringAsFixed(1)}万';
    } else if (viewerCount >= 1000) {
      return '${(viewerCount / 1000).toStringAsFixed(1)}千';
    } else {
      return viewerCount.toString();
    }
  }
  
  // 以下是Equatable所需的方法
  @override
  List<Object?> get props => [
    id,
    title,
    description,
    hostId,
    hostName,
    hostAvatar,
    coverUrl,
    streamUrl,
    scheduledStartTime,
    actualStartTime,
    endTime,
    status,
    viewerCount,
    likeCount,
    commentCount,
    tags,
    isFollowing,
    isReminded,
    participants,
  ];
  
  @override
  bool get stringify => true;
} 