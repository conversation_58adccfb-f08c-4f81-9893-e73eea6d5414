package com.muzi.mzmusic.ui.minibar;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.view.View;
public class BottomDelegate{
    private final RectF roundRect = new RectF();
    private float rect_adius = 60;
    private final Paint maskPaint = new Paint();
    private final Paint zonePaint = new Paint();
    private View mView;
    private Context mContext;

    public BottomDelegate(View view, Context context){
        this.mView = view;
        this.mContext = context;
        init();
    }

    private void init(){
        maskPaint.setAntiAlias(true);
        maskPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        zonePaint.setAntiAlias(true);
        zonePaint.setColor(Color.WHITE);
    }

    public void setRectAdius(float adius) {
        rect_adius = adius;
        if(mView != null){
            mView.invalidate();
        }
    }


    public void roundRectSet(int width,int height){
        roundRect.set(0, 0, width, height);
    }

    public void canvasSetLayer(Canvas canvas){
        canvas.saveLayer(roundRect, zonePaint, Canvas.ALL_SAVE_FLAG);
        canvas.drawRoundRect(roundRect, rect_adius, rect_adius, zonePaint);
        canvas.saveLayer(roundRect, maskPaint, Canvas.ALL_SAVE_FLAG);
    }
}