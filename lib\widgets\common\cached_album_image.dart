import 'dart:typed_data';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../data/services/unified_cache_manager.dart';
import '../../data/services/kuwo_music_service.dart';

/// 统一的专辑封面图片组件
/// 使用统一缓存管理器，避免重复网络请求
/// 替换项目中所有的CachedNetworkImage使用
class CachedAlbumImage extends StatefulWidget {
  // 静态缓存，避免同一图片重复加载
  static final Map<String, Widget> _widgetCache = {};
  final String songId;
  final String? imageUrl;
  final String? fallbackImageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final String? heroTag;
  final int? requestedSize; // 请求的图片尺寸，用于缓存优化

  const CachedAlbumImage({
    super.key,
    required this.songId,
    this.imageUrl,
    this.fallbackImageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
    this.heroTag,
    this.requestedSize,
  });

  @override
  State<CachedAlbumImage> createState() => _CachedAlbumImageState();
}

class _CachedAlbumImageState extends State<CachedAlbumImage> {
  final UnifiedCacheManager _cacheManager = UnifiedCacheManager();
  final KuwoMusicService _musicService = KuwoMusicService();
  Uint8List? _imageData;
  bool _isLoading = true;
  bool _hasError = false;
  StreamSubscription<String>? _cacheUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _loadImage();

    // 监听缓存更新事件
    _cacheUpdateSubscription = UnifiedCacheManager.imageCacheUpdateStream.listen((songId) {
      if (songId == widget.songId && mounted) {
        // 缓存更新，重新加载图片
        _loadImage();
      }
    });
  }

  @override
  void didUpdateWidget(CachedAlbumImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果图片URL或歌曲ID发生变化，重新加载
    if (oldWidget.imageUrl != widget.imageUrl || 
        oldWidget.songId != widget.songId ||
        oldWidget.fallbackImageUrl != widget.fallbackImageUrl) {
      _loadImage();
    }
  }

  @override
  void dispose() {
    _cacheUpdateSubscription?.cancel();
    super.dispose();
  }

  Future<void> _loadImage() async {
    if (!mounted) return;

    // 加载图片

    setState(() {
      _isLoading = true;
      _hasError = false;
      _imageData = null;
    });

    try {
      // 首先检查是否已有缓存（任意尺寸）
      final cachedImage = await _cacheManager.getAlbumCover(
        widget.songId,
        '', // 空URL，只检查缓存
        requestedSize: 500,
        checkCacheOnly: true,
      );

      if (cachedImage != null) {
        // 有缓存，直接使用
        if (mounted) {
          setState(() {
            _imageData = cachedImage;
            _isLoading = false;
            _hasError = false;
          });
        }
        return;
      }

      // 没有缓存，主动获取图片URL并下载
      if (kDebugMode) {
        print('CachedAlbumImage: No cache found for ${widget.songId}, fetching image...');
      }

      // 获取高质量专辑封面URL
      final imageUrl = await _musicService.getHighQualityAlbumCover(widget.songId);
      if (imageUrl != null && mounted) {
        // 下载并缓存图片
        final imageData = await _cacheManager.getAlbumCover(
          widget.songId,
          imageUrl,
          requestedSize: widget.requestedSize ?? 500,
        );

        if (imageData != null && mounted) {
          setState(() {
            _imageData = imageData;
            _isLoading = false;
            _hasError = false;
          });
          return;
        }
      }

      // 如果获取失败，显示错误状态
      if (mounted) {
        setState(() {
          _imageData = null;
          _isLoading = false;
          _hasError = true;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('CachedAlbumImage: Error loading image for ${widget.songId}: $e');
      }
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: widget.borderRadius,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    );
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: widget.borderRadius,
      ),
      child: const Center(
        child: Icon(
          Icons.music_note,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildImageWidget() {
    Widget imageWidget;

    if (_isLoading) {
      imageWidget = widget.placeholder ?? _buildDefaultPlaceholder();
    } else if (_hasError || _imageData == null) {
      imageWidget = widget.errorWidget ?? _buildDefaultErrorWidget();
    } else {
      imageWidget = Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          borderRadius: widget.borderRadius,
        ),
        clipBehavior: widget.borderRadius != null ? Clip.antiAlias : Clip.none,
        child: Image.memory(
          _imageData!,
          fit: widget.fit,
          width: widget.width,
          height: widget.height,
          errorBuilder: (context, error, stackTrace) {
            if (kDebugMode) {
              print('CachedAlbumImage: Error displaying image for ${widget.songId}: $error');
            }
            return widget.errorWidget ?? _buildDefaultErrorWidget();
          },
        ),
      );
    }

    // 如果有heroTag，包装在Hero中
    if (widget.heroTag != null) {
      return Hero(
        tag: widget.heroTag!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  @override
  Widget build(BuildContext context) {
    return _buildImageWidget();
  }
}

/// 专辑封面预加载器
/// 用于在后台预加载即将显示的专辑封面
class AlbumImagePreloader {
  static final UnifiedCacheManager _cacheManager = UnifiedCacheManager();

  /// 预加载专辑封面列表
  static Future<void> preloadAlbumCovers(List<Map<String, dynamic>> imageInfos) async {
    for (final info in imageInfos) {
      final songId = info['songId'] as String?;
      final imageUrl = info['imageUrl'] as String?;
      final size = info['size'] as int? ?? 100;

      if (songId != null && imageUrl != null) {
        _cacheManager.preloadAlbumCover(songId, imageUrl, size: size);

        // 添加小延迟，避免同时发起太多请求
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }
  }

  /// 预加载单个专辑封面
  static Future<void> preloadSingleAlbumCover(String songId, String? imageUrl, {int size = 100}) async {
    if (imageUrl != null && imageUrl.isNotEmpty) {
      await _cacheManager.preloadAlbumCover(songId, imageUrl, size: size);
    }
  }
}

/// 圆形专辑封面组件
class CircularAlbumImage extends StatelessWidget {
  final String songId;
  final String? imageUrl;
  final String? fallbackImageUrl;
  final double size;
  final Widget? placeholder;
  final Widget? errorWidget;
  final String? heroTag;
  final int? requestedSize;

  const CircularAlbumImage({
    super.key,
    required this.songId,
    this.imageUrl,
    this.fallbackImageUrl,
    this.size = 100,
    this.placeholder,
    this.errorWidget,
    this.heroTag,
    this.requestedSize,
  });

  @override
  Widget build(BuildContext context) {
    return ClipOval(
      child: CachedAlbumImage(
        songId: songId,
        imageUrl: imageUrl,
        fallbackImageUrl: fallbackImageUrl,
        width: size,
        height: size,
        fit: BoxFit.cover,
        placeholder: placeholder,
        errorWidget: errorWidget,
        heroTag: heroTag,
        requestedSize: requestedSize ?? size.toInt(),
      ),
    );
  }
}

/// 圆角专辑封面组件
class RoundedAlbumImage extends StatelessWidget {
  final String songId;
  final String? imageUrl;
  final String? fallbackImageUrl;
  final double? width;
  final double? height;
  final double borderRadius;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final String? heroTag;
  final int? requestedSize;

  const RoundedAlbumImage({
    super.key,
    required this.songId,
    this.imageUrl,
    this.fallbackImageUrl,
    this.width,
    this.height,
    this.borderRadius = 8.0,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.heroTag,
    this.requestedSize,
  });

  @override
  Widget build(BuildContext context) {
    return CachedAlbumImage(
      songId: songId,
      imageUrl: imageUrl,
      fallbackImageUrl: fallbackImageUrl,
      width: width,
      height: height,
      fit: fit,
      borderRadius: BorderRadius.circular(borderRadius),
      placeholder: placeholder,
      errorWidget: errorWidget,
      heroTag: heroTag,
      requestedSize: requestedSize,
    );
  }
}
